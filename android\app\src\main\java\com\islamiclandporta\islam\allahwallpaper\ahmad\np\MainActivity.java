package com.islamiclandporta.islam.allahwallpaper.ahmad.np;

import androidx.annotation.NonNull;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import android.os.Bundle;
import android.view.WindowManager;
import android.view.Window;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

public class MainActivity extends FlutterActivity {
    private static final String TAG = "MainActivity";
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            // تنفيذ العمليات الأساسية فقط لتجنب ANR
            super.onCreate(savedInstanceState);

            // إنشاء Handler للخيط الرئيسي
            mainHandler = new Handler(Looper.getMainLooper());

            // تأجيل جميع العمليات الثقيلة إلى ما بعد onCreate
            mainHandler.post(() -> {
                initializeWindowSettings();
            });

            Log.d(TAG, "MainActivity onCreate completed successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate: " + e.getMessage(), e);
            // استمرار التشغيل حتى لو حدث خطأ
        }
    }

    /**
     * تهيئة إعدادات النافذة في خيط منفصل لتجنب ANR
     */
    private void initializeWindowSettings() {
        new Thread(() -> {
            try {
                // تعيين إعدادات النافذة بشكل آمن
                mainHandler.post(() -> {
                    try {
                        if (getWindow() != null) {
                            // تعيين العلم لتجنب تأخير العرض
                            getWindow().setFlags(
                                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
                            );

                            // تعطيل الرسوم المتحركة للانتقال
                            getWindow().setWindowAnimations(0);

                            Log.d(TAG, "Window settings applied successfully");
                        }
                    } catch (Exception e) {
                        Log.w(TAG, "Failed to apply window settings: " + e.getMessage());
                    }
                });

                // تأخير قصير لضمان استقرار النافذة
                Thread.sleep(100);

                // تطبيق إعدادات إضافية
                mainHandler.post(() -> {
                    try {
                        if (getWindow() != null) {
                            // تعيين ميزات النافذة بحذر
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                                getWindow().requestFeature(Window.FEATURE_ACTIVITY_TRANSITIONS);
                                getWindow().requestFeature(Window.FEATURE_CONTENT_TRANSITIONS);
                            }

                            Log.d(TAG, "Additional window features applied");
                        }
                    } catch (Exception e) {
                        Log.w(TAG, "Failed to apply additional window features: " + e.getMessage());
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "Error in initializeWindowSettings: " + e.getMessage(), e);
            }
        }).start();
    }

    @Override
    public void configureFlutterEngine(@NonNull FlutterEngine flutterEngine) {
        super.configureFlutterEngine(flutterEngine);
        // تسجيل قناة المنصة لتعيين الخلفية
        WallpaperMethodChannel.registerWith(flutterEngine, this);
        // تسجيل قناة المنصة لحفظ الصور في المعرض
        GalleryMethodChannel.registerWith(flutterEngine, this);
        // تسجيل قناة المنصة لمسح الملفات
        MediaScannerMethodChannel.registerWith(flutterEngine, this);
    }
}
