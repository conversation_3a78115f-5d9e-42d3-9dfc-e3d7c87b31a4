import 'package:flutter/material.dart';
import '../../../data/services/ad_service.dart';

/// مدير الإعلانات البينية
/// يستخدم لعرض الإعلانات البينية في أماكن محددة من التطبيق
class InterstitialAdManager {
  static final InterstitialAdManager _instance = InterstitialAdManager._internal();
  final AdService _adService = AdService();
  
  // متغيرات للتحكم في عرض الإعلانات
  bool _isInitialized = false;
  int _screenChangeCounter = 0;
  
  // الحصول على نسخة واحدة من المدير (Singleton)
  factory InterstitialAdManager() {
    return _instance;
  }
  
  InterstitialAdManager._internal();
  
  /// تهيئة مدير الإعلانات البينية
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // تهيئة خدمة الإعلانات
      await _adService.initialize();
      
      // تحميل إعلان بيني مسبقًا
      await _adService.loadInterstitialAd();
      
      _isInitialized = true;
      debugPrint('✅ تم تهيئة مدير الإعلانات البينية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مدير الإعلانات البينية: $e');
    }
  }
  
  /// عرض إعلان بيني عند تغيير الشاشة
  /// يتم استدعاء هذه الدالة عند الانتقال بين الشاشات
  Future<void> showAdOnScreenChange() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    // زيادة العداد
    _screenChangeCounter++;
    
    // عرض الإعلان كل 4 مرات فقط
    if (_screenChangeCounter % 4 == 0) {
      await _adService.showInterstitialAd();
    }
  }
  
  /// عرض إعلان بيني بشكل إجباري
  /// يتم استدعاء هذه الدالة عند الحاجة لعرض إعلان بشكل إجباري
  Future<bool> showAdForced() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    return await _adService.showInterstitialAd();
  }
}
