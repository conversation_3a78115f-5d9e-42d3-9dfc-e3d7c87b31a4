import 'dart:io';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة تحديد الموقع الجغرافي البسيطة
/// تستخدم لتحديد ما إذا كان المستخدم في الاتحاد الأوروبي أم لا
class GeoService {
  static const String _euDetectionKey = 'eu_detection_result';
  static const String _lastCheckTimeKey = 'eu_detection_last_check';

  // قائمة رموز الدول الأعضاء في الاتحاد الأوروبي
  static const List<String> _euCountryCodes = [
    'AT', // النمسا
    'BE', // بلجيكا
    'BG', // بلغاريا
    'HR', // كرواتيا
    'CY', // قبرص
    'CZ', // جمهورية التشيك
    'DK', // الدنمارك
    'EE', // إستونيا
    'FI', // فنلندا
    'FR', // فرنسا
    'DE', // ألمانيا
    'GR', // اليونان
    'HU', // المجر
    'IE', // أيرلندا
    'IT', // إيطاليا
    'LV', // لاتفيا
    'LT', // ليتوانيا
    'LU', // لوكسمبورغ
    'MT', // مالطا
    'NL', // هولندا
    'PL', // بولندا
    'PT', // البرتغال
    'RO', // رومانيا
    'SK', // سلوفاكيا
    'SI', // سلوفينيا
    'ES', // إسبانيا
    'SE', // السويد
  ];

  // التحقق مما إذا كان المستخدم في الاتحاد الأوروبي
  static Future<bool> isInEuropeanUnion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // التحقق من وجود نتيجة محفوظة
      if (prefs.containsKey(_euDetectionKey)) {
        final lastCheckTime = DateTime.fromMillisecondsSinceEpoch(
          prefs.getInt(_lastCheckTimeKey) ?? 0,
        );
        
        // إذا كان آخر تحقق منذ أقل من أسبوع، استخدم النتيجة المحفوظة
        if (DateTime.now().difference(lastCheckTime).inDays < 7) {
          return prefs.getBool(_euDetectionKey) ?? false;
        }
      }
      
      // الحصول على رمز البلد من إعدادات الجهاز
      final String countryCode = _getDeviceCountryCode();
      
      // التحقق مما إذا كان رمز البلد في قائمة دول الاتحاد الأوروبي
      final bool isInEU = _euCountryCodes.contains(countryCode);
      
      // حفظ النتيجة ووقت التحقق
      await prefs.setBool(_euDetectionKey, isInEU);
      await prefs.setInt(_lastCheckTimeKey, DateTime.now().millisecondsSinceEpoch);
      
      debugPrint('✅ تم تحديد الموقع: ${isInEU ? 'في الاتحاد الأوروبي' : 'خارج الاتحاد الأوروبي'} (${countryCode})');
      
      return isInEU;
    } catch (e) {
      debugPrint('❌ خطأ في تحديد الموقع: $e');
      return false;
    }
  }

  // الحصول على رمز البلد من إعدادات الجهاز
  static String _getDeviceCountryCode() {
    try {
      // محاولة الحصول على رمز البلد من إعدادات اللغة
      final String locale = Platform.localeName;
      
      // استخراج رمز البلد من الإعدادات المحلية (مثل en_US أو fr_FR)
      if (locale.contains('_') && locale.split('_').length > 1) {
        return locale.split('_')[1].toUpperCase();
      }
      
      // إذا لم يتم العثور على رمز البلد، افترض أن المستخدم خارج الاتحاد الأوروبي
      return 'UNKNOWN';
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على رمز البلد: $e');
      return 'UNKNOWN';
    }
  }
}
