import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;

/// خدمة تعيين الخلفية باستخدام قناة المنصة
/// تستخدم للتواصل مع كود Java/Kotlin الأصلي لتعيين الخلفية مباشرة
class WallpaperPlatformService {
  static const MethodChannel _channel = MethodChannel(
    'com.islamiclandporta.islam.allahwallpaper.ahmad.np/wallpaper',
  );

  /// تهيئة الخدمة
  static Future<void> init() async {
    try {
      await _channel.invokeMethod('initialize');
    } catch (e) {
      debugPrint('فشل تهيئة خدمة الخلفية: $e');
    }
  }

  /// تعيين خلفية الشاشة الرئيسية
  static Future<bool> setHomeScreenWallpaper(String imageUrl) async {
    try {
      // تحميل الصورة أولاً
      final bytes = await _downloadImageBytes(imageUrl);
      if (bytes == null) {
        return false;
      }

      // استدعاء الوظيفة الأصلية
      final result = await _channel.invokeMethod('setHomeScreenWallpaper', {
        'imageBytes': bytes,
      });

      return result == true;
    } catch (e) {
      debugPrint('فشل تعيين خلفية الشاشة الرئيسية: $e');
      return false;
    }
  }

  /// تعيين خلفية شاشة القفل
  static Future<bool> setLockScreenWallpaper(String imageUrl) async {
    try {
      // تحميل الصورة أولاً
      final bytes = await _downloadImageBytes(imageUrl);
      if (bytes == null) {
        return false;
      }

      // استدعاء الوظيفة الأصلية
      final result = await _channel.invokeMethod('setLockScreenWallpaper', {
        'imageBytes': bytes,
      });

      return result == true;
    } catch (e) {
      debugPrint('فشل تعيين خلفية شاشة القفل: $e');
      return false;
    }
  }

  /// تعيين خلفية الشاشة الرئيسية وشاشة القفل معًا
  static Future<bool> setBothScreensWallpaper(String imageUrl) async {
    try {
      // تحميل الصورة أولاً
      final bytes = await _downloadImageBytes(imageUrl);
      if (bytes == null) {
        return false;
      }

      // استدعاء الوظيفة الأصلية
      final result = await _channel.invokeMethod('setBothScreensWallpaper', {
        'imageBytes': bytes,
      });

      return result == true;
    } catch (e) {
      debugPrint('فشل تعيين خلفية الشاشتين: $e');
      return false;
    }
  }

  /// تحميل الصورة وتحويلها إلى مصفوفة بايت
  static Future<Uint8List?> _downloadImageBytes(String imageUrl) async {
    try {
      // تحميل الصورة من الإنترنت
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode != 200) {
        debugPrint('فشل تحميل الصورة: ${response.statusCode}');
        return null;
      }

      return response.bodyBytes;
    } catch (e) {
      debugPrint('فشل تحميل الصورة: $e');
      return null;
    }
  }
}
