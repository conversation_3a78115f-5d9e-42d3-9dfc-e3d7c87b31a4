import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../design_system/colors.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:url_launcher/url_launcher.dart';

/// صفحة عن التطبيق
class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: Safe<PERSON>rea(
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // رأس الصفحة
              SliverAppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: IconButton(
                  icon: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.black.with<PERSON><PERSON><PERSON>(50),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(Icons.arrow_back, color: Colors.white),
                  ),
                  onPressed: () => context.pop(),
                ),
                expandedHeight: 200,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  background: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          AppColors.primary.withAlpha(100),
                          Colors.transparent,
                        ],
                      ),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // شعار التطبيق
                          Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withAlpha(50),
                                      blurRadius: 10,
                                      spreadRadius: 1,
                                    ),
                                  ],
                                ),
                                child: const Icon(
                                  Icons.wallpaper,
                                  color: AppColors.primary,
                                  size: 40,
                                ),
                              )
                              .animate()
                              .fadeIn(duration: 600.ms)
                              .slideY(
                                begin: 0.3,
                                end: 0,
                                curve: Curves.easeOutBack,
                                duration: 600.ms,
                              ),
                          const SizedBox(height: 16),

                          // اسم التطبيق
                          const Text(
                            'خلفيات إسلامية',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ).animate().fadeIn(delay: 200.ms, duration: 600.ms),

                          // إصدار التطبيق
                          const Text(
                            'الإصدار 6.0.0',
                            style: TextStyle(
                              color: Colors.white70,
                              fontSize: 14,
                            ),
                          ).animate().fadeIn(delay: 400.ms, duration: 600.ms),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // محتوى الصفحة
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عن التطبيق
                      _buildSectionTitle('عن التطبيق'),
                      _buildInfoCard(
                        'تطبيق خلفيات إسلامية هو تطبيق يوفر مجموعة متنوعة من الخلفيات الإسلامية عالية الجودة لهاتفك. يمكنك تصفح الخلفيات وتحميلها وتعيينها كخلفية للشاشة الرئيسية أو شاشة القفل أو كليهما.',
                      ),

                      const SizedBox(height: 24),

                      // المميزات
                      _buildSectionTitle('المميزات'),
                      _buildFeatureItem(
                        icon: Icons.wallpaper,
                        title: 'خلفيات عالية الجودة',
                        description:
                            'مجموعة متنوعة من الخلفيات الإسلامية عالية الدقة',
                      ),
                      _buildFeatureItem(
                        icon: Icons.category,
                        title: 'تصنيفات متعددة',
                        description: 'تصفح الخلفيات حسب التصنيف لسهولة الوصول',
                      ),
                      _buildFeatureItem(
                        icon: Icons.favorite,
                        title: 'المفضلة',
                        description:
                            'حفظ الخلفيات المفضلة لديك للوصول إليها بسهولة',
                      ),
                      _buildFeatureItem(
                        icon: Icons.download,
                        title: 'تحميل الخلفيات',
                        description: 'تحميل الخلفيات بسهولة إلى جهازك',
                      ),
                      _buildFeatureItem(
                        icon: Icons.dark_mode,
                        title: 'الوضع الداكن',
                        description:
                            'واجهة مستخدم مريحة للعين في الإضاءة المنخفضة',
                      ),

                      const SizedBox(height: 24),

                      // فريق التطوير
                      _buildSectionTitle('فريق التطوير'),
                      _buildDeveloperCard(
                        name: 'أحمد التعمري',
                        role: 'مطور ومصمم التطبيق',
                        imageUrl: 'https://example.com/team.jpg',
                      ),

                      const SizedBox(height: 24),

                      // التواصل والحقوق
                      _buildContactButtons(),

                      const SizedBox(height: 40),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // عنوان القسم
  Widget _buildSectionTitle(String title) {
    return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: Text(
            title,
            style: const TextStyle(
              color: AppColors.primary,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 600.ms)
        .slideX(begin: -0.1, end: 0, duration: 600.ms);
  }

  // بطاقة معلومات
  Widget _buildInfoCard(String text) {
    return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white.withAlpha(20), width: 1),
          ),
          child: Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              height: 1.6,
            ),
            textAlign: TextAlign.justify,
          ),
        )
        .animate()
        .fadeIn(duration: 800.ms)
        .scale(
          begin: const Offset(0.95, 0.95),
          end: const Offset(1, 1),
          duration: 800.ms,
        );
  }

  // عنصر ميزة
  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.white.withAlpha(20), width: 1),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.primary.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: AppColors.primary, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        )
        .animate()
        .fadeIn(duration: 800.ms)
        .slideX(begin: 0.05, end: 0, duration: 800.ms);
  }

  // بطاقة المطور
  Widget _buildDeveloperCard({
    required String name,
    required String role,
    required String imageUrl,
  }) {
    return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.surface.withAlpha(220),
                AppColors.surface.withAlpha(180),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: AppColors.primary.withAlpha(40),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(40),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // صورة المطور
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      AppColors.primary.withAlpha(50),
                      AppColors.primary.withAlpha(30),
                    ],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primary.withAlpha(30),
                      blurRadius: 10,
                      spreadRadius: 1,
                    ),
                  ],
                  border: Border.all(
                    color: AppColors.primary.withAlpha(60),
                    width: 2,
                  ),
                ),
                child: const Icon(
                  Icons.person,
                  color: AppColors.primary,
                  size: 40,
                ),
              ),
              const SizedBox(width: 20),

              // معلومات المطور
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم المطور
                    Text(
                      name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                    const SizedBox(height: 6),

                    // دور المطور
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.primary.withAlpha(30),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: AppColors.primary.withAlpha(50),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        role,
                        style: TextStyle(
                          color: AppColors.primary.withAlpha(220),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(height: 10),

                    // وصف إضافي
                    Text(
                      'مطور تطبيقات متخصص في تطوير تطبيقات الموبايل',
                      style: TextStyle(
                        color: Colors.white.withAlpha(180),
                        fontSize: 12,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        )
        .animate()
        .fadeIn(duration: 800.ms)
        .scale(
          begin: const Offset(0.95, 0.95),
          end: const Offset(1, 1),
          duration: 800.ms,
        );
  }

  // أزرار التواصل
  Widget _buildContactButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(77), // 0.3 * 255 = 77
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withAlpha(26),
          width: 1,
        ), // 0.1 * 255 = 26
      ),
      child: Column(
        children: [
          // عنوان القسم
          Padding(
            padding: const EdgeInsets.only(top: 8, bottom: 16),
            child: Text(
              'تواصل معنا',
              style: TextStyle(
                color: AppColors.primary,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          // الأزرار
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildContactButton(
                icon: Icons.facebook,
                label: 'صفحة فيسبوك',
                onTap:
                    () => _launchUrl(
                      'https://www.facebook.com/share/16XbtVRXxG/',
                    ),
              ),
              _buildContactButton(
                icon: Icons.email,
                label: 'راسلنا',
                onTap: () => _launchEmail('<EMAIL>'),
              ),
              _buildContactButton(
                icon: Icons.support_agent,
                label: 'الدعم الفني',
                onTap: () => _launchEmail('<EMAIL>'),
              ),
            ],
          ),
          // حقوق النشر
          Padding(
            padding: const EdgeInsets.only(top: 16, bottom: 8),
            child: Text(
              'جميع الحقوق محفوظة © 2025',
              style: TextStyle(
                color: Colors.white.withAlpha(128), // 0.5 * 255 = 128
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 800.ms);
  }

  // زر التواصل
  Widget _buildContactButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: 100,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(51), // 0.2 * 255 = 51
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // أيقونة
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(51), // 0.2 * 255 = 51
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: AppColors.primary, size: 20),
            ),
            const SizedBox(height: 8),
            // نص الزر
            Text(
              label,
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // فتح رابط
  Future<void> _launchUrl(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        // استخدام وضع التطبيق الخارجي لتجنب مشكلة الشاشة السوداء
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        debugPrint('✅ تم فتح الرابط بنجاح: $url');
      } else {
        debugPrint('❌ لا يمكن فتح الرابط: $url');
      }
    } catch (e) {
      debugPrint('❌ خطأ في فتح الرابط: $e');
    }
  }

  // فتح البريد الإلكتروني
  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=استفسار من تطبيق خلفيات إسلامية',
    );

    try {
      await launchUrl(emailUri);
      debugPrint('✅ تم فتح البريد الإلكتروني بنجاح: $email');
    } catch (e) {
      debugPrint('❌ لا يمكن فتح تطبيق البريد الإلكتروني: $e');
    }
  }
}
