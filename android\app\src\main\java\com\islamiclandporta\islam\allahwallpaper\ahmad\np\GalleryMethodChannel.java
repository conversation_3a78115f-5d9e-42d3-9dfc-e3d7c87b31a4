package com.islamiclandporta.islam.allahwallpaper.ahmad.np;

import android.content.ContentResolver;
import android.content.ContentValues;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.MediaScannerConnection;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class GalleryMethodChannel implements MethodChannel.MethodCallHandler {
    private static final String TAG = "GalleryMethodChannel";
    private static final String CHANNEL = "com.islamiclandporta.islam.allahwallpaper.ahmad.np/gallery";
    private final Context context;

    public GalleryMethodChannel(Context context) {
        this.context = context;
    }

    public static void registerWith(FlutterEngine flutterEngine, Context context) {
        MethodChannel channel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL);
        GalleryMethodChannel instance = new GalleryMethodChannel(context);
        channel.setMethodCallHandler(instance);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        // التحقق من صحة المعاملات لتجنب IllegalArgumentException
        if (call == null || result == null) {
            Log.e(TAG, "MethodCall أو Result null في GalleryMethodChannel");
            return;
        }

        // تنفيذ العمليات في خيط منفصل لتجنب ANR
        new Thread(() -> {
            android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());

            try {
                String method = call.method;
                if (method == null) {
                    mainHandler.post(() -> {
                        try {
                            result.error("INVALID_METHOD", "اسم الطريقة مفقود", null);
                        } catch (Exception e) {
                            Log.e(TAG, "خطأ في إرجاع خطأ الطريقة المفقودة: " + e.getMessage());
                        }
                    });
                    return;
                }

                switch (method) {
                    case "saveImageToGallery":
                        try {
                            // التحقق من صحة البيانات مع معالجة أفضل للأخطاء
                            Object imageBytesObj = call.argument("imageBytes");
                            Object titleObj = call.argument("title");
                            Object albumNameObj = call.argument("albumName");

                            if (imageBytesObj == null) {
                                mainHandler.post(() -> {
                                    try {
                                        result.error("INVALID_ARGUMENT", "بيانات الصورة مفقودة", null);
                                    } catch (Exception e) {
                                        Log.e(TAG, "خطأ في إرجاع خطأ البيانات المفقودة: " + e.getMessage());
                                    }
                                });
                                return;
                            }

                            byte[] imageBytes = (byte[]) imageBytesObj;
                            String title = titleObj != null ? titleObj.toString() : "Islamic_Wallpaper";
                            String albumName = albumNameObj != null ? albumNameObj.toString() : "Islamic Wallpapers";

                            if (imageBytes.length == 0) {
                                mainHandler.post(() -> {
                                    try {
                                        result.error("INVALID_ARGUMENT", "بيانات الصورة فارغة", null);
                                    } catch (Exception e) {
                                        Log.e(TAG, "خطأ في إرجاع خطأ البيانات الفارغة: " + e.getMessage());
                                    }
                                });
                                return;
                            }

                            boolean success = saveImageToGallery(imageBytes, title, albumName);
                            mainHandler.post(() -> {
                                try {
                                    result.success(success);
                                } catch (Exception e) {
                                    Log.e(TAG, "خطأ في إرجاع نتيجة الحفظ: " + e.getMessage());
                                }
                            });

                        } catch (ClassCastException e) {
                            Log.e(TAG, "خطأ في تحويل بيانات الصورة: " + e.getMessage());
                            mainHandler.post(() -> {
                                try {
                                    result.error("TYPE_ERROR", "نوع بيانات الصورة غير صحيح", null);
                                } catch (Exception resultError) {
                                    Log.e(TAG, "خطأ في إرجاع خطأ النوع: " + resultError.getMessage());
                                }
                            });
                        }
                        break;

                    default:
                        mainHandler.post(() -> {
                            try {
                                result.notImplemented();
                            } catch (Exception e) {
                                Log.e(TAG, "خطأ في إرجاع notImplemented: " + e.getMessage());
                            }
                        });
                        break;
                }

            } catch (Exception e) {
                Log.e(TAG, "خطأ في معالجة استدعاء الطريقة: " + e.getMessage(), e);
                mainHandler.post(() -> {
                    try {
                        result.error("EXECUTION_ERROR", "خطأ في تنفيذ العملية: " + e.getMessage(), null);
                    } catch (Exception resultError) {
                        Log.e(TAG, "خطأ في إرجاع نتيجة الخطأ: " + resultError.getMessage());
                    }
                });
            }
        }).start();
    }

    private boolean saveImageToGallery(byte[] imageBytes, String title, String albumName) {
        if (imageBytes == null) {
            showToast("خطأ: بيانات الصورة فارغة");
            return false;
        }

        Bitmap bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
        if (bitmap == null) {
            showToast("خطأ: فشل إنشاء الصورة");
            return false;
        }

        // استخدام MediaStore API للأندرويد 10 وما فوق
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            return saveImageWithMediaStore(bitmap, title, albumName);
        } else {
            // للإصدارات القديمة
            return saveImageToLegacyStorage(bitmap, title, albumName);
        }
    }

    private boolean saveImageWithMediaStore(Bitmap bitmap, String title, String albumName) {
        ContentResolver resolver = context.getContentResolver();
        ContentValues contentValues = new ContentValues();
        contentValues.put(MediaStore.MediaColumns.DISPLAY_NAME, title + "_" + System.currentTimeMillis() + ".jpg");
        contentValues.put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg");
        contentValues.put(MediaStore.MediaColumns.RELATIVE_PATH, Environment.DIRECTORY_DCIM + File.separator + albumName);

        Uri imageUri = null;
        try {
            imageUri = resolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValues);
            if (imageUri == null) {
                showToast("خطأ: فشل إنشاء URI للصورة");
                return false;
            }

            try (OutputStream outputStream = resolver.openOutputStream(imageUri)) {
                if (outputStream == null) {
                    showToast("خطأ: فشل فتح مجرى الكتابة");
                    return false;
                }
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
            }

            showToast("تم حفظ الصورة في الاستديو بنجاح");
            return true;
        } catch (IOException e) {
            Log.e(TAG, "خطأ في حفظ الصورة: " + e.getMessage());
            if (imageUri != null) {
                resolver.delete(imageUri, null, null);
            }
            showToast("خطأ في حفظ الصورة: " + e.getMessage());
            return false;
        }
    }

    private boolean saveImageToLegacyStorage(Bitmap bitmap, String title, String albumName) {
        File directory = new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DCIM), albumName);
        if (!directory.exists()) {
            if (!directory.mkdirs()) {
                showToast("خطأ: فشل إنشاء المجلد");
                return false;
            }
        }

        String fileName = title + "_" + System.currentTimeMillis() + ".jpg";
        File file = new File(directory, fileName);

        try (FileOutputStream outputStream = new FileOutputStream(file)) {
            bitmap.compress(Bitmap.CompressFormat.JPEG, 100, outputStream);
            // مسح الملف لإضافته إلى معرض الصور
            MediaScannerConnection.scanFile(
                    context,
                    new String[]{file.getAbsolutePath()},
                    new String[]{"image/jpeg"},
                    null
            );
            showToast("تم حفظ الصورة في الاستديو بنجاح");
            return true;
        } catch (IOException e) {
            Log.e(TAG, "خطأ في حفظ الصورة: " + e.getMessage());
            showToast("خطأ في حفظ الصورة: " + e.getMessage());
            return false;
        }
    }

    private void showToast(String message) {
        Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
    }
}
