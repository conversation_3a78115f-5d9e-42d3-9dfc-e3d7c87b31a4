import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';

/// مساعد متقدم لتسجيل الأحداث في التطبيق
class Logger {
  static final Logger _instance = Logger._internal();

  factory Logger() => _instance;

  Logger._internal();

  // سجل الأحداث في الذاكرة
  final List<LogEntry> _inMemoryLogs = [];

  // الحد الأقصى لعدد الأحداث في الذاكرة
  static const int _maxInMemoryLogs = 1000;

  // حفظ السجلات في الملف
  static bool _saveToFile = false;

  /// تمكين حفظ السجلات في ملف
  static void enableFileLogging(bool enable) {
    _saveToFile = enable;
  }
  /// تسجيل رسالة في وحدة التحكم
  static void log(String message) {
    final entry = LogEntry(
      level: LogLevel.debug,
      message: message,
      timestamp: DateTime.now(),
    );

    _instance._addEntry(entry);
    if (kDebugMode) {
      debugPrint('${entry.formattedTimestamp} ${entry.levelTag} $message');
    }
  }
  /// تسجيل خطأ في وحدة التحكم
  static void error(String message, [dynamic error, StackTrace? stackTrace]) {
    final errorDetails = error != null ? ' - $error' : '';
    final entry = LogEntry(
      level: LogLevel.error,
      message: '$message$errorDetails',
      timestamp: DateTime.now(),
      stackTrace: stackTrace?.toString(),
    );

    _instance._addEntry(entry);
    if (kDebugMode) {
      debugPrint('${entry.formattedTimestamp} ❌ $message$errorDetails');
      if (stackTrace != null) {
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }

  /// تسجيل تحذير في وحدة التحكم
  static void warning(String message) {
    final entry = LogEntry(
      level: LogLevel.warning,
      message: message,
      timestamp: DateTime.now(),
    );

    _instance._addEntry(entry);
    if (kDebugMode) {
      debugPrint('${entry.formattedTimestamp} ⚠️ $message');
    }
  }

  /// تسجيل معلومة في وحدة التحكم
  static void info(String message) {
    final entry = LogEntry(
      level: LogLevel.info,
      message: message,
      timestamp: DateTime.now(),
    );

    _instance._addEntry(entry);

    if (kDebugMode) {
      debugPrint('${entry.formattedTimestamp} ℹ️ $message');
    }
  }

  /// تسجيل نجاح في وحدة التحكم
  static void success(String message) {
    final entry = LogEntry(
      level: LogLevel.success,
      message: message,
      timestamp: DateTime.now(),
    );

    _instance._addEntry(entry);

    if (kDebugMode) {
      debugPrint('${entry.formattedTimestamp} ✅ $message');
    }
  }

  /// إضافة سجل جديد
  void _addEntry(LogEntry entry) {
    _inMemoryLogs.add(entry);

    // التأكد من عدم تجاوز الحد الأقصى للسجلات في الذاكرة
    if (_inMemoryLogs.length > _maxInMemoryLogs) {
      _inMemoryLogs.removeAt(0);
    }

    // حفظ السجل في ملف إذا كان مفعل
    if (_saveToFile) {
      _saveLogToFile(entry);
    }
  }

  /// الحصول على السجلات الحالية
  static List<LogEntry> getLogs() {
    return List.from(_instance._inMemoryLogs);
  }

  /// الحصول على السجلات بمستوى معين
  static List<LogEntry> getLogsByLevel(LogLevel level) {
    return _instance._inMemoryLogs.where((log) => log.level == level).toList();
  }

  /// حفظ السجل في ملف
  Future<void> _saveLogToFile(LogEntry entry) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logFile = File('${directory.path}/app_logs.txt');

      // إنشاء الملف إذا لم يكن موجودًا
      if (!await logFile.exists()) {
        await logFile.create();
      }

      // إضافة السجل إلى الملف
      await logFile.writeAsString(
        '${entry.formattedTimestamp} ${entry.levelTag} ${entry.message}\n',
        mode: FileMode.append,
      );

      // إضافة تفاصيل الخطأ إذا كانت موجودة
      if (entry.stackTrace != null) {
        await logFile.writeAsString(
          '${entry.formattedTimestamp} STACK: ${entry.stackTrace}\n',
          mode: FileMode.append,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ فشل في حفظ السجل في الملف: $e');
      }
    }
  }

  /// تصدير السجلات إلى ملف JSON
  static Future<String?> exportLogsToJson() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final jsonFile = File('${directory.path}/app_logs.json');

      final jsonData = jsonEncode(_instance._inMemoryLogs);
      await jsonFile.writeAsString(jsonData);

      return jsonFile.path;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ فشل في تصدير السجلات إلى JSON: $e');
      }
      return null;
    }
  }

  /// مسح جميع السجلات
  static void clearLogs() {
    _instance._inMemoryLogs.clear();
  }
}

/// مستويات السجلات
enum LogLevel {
  debug,
  info,
  success,
  warning,
  error,
}

/// نموذج بيانات السجل
class LogEntry {
  final LogLevel level;
  final String message;
  final DateTime timestamp;
  final String? stackTrace;

  LogEntry({
    required this.level,
    required this.message,
    required this.timestamp,
    this.stackTrace,
  });

  /// الحصول على علامة المستوى
  String get levelTag {
    switch (level) {
      case LogLevel.debug:
        return '[DEBUG]';
      case LogLevel.info:
        return '[INFO]';
      case LogLevel.success:
        return '[SUCCESS]';
      case LogLevel.warning:
        return '[WARNING]';
      case LogLevel.error:
        return '[ERROR]';
    }
  }

  /// الحصول على الوقت المنسق
  String get formattedTimestamp {
    return '${timestamp.year}-${_twoDigits(timestamp.month)}-${_twoDigits(timestamp.day)} '
        '${_twoDigits(timestamp.hour)}:${_twoDigits(timestamp.minute)}:${_twoDigits(timestamp.second)}';
  }

  /// تنسيق الرقم ليكون رقمين
  String _twoDigits(int n) {
    if (n >= 10) return '$n';
    return '0$n';
  }

  /// تحويل السجل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'level': level.toString(),
      'message': message,
      'timestamp': timestamp.toIso8601String(),
      'stackTrace': stackTrace,
    };
  }

  /// إنشاء سجل من JSON
  factory LogEntry.fromJson(Map<String, dynamic> json) {
    return LogEntry(
      level: _parseLogLevel(json['level']),
      message: json['message'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
      stackTrace: json['stackTrace'],
    );
  }

  /// تحليل مستوى السجل من النص
  static LogLevel _parseLogLevel(String? levelStr) {
    if (levelStr == null) return LogLevel.debug;

    for (var level in LogLevel.values) {
      if (levelStr.contains(level.toString())) {
        return level;
      }
    }

    return LogLevel.debug;
  }
}
