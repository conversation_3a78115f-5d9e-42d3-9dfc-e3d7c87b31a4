import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// خدمة التحقق من تحديثات التطبيق
/// تم تعديلها لإزالة اعتمادها على Firebase
class UpdateService {
  /// الحصول على معلومات التطبيق الحالي
  Future<String> getCurrentVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      final currentBuildNumber = packageInfo.buildNumber;

      return '$currentVersion+$currentBuildNumber';
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إصدار التطبيق: $e');
      return 'غير معروف';
    }
  }
}
