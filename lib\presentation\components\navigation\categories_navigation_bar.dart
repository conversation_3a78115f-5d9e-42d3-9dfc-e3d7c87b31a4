import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';
import '../effects/glass_container.dart';

/// شريط تصفح الفئات المحسن
class CategoriesNavigationBar extends StatelessWidget {
  final List<CategoryItem> categories;
  final String? selectedCategoryId;
  final Function(String) onCategorySelected;
  final VoidCallback onViewAllPressed;
  final VoidCallback onShowAllWallpapersPressed;

  const CategoriesNavigationBar({
    super.key,
    required this.categories,
    this.selectedCategoryId,
    required this.onCategorySelected,
    required this.onViewAllPressed,
    required this.onShowAllWallpapersPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // شريط الفئات
        Container(
          height: 100,
          margin: const EdgeInsets.only(bottom: 8),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = category.id == selectedCategoryId;
              
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: _buildCategoryItem(category, isSelected, context),
              );
            },
          ),
        ),
        
        // شريط التصفح
        Container(
          height: 50,
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.4),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          ),
          child: Row(
            textDirection: TextDirection.rtl,
            children: [
              // زر عرض جميع الخلفيات
              Expanded(
                child: InkWell(
                  onTap: onShowAllWallpapersPressed,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(AppDimensions.radiusMedium),
                    bottomRight: Radius.circular(AppDimensions.radiusMedium),
                  ),
                  child: Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppColors.accent.withOpacity(0.7),
                          AppColors.accent.withOpacity(0.5),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        topRight: Radius.circular(AppDimensions.radiusMedium),
                        bottomRight: Radius.circular(AppDimensions.radiusMedium),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      textDirection: TextDirection.rtl,
                      children: [
                        const Icon(
                          Icons.grid_view_rounded,
                          color: Colors.white,
                          size: 18,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'جميع الخلفيات',
                          style: AppTypography.labelMedium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              // خط فاصل
              Container(
                width: 1,
                height: 30,
                color: Colors.white.withOpacity(0.2),
              ),
              
              // زر عرض الكل
              Expanded(
                child: InkWell(
                  onTap: onViewAllPressed,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppDimensions.radiusMedium),
                    bottomLeft: Radius.circular(AppDimensions.radiusMedium),
                  ),
                  child: Container(
                    alignment: Alignment.center,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      textDirection: TextDirection.rtl,
                      children: [
                        const Icon(
                          Icons.arrow_back_ios,
                          color: AppColors.accent,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'عرض الكل',
                          style: AppTypography.labelMedium.copyWith(
                            color: AppColors.accent,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryItem(CategoryItem category, bool isSelected, BuildContext context) {
    return GestureDetector(
      onTap: () => onCategorySelected(category.id),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // صورة الفئة
          Container(
            width: 70,
            height: 70,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              border: Border.all(
                color: isSelected ? AppColors.accent : Colors.transparent,
                width: 2,
              ),
              boxShadow: isSelected ? AppShadows.small : null,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium - 2),
              child: Stack(
                children: [
                  // صورة الفئة
                  Image.asset(
                    category.imageAsset,
                    width: 70,
                    height: 70,
                    fit: BoxFit.cover,
                  ),
                  
                  // طبقة تظليل
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.1),
                          Colors.black.withOpacity(0.6),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 4),
          
          // اسم الفئة
          Text(
            category.name,
            style: AppTypography.labelMedium.copyWith(
              color: isSelected ? AppColors.accent : Colors.white,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }
}

/// نموذج بيانات الفئة
class CategoryItem {
  final String id;
  final String name;
  final String imageAsset;

  const CategoryItem({
    required this.id,
    required this.name,
    required this.imageAsset,
  });
}
