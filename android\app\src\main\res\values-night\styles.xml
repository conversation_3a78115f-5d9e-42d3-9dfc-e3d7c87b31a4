<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- ثيم شاشة البداية المحسنة للوضع الداكن -->
    <style name="SplashTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <!-- تعيين صورة خلفية -->
        <item name="android:windowBackground">@drawable/splash_background</item>
        <item name="android:windowDisablePreview">false</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowSplashScreenAnimationDuration">0</item>
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <!-- إضافة أيقونة للشاشة -->
        <item name="android:icon">@mipmap/ic_launcher</item>
    </style>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is on -->
    <style name="LaunchTheme" parent="SplashTheme">
        <!-- استخدام نفس إعدادات SplashTheme -->
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Material.NoActionBar">
        <item name="android:windowBackground">@color/app_background</item>
        <!-- إضافة دعم Material You (Monet) للأجهزة التي تدعمها -->
        <item name="android:windowSplashScreenBackground">@color/app_background</item>
        <item name="android:windowSplashScreenAnimatedIcon">@null</item>
        <item name="android:windowSplashScreenIconBackgroundColor">@color/app_background</item>
        <item name="android:windowSplashScreenBrandingImage">@null</item>
        <item name="android:windowSplashScreenAnimationDuration">0</item>
        <item name="android:enforceNavigationBarContrast">false</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
</resources>
