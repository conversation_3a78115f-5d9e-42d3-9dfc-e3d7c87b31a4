import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';

/// مؤشر المحتوى المميز
class PremiumIndicator extends StatelessWidget {
  final double size;
  final bool showBackground;
  final bool animated;

  const PremiumIndicator({
    super.key,
    this.size = 24,
    this.showBackground = true,
    this.animated = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: showBackground ? Colors.black.withOpacity(0.5) : null,
        boxShadow: showBackground ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 5,
            spreadRadius: 1,
          ),
        ] : null,
      ),
      child: animated ? _buildAnimatedIcon() : _buildStaticIcon(),
    );
  }

  Widget _buildStaticIcon() {
    return Container(
      padding: EdgeInsets.all(size * 0.2),
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [
            Color(0xFFD4AF37),
            Color(0xFFFFD700),
          ],
        ),
      ),
      child: Icon(
        Icons.workspace_premium,
        color: Colors.black,
        size: size * 0.6,
      ),
    );
  }

  Widget _buildAnimatedIcon() {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 0.8, end: 1.0),
      duration: const Duration(seconds: 2),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: Container(
            padding: EdgeInsets.all(size * 0.2),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                colors: [
                  Color(0xFFD4AF37),
                  Color(0xFFFFD700),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFFFFD700).withOpacity(0.5 * value),
                  blurRadius: 10 * value,
                  spreadRadius: 2 * value,
                ),
              ],
            ),
            child: Icon(
              Icons.workspace_premium,
              color: Colors.black,
              size: size * 0.6,
            ),
          ),
        );
      },
    );
  }
}

/// مؤشر المحتوى المميز للخلفيات
class WallpaperPremiumIndicator extends StatelessWidget {
  final bool isPremium;
  final double size;
  final VoidCallback? onTap;

  const WallpaperPremiumIndicator({
    super.key,
    required this.isPremium,
    this.size = 30,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    if (!isPremium) return const SizedBox.shrink();

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.black.withOpacity(0.5),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 5,
              spreadRadius: 1,
            ),
          ],
        ),
        child: TweenAnimationBuilder<double>(
          tween: Tween<double>(begin: 0.8, end: 1.0),
          duration: const Duration(seconds: 2),
          curve: Curves.easeInOut,
          builder: (context, value, child) {
            return Transform.scale(
              scale: value,
              child: Container(
                padding: EdgeInsets.all(size * 0.2),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: const LinearGradient(
                    colors: [
                      Color(0xFFD4AF37),
                      Color(0xFFFFD700),
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFFFFD700).withOpacity(0.5 * value),
                      blurRadius: 10 * value,
                      spreadRadius: 2 * value,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.workspace_premium,
                  color: Colors.black,
                  size: size * 0.6,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
