import 'package:flutter/material.dart';
import 'colors.dart';

/// نظام التدرجات اللونية الموحد للتطبيق
class AppGradients {
  // تدرج أساسي - أزرق
  static const LinearGradient primary = LinearGradient(
    colors: AppColors.primaryGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // تدرج ثانوي - أخضر
  static const LinearGradient secondary = LinearGradient(
    colors: AppColors.secondaryGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // تدرج ذهبي للعناصر المميزة
  static const LinearGradient gold = LinearGradient(
    colors: AppColors.goldGradient,
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // تدرج الخلفية
  static const LinearGradient background = LinearGradient(
    colors: AppColors.backgroundGradient,
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // تدرج شفاف للتراكب على الصور
  static const LinearGradient overlay = LinearGradient(
    colors: [
      Colors.black87,
      Colors.transparent,
    ],
    begin: Alignment.bottomCenter,
    end: Alignment.topCenter,
  );
  
  // تدرج شفاف للتراكب على الصور من الأعلى
  static const LinearGradient overlayTop = LinearGradient(
    colors: [
      Colors.black87,
      Colors.transparent,
    ],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );
  
  // تدرج شفاف للتراكب على الصور من الجانبين
  static const LinearGradient overlayHorizontal = LinearGradient(
    colors: [
      Colors.black87,
      Colors.transparent,
      Colors.transparent,
      Colors.black87,
    ],
    stops: [0.0, 0.3, 0.7, 1.0],
    begin: Alignment.centerLeft,
    end: Alignment.centerRight,
  );
  
  // تدرج زجاجي للعناصر الشفافة
  static LinearGradient glass = LinearGradient(
    colors: [
      Colors.white.withOpacity(0.15),
      Colors.white.withOpacity(0.05),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // تدرج زجاجي داكن للعناصر الشفافة
  static LinearGradient darkGlass = LinearGradient(
    colors: [
      Colors.black.withOpacity(0.5),
      Colors.black.withOpacity(0.3),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // تدرج للفئات المختلفة
  static LinearGradient categoryGradient(Color baseColor) {
    return LinearGradient(
      colors: [
        baseColor.withOpacity(0.8),
        baseColor,
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }
  
  // تدرج للبطاقات
  static const LinearGradient cardGradient = LinearGradient(
    colors: [
      AppColors.surface,
      AppColors.surfaceLight,
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // تدرج للبطاقات المميزة
  static const LinearGradient featuredCardGradient = LinearGradient(
    colors: [
      AppColors.surfaceLight,
      AppColors.card,
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
