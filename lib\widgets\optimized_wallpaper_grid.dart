import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../models/wallpaper_model.dart';
import '../services/cache_service.dart';
import '../utils/error_handler.dart';
import '../constants/app_colors.dart';
import '../constants/app_strings.dart';

class OptimizedWallpaperGrid extends StatelessWidget {
  final List<Wallpaper> wallpapers;
  final List<String> favoriteIds;
  final Function(Wallpaper) onTap;
  final Function(Wallpaper, bool) onFavoriteToggle;
  final bool isLoading;
  final String emptyMessage;
  final bool showFeaturedLabel;

  const OptimizedWallpaperGrid({
    super.key,
    required this.wallpapers,
    required this.favoriteIds,
    required this.onTap,
    required this.onFavoriteToggle,
    this.isLoading = false,
    this.emptyMessage = AppStrings.noWallpapersFound,
    this.showFeaturedLabel = true,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppColors.primaryColor),
      );
    }

    if (wallpapers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.image_not_supported_outlined,
              size: 80,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: const TextStyle(fontSize: 16, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              AppStrings.checkInternetConnection,
              style: const TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () async {
                bool connected = await ErrorHandler.isConnected();
                if (!connected) {
                  if (context.mounted) {
                    ErrorHandler.showNoInternetSnackBar(context);
                  }
                  return;
                }
                // إعادة تحميل البيانات
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(AppStrings.reloadingData),
                      duration: Duration(seconds: 2),
                    ),
                  );
                }
              },
              icon: const Icon(Icons.refresh),
              label: const Text(AppStrings.retry),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                elevation: 2,
              ),
            ),
          ],
        ),
      );
    }

    return MasonryGridView.count(
      crossAxisCount: 2,
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      padding: const EdgeInsets.all(8),
      itemCount: wallpapers.length,
      itemBuilder: (context, index) {
        final wallpaper = wallpapers[index];
        final isFavorite = favoriteIds.contains(wallpaper.id);

        // Eliminamos el Hero tag de aquí para evitar duplicados
        return OptimizedWallpaperItem(
          wallpaper: wallpaper,
          isFavorite: isFavorite,
          onTap: () => onTap(wallpaper),
          onFavoriteToggle: (isFav) => onFavoriteToggle(wallpaper, isFav),
          showFeaturedLabel: showFeaturedLabel && wallpaper.isFeatured,
        );
      },
    );
  }
}

class OptimizedWallpaperItem extends StatelessWidget {
  final Wallpaper wallpaper;
  final bool isFavorite;
  final VoidCallback onTap;
  final Function(bool) onFavoriteToggle;
  final bool showFeaturedLabel;

  const OptimizedWallpaperItem({
    super.key,
    required this.wallpaper,
    required this.isFavorite,
    required this.onTap,
    required this.onFavoriteToggle,
    this.showFeaturedLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      duration: const Duration(milliseconds: 300),
      opacity: 1.0,
      child: GestureDetector(
        onTap: () {
          // إضافة تأثير النقر
          ScaleTransition(
            scale: Tween<double>(
              begin: 0.95,
              end: 1.0,
            ).animate(CurvedAnimation(
              parent: AnimationController(
                duration: const Duration(milliseconds: 200),
                vsync: Navigator.of(context),
              ),
              curve: Curves.easeInOut,
            )),
            child: Container(),
          );
          onTap();
        },
        child: Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          clipBehavior: Clip.antiAlias,
          child: Stack(
          children: [
            // صورة الخلفية
            CachedNetworkImage(
              imageUrl: wallpaper.thumbnailUrl,
              cacheManager: CacheService.customCacheManager,
              placeholder: (context, url) => Container(
                color: Colors.grey[300],
                child: const Center(
                  child: SizedBox(
                    width: 30,
                    height: 30,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppColors.primaryColor,
                      backgroundColor: Colors.white24,
                    ),
                  ),
                ),
              ),
              errorWidget: (context, url, error) => Container(
                color: Colors.grey[300],
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      color: Colors.red,
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      AppStrings.imageLoadError,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              fit: BoxFit.cover,
              height: wallpaper.id.hashCode % 2 == 0 ? 280 : 200,
              width: double.infinity,
              fadeInDuration: const Duration(milliseconds: 400),
              fadeOutDuration: const Duration(milliseconds: 300),
              memCacheWidth: 800,
              memCacheHeight: 1200,
              maxHeightDiskCache: 1600,
              filterQuality: FilterQuality.medium
            ),

            // زر المفضلة
            Positioned(
              top: 8,
              right: 8,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => onFavoriteToggle(!isFavorite),
                  customBorder: const CircleBorder(),
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      isFavorite ? Icons.favorite : Icons.favorite_border,
                      color: isFavorite ? Colors.red : Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),

            // علامة الخلفيات المميزة
            if (showFeaturedLabel)
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.accentColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    AppStrings.featured,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

            // علامة الخلفيات المدفوعة
            if (wallpaper.isPremium)
              Positioned(
                bottom: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: AppColors.premiumColor,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.workspace_premium,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}