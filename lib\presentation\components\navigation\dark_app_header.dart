import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/shadows.dart';
import '../../../data/services/subscription_service.dart';
import '../effects/glass_container.dart';

/// شريط علوي احترافي بتصميم داكن
class DarkAppHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool showBackButton;
  final bool showActions;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final double height;
  final bool isTransparent;
  final Color? backgroundColor;
  final Gradient? backgroundGradient;
  final Widget? leading;
  final bool centerTitle;

  const DarkAppHeader({
    Key? key,
    required this.title,
    this.subtitle,
    this.showBackButton = false,
    this.showActions = true,
    this.onBackPressed,
    this.actions,
    this.height = 70,
    this.isTransparent = false,
    this.backgroundColor,
    this.backgroundGradient,
    this.leading,
    this.centerTitle = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final topPadding = MediaQuery.of(context).padding.top;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      height: height + topPadding,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surface,
        gradient:
            backgroundGradient ??
            LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.surface,
                AppColors.surface.withAlpha(isTransparent ? 0 : 255),
              ],
            ),
        boxShadow:
            isTransparent
                ? null
                : [
                  BoxShadow(
                    color: Colors.black.withAlpha(50),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
      ),
      child: Padding(
        padding: EdgeInsets.only(top: topPadding, left: 16, right: 16),
        child: _buildHeaderContent(context, screenWidth),
      ),
    ).animate().fadeIn(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOut,
    );
  }

  Widget _buildHeaderContent(BuildContext context, double screenWidth) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // زر الرجوع أو أيقونة مخصصة
        if (showBackButton || leading != null) _buildLeadingWidget(context),

        // عنوان الصفحة
        Expanded(child: _buildTitleWidget(centerTitle)),

        // أزرار الإجراءات
        if (showActions) _buildActionsWidget(context),
      ],
    );
  }

  Widget _buildLeadingWidget(BuildContext context) {
    if (leading != null) return leading!;

    return GestureDetector(
      onTap: onBackPressed ?? () => Navigator.of(context).pop(),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.surface.withAlpha(150),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.divider.withAlpha(100), width: 1),
        ),
        child: const Icon(
          Icons.arrow_back_ios_new_rounded,
          color: AppColors.textPrimary,
          size: 18,
        ),
      ),
    ).animate().fadeIn(
      delay: const Duration(milliseconds: 200),
      duration: const Duration(milliseconds: 300),
    );
  }

  Widget _buildTitleWidget(bool center) {
    return Column(
      crossAxisAlignment:
          center ? CrossAxisAlignment.center : CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // العنوان الرئيسي
        Text(
          title,
          style: AppTypography.headingMedium.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
            shadows: AppShadows.textSmall,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          textAlign: center ? TextAlign.center : TextAlign.start,
        ).animate().fadeIn(
          delay: const Duration(milliseconds: 100),
          duration: const Duration(milliseconds: 300),
        ),

        // العنوان الفرعي
        if (subtitle != null) ...[
          const SizedBox(height: 2),
          Text(
            subtitle!,
            style: AppTypography.bodySmall.copyWith(
              color: AppColors.textSecondary,
              shadows: AppShadows.textSmall,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: center ? TextAlign.center : TextAlign.start,
          ).animate().fadeIn(
            delay: const Duration(milliseconds: 200),
            duration: const Duration(milliseconds: 300),
          ),
        ],
      ],
    );
  }

  Widget _buildActionsWidget(BuildContext context) {
    if (actions != null && actions!.isNotEmpty) {
      return Row(
        children:
            actions!.map((action) {
              return Padding(
                padding: const EdgeInsets.only(left: 8),
                child: action,
              );
            }).toList(),
      ).animate().fadeIn(
        delay: const Duration(milliseconds: 300),
        duration: const Duration(milliseconds: 300),
      );
    }

    // أزرار افتراضية
    return Row(
      children: [
        _buildActionButton(
          icon: Icons.search_rounded,
          onTap: () => context.push('/search'),
        ),
        const SizedBox(width: 8),
        _buildActionButton(
          icon: Icons.notifications_none_rounded,
          onTap: () {},
        ),
      ],
    ).animate().fadeIn(
      delay: const Duration(milliseconds: 300),
      duration: const Duration(milliseconds: 300),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
    Color? color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.surface.withAlpha(150),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.divider.withAlpha(100), width: 1),
        ),
        child: Icon(icon, color: color ?? AppColors.textPrimary, size: 20),
      ),
    );
  }
}

/// شريط علوي ثابت عند التمرير
class DarkStickyHeader extends SliverPersistentHeaderDelegate {
  final String title;
  final String subtitle;
  final Color accentColor;
  final double collapsedHeight;
  final double expandedHeight;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final List<Widget>? actions;
  final Widget? background;
  final SubscriptionService _subscriptionService = SubscriptionService();

  DarkStickyHeader({
    required this.title,
    required this.subtitle,
    this.accentColor = AppColors.accent,
    this.collapsedHeight = 70,
    this.expandedHeight = 150,
    this.showBackButton = false,
    this.onBackPressed,
    this.actions,
    this.background,
  });

  // التحقق مما إذا كان المستخدم مشتركًا
  Future<bool> _isUserSubscribed() async {
    try {
      return await _subscriptionService.isSubscribed();
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من حالة الاشتراك: $e');
      return false;
    }
  }

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    final double percentage = shrinkOffset / maxExtent;
    final double opacity = 1 - percentage;
    final double topPadding = MediaQuery.of(context).padding.top;

    return Container(
      height: maxExtent,
      padding: EdgeInsets.only(top: 0), // إزالة الهامش العلوي
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.surface,
            AppColors.surface.withAlpha(percentage > 0.5 ? 255 : 200),
            AppColors.surface.withAlpha(percentage > 0.7 ? 200 : 100),
            AppColors.surface.withAlpha(percentage > 0.9 ? 100 : 0),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // خلفية مخصصة
          if (background != null && opacity > 0.3)
            Opacity(opacity: opacity, child: background),

          // زر الرجوع
          if (showBackButton)
            Positioned(
              top: topPadding + 5, // تعديل الموضع
              left: 16,
              child: _buildBackButton(context, percentage),
            ),

          // عنوان متحرك - تم تعديل الموضع ليكون تحت شريط الإشعارات مباشرة
          Positioned(
            top: topPadding + 5, // تعديل الموضع ليكون تحت شريط الإشعارات مباشرة
            right: lerpDouble(20, 60, percentage)!,
            left: showBackButton ? 60 : 20,
            child: Opacity(
              opacity: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان الرئيسي
                  Text(
                    title,
                    style: AppTypography.headingMedium.copyWith(
                      fontSize: lerpDouble(28, 20, percentage),
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                      shadows: AppShadows.textSmall,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  // العنوان الفرعي
                  if (opacity > 0.3)
                    Opacity(
                      opacity: opacity,
                      child: Text(
                        subtitle,
                        style: AppTypography.bodySmall.copyWith(
                          fontSize: lerpDouble(16, 14, percentage),
                          color: AppColors.textSecondary,
                          shadows: AppShadows.textSmall,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
          ),

          // أزرار الإجراءات (تم إخفاؤها عند وجود زر الاشتراك)
          // if (actions != null)
          //   Positioned(
          //     top: topPadding + 5, // تعديل الموضع
          //     right: 16,
          //     child: Row(children: actions!),
          //   ),

          // زر الاشتراك المميز - يظهر فقط للمستخدمين غير المشتركين
          FutureBuilder<bool>(
            future: _isUserSubscribed(),
            builder: (context, snapshot) {
              // إذا كان المستخدم مشتركًا، لا نعرض زر الاشتراك
              if (snapshot.hasData && snapshot.data == true) {
                return const SizedBox.shrink();
              }

              // تم إزالة زر الاشتراك
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBackButton(BuildContext context, double percentage) {
    return GestureDetector(
      onTap: onBackPressed ?? () => Navigator.of(context).pop(),
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: AppColors.surface.withAlpha(150),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppColors.divider.withAlpha(100), width: 1),
        ),
        child: const Icon(
          Icons.arrow_back_ios_new_rounded,
          color: AppColors.textPrimary,
          size: 18,
        ),
      ),
    );
  }

  @override
  double get maxExtent => expandedHeight;

  @override
  double get minExtent => collapsedHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }

  // Helper function for linear interpolation
  double? lerpDouble(double? a, double? b, double t) {
    if (a == null && b == null) return null;
    a ??= 0.0;
    b ??= 0.0;
    return a + (b - a) * t;
  }
}
