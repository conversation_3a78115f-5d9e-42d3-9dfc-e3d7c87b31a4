import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/gradients.dart';
import '../../../data/models/wallpaper.dart';

/// بطاقة عرض الفئة
class CategoryCard extends StatelessWidget {
  final Category category;
  final double borderRadius;
  final VoidCallback? onTap;
  final double? height;
  final double? width;
  final bool isCircular;

  const CategoryCard({
    super.key,
    required this.category,
    this.borderRadius = AppDimensions.radiusMedium,
    this.onTap,
    this.height,
    this.width,
    this.isCircular = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isCircular) {
      return _buildCircularCard(context);
    } else {
      return _buildRectangularCard(context);
    }
  }

  Widget _buildCircularCard(BuildContext context) {
    final size = width ?? 100.0;

    return GestureDetector(
      onTap:
          onTap ??
          () {
            context.push('/category/${category.id}', extra: category);
          },
      child: Column(
        children: [
          // الصورة الدائرية مع التأثيرات
          Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: (category.color ?? AppColors.primary).withOpacity(0.3),
                  blurRadius: 15,
                  spreadRadius: 2,
                  offset: const Offset(0, 5),
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                // الصورة الدائرية
                ClipRRect(
                  borderRadius: BorderRadius.circular(size / 2),
                  child: Stack(
                    fit: StackFit.expand,
                    children: [
                      // صورة الفئة
                      Hero(
                        tag: 'category_${category.id}',
                        child: _buildCategoryImage(isCircular: true),
                      ),

                      // تراكب لوني متدرج
                      Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withOpacity(0.1),
                              (category.color ?? AppColors.primary).withOpacity(
                                0.4,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // حدود دائرية مضيئة
                Container(
                  width: size,
                  height: size,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: (category.color ?? AppColors.primary).withOpacity(
                        0.7,
                      ),
                      width: 2,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppDimensions.marginMedium),

          // اسم الفئة مع تأثير ظل
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingMedium,
              vertical: AppDimensions.paddingXSmall,
            ),
            decoration: BoxDecoration(
              color: (category.color ?? AppColors.primary).withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              category.name,
              style: AppTypography.titleSmall.copyWith(
                fontWeight: FontWeight.bold,
                color: category.color ?? AppColors.primary,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRectangularCard(BuildContext context) {
    return GestureDetector(
      onTap:
          onTap ??
          () {
            context.push('/category/${category.id}', extra: category);
          },
      child: Container(
        height: height,
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
          boxShadow: [
            BoxShadow(
              color: (category.color ?? AppColors.primary).withAlpha(60),
              blurRadius: 15,
              spreadRadius: 2,
              offset: const Offset(0, 5),
            ),
            BoxShadow(
              color: Colors.black.withAlpha(30),
              blurRadius: 8,
              spreadRadius: 1,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(borderRadius),
          child: Stack(
            fit: StackFit.expand,
            children: [
              // صورة الفئة
              Hero(
                tag: 'category_${category.id}',
                child: _buildCategoryImage(),
              ),

              // تراكب لوني متدرج
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withAlpha(100),
                      Colors.black.withAlpha(180),
                    ],
                    stops: const [0.5, 0.8, 1.0],
                  ),
                ),
              ),

              // تأثير حدود مضيئة
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: (category.color ?? AppColors.primary).withAlpha(150),
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(borderRadius),
                ),
              ),

              // معلومات الفئة
              Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // أيقونة الفئة
                    Container(
                      padding: const EdgeInsets.all(AppDimensions.paddingSmall),
                      decoration: BoxDecoration(
                        color: (category.color ?? AppColors.primary).withAlpha(
                          50,
                        ),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getCategoryIcon(),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),

                    const Spacer(),

                    // اسم الفئة
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingSmall,
                        vertical: AppDimensions.paddingXSmall,
                      ),
                      decoration: BoxDecoration(
                        color: (category.color ?? AppColors.primary).withAlpha(
                          80,
                        ),
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusSmall,
                        ),
                      ),
                      child: Text(
                        category.name,
                        style: AppTypography.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          shadows: [
                            Shadow(
                              color: Colors.black.withAlpha(150),
                              blurRadius: 2,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    const SizedBox(height: AppDimensions.marginXSmall),

                    // عدد الخلفيات
                    if (category.wallpaperCount > 0)
                      Row(
                        children: [
                          Icon(
                            Icons.wallpaper,
                            color: Colors.white.withAlpha(200),
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${category.wallpaperCount} خلفية',
                            style: AppTypography.bodySmall.copyWith(
                              color: Colors.white.withAlpha(200),
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // الحصول على أيقونة مناسبة للفئة
  IconData _getCategoryIcon() {
    switch (category.id) {
      case 'مسجد':
        return Icons.mosque;
      case 'دعاء':
        return Icons.favorite;
      case 'القران':
        return Icons.menu_book;
      case 'ذكر الله':
        return Icons.star;
      case 'اسم الله الحسنى':
        return Icons.brightness_7;
      case 'متنوعه':
        return Icons.collections;
      default:
        return Icons.image;
    }
  }

  Widget _buildPlaceholder({bool isCircular = false}) {
    return Shimmer.fromColors(
      baseColor: AppColors.card,
      highlightColor: AppColors.surfaceLight,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          shape: isCircular ? BoxShape.circle : BoxShape.rectangle,
          borderRadius: isCircular ? null : BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }

  // بناء صورة الفئة (محلية أو من الإنترنت)
  Widget _buildCategoryImage({bool isCircular = false}) {
    final imageUrl = category.imageUrl;

    // التحقق مما إذا كانت الصورة محلية
    if (imageUrl.startsWith('assets/')) {
      return Image.asset(imageUrl, fit: BoxFit.cover);
    } else {
      // صورة من الإنترنت
      return CachedNetworkImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover,
        memCacheHeight: isCircular ? 300 : 400,
        fadeInDuration: const Duration(milliseconds: 300),
        placeholder:
            (context, url) => _buildPlaceholder(isCircular: isCircular),
        errorWidget:
            (context, url, error) => Container(
              color: category.color ?? AppColors.primary,
              child: const Center(
                child: Icon(Icons.error_outline, color: Colors.white, size: 32),
              ),
            ),
      );
    }
  }
}
