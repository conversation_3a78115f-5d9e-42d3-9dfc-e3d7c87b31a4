import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';
import '../../../data/models/wallpaper.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/shadows.dart';
import '../effects/glass_container.dart';
import '../loaders/shimmer_loader.dart';

/// قسم الفئات الاحترافي
class ProfessionalCategoriesSection extends StatelessWidget {
  final List<Category> categories;
  final bool isLoading;
  final String title;
  final VoidCallback? onSeeAllPressed;

  const ProfessionalCategoriesSection({
    Key? key,
    required this.categories,
    this.isLoading = false,
    this.title = 'الفئات',
    this.onSeeAllPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // استخدام MediaQuery للحصول على أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;
    final categorySize = screenSize.width * 0.22; // حجم نسبي للفئة

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        Padding(
          padding: const EdgeInsets.fromLTRB(
            AppDimensions.paddingMedium,
            AppDimensions.paddingLarge,
            AppDimensions.paddingMedium,
            AppDimensions.paddingMedium,
          ),
          child: Row(
            textDirection: TextDirection.rtl,
            children: [
              // أيقونة الفئات
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  color: AppColors.accent.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusMedium,
                  ),
                ),
                child: Icon(
                  Icons.category_rounded,
                  color: AppColors.accent,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppDimensions.marginSmall),

              // عنوان القسم
              Text(
                title,
                style: AppTypography.titleMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  shadows: AppShadows.textSmall,
                ),
              ),

              const Spacer(),

              // زر عرض الكل
              if (onSeeAllPressed != null)
                TextButton(
                  onPressed: onSeeAllPressed,
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.accent,
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingSmall,
                      vertical: AppDimensions.paddingXSmall,
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(
                        'عرض الكل',
                        style: AppTypography.labelMedium.copyWith(
                          color: AppColors.accent,
                        ),
                      ),
                      const SizedBox(width: 4),
                      const Icon(Icons.arrow_forward_ios_rounded, size: 12),
                    ],
                  ),
                ),
            ],
          ),
        ),

        // عرض الفئات
        SizedBox(
          height: categorySize * 1.8, // ارتفاع نسبي للقسم
          child:
              isLoading
                  ? _buildLoadingCategories(categorySize)
                  : _buildCategories(context, categorySize),
        ),
      ],
    );
  }

  Widget _buildCategories(BuildContext context, double categorySize) {
    return AnimationLimiter(
      child: LayoutBuilder(
        builder: (context, constraints) {
          // حساب العرض المتاح للقائمة
          final availableWidth = constraints.maxWidth;
          // حساب عرض العنصر بناءً على العرض المتاح
          final itemWidth = availableWidth / 4.5;

          return ListView.builder(
            scrollDirection: Axis.horizontal,
            physics: const BouncingScrollPhysics(),
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingMedium,
            ),
            itemCount: categories.length,
            cacheExtent: 1000, // تخزين مؤقت أكبر للعناصر
            itemBuilder: (context, index) {
              final category = categories[index];

              // تحميل مسبق للصورة
              if (category.imageUrl.isNotEmpty) {
                precacheImage(
                  CachedNetworkImageProvider(category.imageUrl),
                  context,
                );
              }

              return AnimationConfiguration.staggeredList(
                position: index,
                duration: const Duration(milliseconds: 500),
                child: SlideAnimation(
                  horizontalOffset: 50.0,
                  child: FadeInAnimation(
                    child: Padding(
                      padding: EdgeInsets.only(
                        right: index == 0 ? 0 : AppDimensions.paddingMedium,
                      ),
                      child: ProfessionalCategoryCard(
                        category: category,
                        size: itemWidth,
                        onTap: () {
                          context.push(
                            '/category/${category.id}',
                            extra: category,
                          );
                        },
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildLoadingCategories(double categorySize) {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
      ),
      itemCount: 5, // عدد العناصر الوهمية
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(
            right: index == 0 ? 0 : AppDimensions.paddingMedium,
          ),
          child: CategoryCardShimmer(
            width: categorySize,
            height: categorySize,
            isCircular: true,
          ),
        );
      },
    );
  }
}

/// بطاقة الفئة الاحترافية
class ProfessionalCategoryCard extends StatelessWidget {
  final Category category;
  final double size;
  final VoidCallback? onTap;

  const ProfessionalCategoryCard({
    Key? key,
    required this.category,
    required this.size,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          // صورة الفئة
          Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: (category.color ?? Colors.blue).withAlpha(76),
                      blurRadius: 10,
                      spreadRadius: 2,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: ClipOval(
                  child: GlassContainer(
                    borderRadius: size / 2,
                    blur: 5,
                    opacity: 0.7,
                    border: 1.5,
                    borderColor: Colors.white.withAlpha(51),
                    backgroundColor: (category.color ?? Colors.blue).withAlpha(
                      150,
                    ),
                    child:
                        category.imageUrl.isNotEmpty
                            ? CachedNetworkImage(
                              imageUrl: category.imageUrl,
                              fit: BoxFit.cover,
                              placeholder:
                                  (context, url) => Container(
                                    color: (category.color ?? Colors.blue)
                                        .withAlpha(76),
                                    child: const Center(
                                      child: CircularProgressIndicator(
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                        strokeWidth: 2,
                                      ),
                                    ),
                                  ),
                              errorWidget:
                                  (context, url, error) => Container(
                                    color: (category.color ?? Colors.blue)
                                        .withAlpha(76),
                                    child: const Icon(
                                      Icons.error_outline_rounded,
                                      color: Colors.white,
                                    ),
                                  ),
                            )
                            : Icon(
                              Icons.image_rounded,
                              color: Colors.white,
                              size: size * 0.4,
                            ),
                  ),
                ),
              )
              .animate()
              .fadeIn(duration: const Duration(milliseconds: 500))
              .scale(
                begin: const Offset(0.8, 0.8),
                end: const Offset(1, 1),
                duration: const Duration(milliseconds: 500),
                curve: Curves.easeOutBack,
              ),

          // اسم الفئة
          const SizedBox(height: AppDimensions.marginSmall),
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingSmall,
              vertical: AppDimensions.paddingXSmall,
            ),
            decoration: BoxDecoration(
              color: (category.color ?? Colors.blue).withAlpha(51),
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            ),
            child: Text(
              category.name,
              style: AppTypography.labelMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                shadows: AppShadows.textSmall,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ).animate().fadeIn(
            delay: const Duration(milliseconds: 200),
            duration: const Duration(milliseconds: 500),
          ),
        ],
      ),
    );
  }
}
