import 'package:flutter/material.dart';

class NavigationUtils {
  // الانتقال إلى شاشة جديدة
  static Future<T?> navigateTo<T>(BuildContext context, Widget screen) {
    return Navigator.push<T>(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }
  
  // الانتقال إلى شاشة جديدة واستبدال الشاشة الحالية
  static Future<T?> navigateToReplacement<T>(BuildContext context, Widget screen) {
    return Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }
  
  // الانتقال إلى شاشة جديدة وإزالة جميع الشاشات السابقة
  static Future<T?> navigateAndRemoveUntil<T>(BuildContext context, Widget screen) {
    return Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (context) => screen),
      (route) => false,
    );
  }
  
  // العودة إلى الشاشة السابقة
  static void goBack<T>(BuildContext context, [T? result]) {
    Navigator.pop<T>(context, result);
  }
}
