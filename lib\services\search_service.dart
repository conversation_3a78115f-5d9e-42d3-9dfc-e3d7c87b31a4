import '../data/models/wallpaper.dart';

class SearchService {
  // البحث عن خلفيات عبر الإنترنت
  Future<List<Wallpaper>> searchOnlineWallpapers(String query) async {
    try {
      // استخدام محرك بحث بسيط (هذا مثال فقط، يمكن استبداله بـ API حقيقي)
      // في بيئة الإنتاج، يجب استخدام API مناسب
      // هنا نستخدم بيانات وهمية للتوضيح
      return _getMockSearchResults(query);
    } catch (e) {
      return [];
    }
  }

  // بيانات وهمية للبحث (للتوضيح فقط)
  List<Wallpaper> _getMockSearchResults(String query) {
    final List<Wallpaper> results = [];

    // إضافة نتائج وهمية بناءً على الاستعلام
    if (query.contains('مسجد') || query.contains('mosque')) {
      results.addAll([
        Wallpaper(
          id: 'search_1',
          imageUrl:
              'https://images.unsplash.com/photo-1584810359583-96fc3448beaa',
          thumbnailUrl:
              'https://images.unsplash.com/photo-1584810359583-96fc3448beaa?w=200',
          category: 'مساجد',
        ),
        Wallpaper(
          id: 'search_2',
          imageUrl:
              'https://images.unsplash.com/photo-1535468850893-d6e543fbd7f5',
          thumbnailUrl:
              'https://images.unsplash.com/photo-1535468850893-d6e543fbd7f5?w=200',
          category: 'مساجد',
        ),
      ]);
    }

    if (query.contains('قرآن') || query.contains('quran')) {
      results.addAll([
        Wallpaper(
          id: 'search_3',
          imageUrl:
              'https://images.unsplash.com/photo-1609599006353-e629aaabfeae',
          thumbnailUrl:
              'https://images.unsplash.com/photo-1609599006353-e629aaabfeae?w=200',
          category: 'قرآن',
        ),
        Wallpaper(
          id: 'search_4',
          imageUrl:
              'https://images.unsplash.com/photo-1591604129939-f1efa4d9f7fa',
          thumbnailUrl:
              'https://images.unsplash.com/photo-1591604129939-f1efa4d9f7fa?w=200',
          category: 'قرآن',
        ),
      ]);
    }

    if (query.contains('مكة') || query.contains('mecca')) {
      results.addAll([
        Wallpaper(
          id: 'search_5',
          imageUrl:
              'https://images.unsplash.com/photo-1519817650390-64a93db51149',
          thumbnailUrl:
              'https://images.unsplash.com/photo-1519817650390-64a93db51149?w=200',
          category: 'مكة',
        ),
      ]);
    }

    if (query.contains('رمضان') || query.contains('ramadan')) {
      results.addAll([
        Wallpaper(
          id: 'search_6',
          imageUrl:
              'https://images.unsplash.com/photo-1566624790190-511a09f6ddbd',
          thumbnailUrl:
              'https://images.unsplash.com/photo-1566624790190-511a09f6ddbd?w=200',
          category: 'رمضان',
        ),
      ]);
    }

    // إذا لم تكن هناك نتائج محددة، إضافة بعض النتائج العامة
    if (results.isEmpty) {
      results.addAll([
        Wallpaper(
          id: 'search_7',
          imageUrl:
              'https://images.unsplash.com/photo-1584810359583-96fc3448beaa',
          thumbnailUrl:
              'https://images.unsplash.com/photo-1584810359583-96fc3448beaa?w=200',
          category: 'إسلامية',
        ),
        Wallpaper(
          id: 'search_8',
          imageUrl:
              'https://images.unsplash.com/photo-1609599006353-e629aaabfeae',
          thumbnailUrl:
              'https://images.unsplash.com/photo-1609599006353-e629aaabfeae?w=200',
          category: 'إسلامية',
        ),
      ]);
    }

    return results;
  }
}
