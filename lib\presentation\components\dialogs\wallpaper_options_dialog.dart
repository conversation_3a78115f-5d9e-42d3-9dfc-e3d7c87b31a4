import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/typography.dart';

/// نافذة حوار خيارات تعيين الخلفية - تصميم محسن
class WallpaperOptionsDialog extends StatefulWidget {
  final Function(String) onApply;
  final VoidCallback? onCancel;

  const WallpaperOptionsDialog({
    super.key,
    required this.onApply,
    this.onCancel,
  });

  @override
  State<WallpaperOptionsDialog> createState() => _WallpaperOptionsDialogState();
}

class _WallpaperOptionsDialogState extends State<WallpaperOptionsDialog>
    with SingleTickerProviderStateMixin {
  String _selectedOption = 'home';
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // استخدام MediaQuery للحصول على أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;
    final dialogWidth = screenSize.width * 0.9; // زيادة عرض الحوار

    return ScaleTransition(
      scale: _scaleAnimation,
      child: Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.symmetric(
          horizontal: screenSize.width * 0.05,
          vertical: screenSize.height * 0.03,
        ),
        child: Container(
          width: dialogWidth,
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(200),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.white.withAlpha(30), width: 1),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(70),
                blurRadius: 15,
                spreadRadius: 1,
              ),
            ],
          ),
          padding: EdgeInsets.all(screenSize.width * 0.04),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // العنوان
              Padding(
                padding: EdgeInsets.only(top: screenSize.height * 0.01),
                child: Text(
                  'تعيين الخلفية',
                  style: AppTypography.titleLarge.copyWith(
                    fontSize: screenSize.width * 0.045, // تصغير حجم العنوان
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              SizedBox(height: screenSize.height * 0.005), // تقليل المسافة
              // الوصف
              Text(
                'اختر أين تريد تطبيق هذه الخلفية',
                style: AppTypography.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontSize: screenSize.width * 0.03, // تصغير حجم النص الوصفي
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: screenSize.height * 0.015), // تقليل المسافة
              // خيارات تعيين الخلفية
              _buildWallpaperOptions(),

              SizedBox(height: screenSize.height * 0.015), // تقليل المسافة
              // أزرار الإجراءات
              Row(
                children: [
                  // زر الإلغاء
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        if (widget.onCancel != null) {
                          widget.onCancel!();
                        }
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.transparent,
                        foregroundColor: Colors.white.withAlpha(180),
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: Colors.white.withAlpha(50),
                            width: 1,
                          ),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: screenSize.width * 0.03),

                  // زر التطبيق
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        widget.onApply(_selectedOption);
                        Navigator.pop(context);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.accent,
                        foregroundColor: Colors.black,
                        elevation: 0,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'تطبيق',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWallpaperOptions() {
    // استخدام MediaQuery للحصول على أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;
    final optionSpacing =
        screenSize.height * 0.01; // تقليل المسافة بين الخيارات

    return Column(
      children: [
        // الشاشة الرئيسية
        _buildOptionTile(
          title: 'الشاشة الرئيسية',
          subtitle: 'تعيين كخلفية للشاشة الرئيسية',
          icon: Icons.home_rounded,
          value: 'home',
          iconColor: AppColors.primary,
        ),

        SizedBox(height: optionSpacing),

        // شاشة القفل
        _buildOptionTile(
          title: 'شاشة القفل',
          subtitle: 'تعيين كخلفية لشاشة القفل',
          icon: Icons.lock_rounded,
          value: 'lock',
          iconColor: AppColors.secondary,
        ),

        SizedBox(height: optionSpacing),

        // كلاهما
        _buildOptionTile(
          title: 'كلاهما',
          subtitle: 'تعيين كخلفية للشاشة الرئيسية وشاشة القفل',
          icon: Icons.smartphone_rounded,
          value: 'both',
          iconColor: AppColors.accent,
        ),
      ],
    );
  }

  Widget _buildOptionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required String value,
    required Color iconColor,
  }) {
    final screenSize = MediaQuery.of(context).size;
    final isSelected = _selectedOption == value;
    final iconSize = screenSize.width * 0.07;
    final fontSize = screenSize.width * 0.035;
    final subtitleFontSize = screenSize.width * 0.025;
    final borderRadius = screenSize.width * 0.02;

    return Container(
      margin: EdgeInsets.symmetric(vertical: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedOption = value;
            });
          },
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
            decoration: BoxDecoration(
              color:
                  isSelected ? Colors.white.withAlpha(20) : Colors.transparent,
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: isSelected ? iconColor : Colors.white.withAlpha(20),
                width: isSelected ? 1.5 : 0.5,
              ),
            ),
            child: Row(
              children: [
                // أيقونة الخيار
                Container(
                  width: iconSize,
                  height: iconSize,
                  decoration: BoxDecoration(
                    color: iconColor.withAlpha(25),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: iconColor, size: iconSize * 0.5),
                ),

                SizedBox(width: 8),

                // نص الخيار
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: AppTypography.titleSmall.copyWith(
                          fontSize: fontSize,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: AppTypography.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                          fontSize: subtitleFontSize,
                        ),
                      ),
                    ],
                  ),
                ),

                // زر الاختيار
                Radio<String>(
                  value: value,
                  groupValue: _selectedOption,
                  onChanged: (newValue) {
                    setState(() {
                      _selectedOption = newValue!;
                    });
                  },
                  activeColor: iconColor,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  visualDensity: VisualDensity.compact,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
