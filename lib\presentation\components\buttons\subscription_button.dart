import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';

/// زر الاشتراك الموحد
/// يستخدم في جميع أنحاء التطبيق للحفاظ على تناسق التصميم
class SubscriptionButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool isCompact;
  final bool isFixed;
  final String? text;
  final double height;
  final double? width;
  final bool showIcon;
  final bool useGradient;

  const SubscriptionButton({
    super.key,
    this.onPressed,
    this.isCompact = false,
    this.isFixed = false,
    this.text,
    this.height = 40,
    this.width,
    this.showIcon = true,
    this.useGradient = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed ?? () => context.push('/subscription'),
      child: Container(
        height: height,
        width: width,
        padding: EdgeInsets.symmetric(
          horizontal: isCompact ? 10 : AppDimensions.paddingMedium,
          vertical: AppDimensions.paddingXSmall,
        ),
        decoration: BoxDecoration(
          gradient:
              useGradient
                  ? const LinearGradient(
                    colors: [Color(0xFFD4AF37), Color(0xFFFFD700)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  )
                  : null,
          color: useGradient ? null : AppColors.accent,
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFD4AF37).withOpacity(0.35),
              blurRadius: 10,
              spreadRadius: 0,
              offset: const Offset(0, 3),
            ),
          ],
          border: Border.all(color: Colors.white.withOpacity(0.3), width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (showIcon) ...[
              Container(
                padding: const EdgeInsets.all(3),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.15),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.workspace_premium,
                  color: Colors.black,
                  size: isCompact ? 16 : 18,
                ),
              ),
              SizedBox(width: isCompact ? 4 : 6),
            ],
            Text(
              text ?? (isCompact ? 'مميز' : 'اشترك الآن'),
              style: (isCompact
                      ? AppTypography.labelSmall
                      : AppTypography.labelMedium)
                  .copyWith(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.3,
                    height: 1.2,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
