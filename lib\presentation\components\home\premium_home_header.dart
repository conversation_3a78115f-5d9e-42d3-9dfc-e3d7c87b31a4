import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/dimensions.dart';
import '../effects/glass_container.dart';
import '../buttons/premium_button.dart';
import '../buttons/subscription_button.dart';
import '../../../data/services/subscription_service.dart';

/// شريط علوي احترافي للصفحة الرئيسية
class PremiumHomeHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final bool isTransparent;
  final double height;
  final VoidCallback? onSubscribePressed;

  const PremiumHomeHeader({
    super.key,
    this.title = 'إسلامية',
    this.subtitle = 'اكتشف أجمل الخلفيات الإسلامية',
    this.isTransparent = false,
    this.height = 120,
    this.onSubscribePressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.black,
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withOpacity(0.98),
            Colors.black.withOpacity(0.95),
            Colors.black.withOpacity(0.90),
          ],
        ),
        boxShadow:
            isTransparent
                ? null
                : [
                  BoxShadow(
                    color: Colors.black.withAlpha(120),
                    blurRadius: 12,
                    spreadRadius: 1,
                    offset: const Offset(0, 4),
                  ),
                ],
        border: Border(
          bottom: BorderSide(color: Colors.white.withOpacity(0.08), width: 1),
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingLarge,
            vertical: AppDimensions.paddingSmall,
          ),
          child: Row(
            textDirection: TextDirection.rtl,
            children: [
              // زر الاشتراك المميز - يظهر فقط للمستخدمين غير المشتركين
              FutureBuilder<bool>(
                future: SubscriptionService().isSubscribed(),
                builder: (context, snapshot) {
                  final isSubscribed = snapshot.data ?? false;

                  if (isSubscribed) {
                    // للمستخدمين المشتركين، نعرض شارة الاشتراك
                    return Container(
                      height: 44,
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingMedium,
                        vertical: AppDimensions.paddingXSmall,
                      ),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF1E8449), Color(0xFF27AE60)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusLarge,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFF27AE60).withAlpha(100),
                            blurRadius: 12,
                            spreadRadius: 1,
                            offset: const Offset(0, 3),
                          ),
                        ],
                        border: Border.all(
                          color: Colors.white.withOpacity(0.25),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(3),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.25),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 4,
                                  spreadRadius: 0,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'مشترك',
                            style: AppTypography.labelMedium.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  // للمستخدمين غير المشتركين، نعرض زر الاشتراك
                  return SubscriptionButton(
                    onPressed: onSubscribePressed,
                    text: 'اشترك الآن',
                    height: 44,
                  );
                },
              ),

              const SizedBox(width: 16),

              // عنوان التطبيق والوصف
              Expanded(
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    // استخدام LayoutBuilder للحصول على قيود الحجم المتاحة
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // عنوان التطبيق مع تأثير ظل
                        ShaderMask(
                          shaderCallback:
                              (bounds) => LinearGradient(
                                colors: [
                                  Colors.white,
                                  Colors.white.withOpacity(0.95),
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ).createShader(bounds),
                          child: Text(
                            title,
                            style: AppTypography.titleLarge.copyWith(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 0.5,
                              height: 1.2,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.6),
                                  blurRadius: 5,
                                  offset: const Offset(0, 2),
                                ),
                                Shadow(
                                  color: AppColors.accent.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        const SizedBox(height: 6),

                        // وصف التطبيق مع تأثير تدرج
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                AppColors.accent.withOpacity(0.15),
                                Colors.transparent,
                              ],
                              begin: Alignment.centerRight,
                              end: Alignment.centerLeft,
                            ),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 3,
                          ),
                          child: Container(), // حذف النص في المربع الأحمر
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
