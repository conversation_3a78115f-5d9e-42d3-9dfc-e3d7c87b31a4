import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'subscription_service.dart';
import '../../services/remote_config_service.dart';

/// خدمة إدارة الإعلانات في التطبيق
class AdService {
  static final AdService _instance = AdService._internal();
  final SubscriptionService _subscriptionService = SubscriptionService();
  final RemoteConfigService _remoteConfigService = RemoteConfigService();

  // معرفات الإعلانات الثابتة الجديدة - لا تتغير من الخادم
  static const String _bannerAdUnitId =
      'ca-app-pub-9278102953647270/3778249828';
  static const String _interstitialAdUnitId =
      'ca-app-pub-9278102953647270/3151563865';
  static const String _rewardedAdUnitId =
      'ca-app-pub-9278102953647270/3416924224';

  // متغيرات لتخزين الإعلانات
  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  RewardedAd? _rewardedAd;

  // متغيرات للتحكم في عرض الإعلانات
  bool _isInitialized = false;
  bool _shouldShowAds = true;
  DateTime? _lastInterstitialShown;
  DateTime? _lastRewardedShown;
  int _interstitialCounter = 0;
  int _dailyAdCount = 0;
  DateTime? _lastAdCountReset;

  // حماية ضد النقرات الاحتيالية
  static const int _maxDailyAds = 15; // الحد الأقصى للإعلانات اليومية
  static const int _minInterstitialInterval =
      120; // الحد الأدنى للفاصل الزمني بين الإعلانات البينية (بالثواني)
  static const int _minRewardedInterval =
      180; // الحد الأدنى للفاصل الزمني بين إعلانات المكافأة (بالثواني)

  // الحصول على نسخة واحدة من الخدمة (Singleton)
  factory AdService() {
    return _instance;
  }

  AdService._internal();

  /// تهيئة خدمة الإعلانات - محسن لتجنب ANR
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة خدمة الاشتراك
      await _subscriptionService.initialize();

      // تهيئة خدمة التحكم عن بعد
      if (!_remoteConfigService.isInitialized) {
        await _remoteConfigService.initialize();

        // تحديث التكوين من الخادم
        await _remoteConfigService.fetchConfig(
          "https://drive.google.com/uc?id=1mut1fYcZMmVFFBuFKjmTEwb-ds6KUnZM",
        );
      }

      // التحقق من حالة الاشتراك
      _shouldShowAds = !(await _subscriptionService.shouldHideAds());

      // استعادة بيانات الإعلانات من التخزين المحلي
      await _loadAdStats();

      // تهيئة SDK الإعلانات
      await MobileAds.instance.initialize();

      // إعداد حماية ضد النقرات الاحتيالية مع معرف الجهاز الجديد
      final RequestConfiguration configuration = RequestConfiguration(
        tagForChildDirectedTreatment: TagForChildDirectedTreatment.yes,
        tagForUnderAgeOfConsent: TagForUnderAgeOfConsent.yes,
        testDeviceIds: [
          '4c50a281-3f15-4191-a3c1-6ece0e6c03bf', // معرف جهاز الاختبار الجديد
        ],
      );
      MobileAds.instance.updateRequestConfiguration(configuration);

      // تم إزالة تحميل إعلان فتح التطبيق

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الإعلانات بنجاح');
      debugPrint('ℹ️ حالة عرض الإعلانات: $_shouldShowAds');
      debugPrint('ℹ️ عدد الإعلانات اليوم: $_dailyAdCount / $_maxDailyAds');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإعلانات: $e');
      // تعيين حالة التهيئة حتى لو فشلت لتجنب محاولات التهيئة المتكررة
      _isInitialized = true;
    }
  }

  /// تحميل إحصائيات الإعلانات من التخزين المحلي
  Future<void> _loadAdStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // استعادة عدد الإعلانات اليومية
      _dailyAdCount = prefs.getInt('daily_ad_count') ?? 0;

      // استعادة تاريخ آخر إعادة تعيين
      final lastResetStr = prefs.getString('last_ad_count_reset');
      if (lastResetStr != null) {
        _lastAdCountReset = DateTime.parse(lastResetStr);

        // التحقق مما إذا كان يجب إعادة تعيين العداد (يوم جديد)
        final now = DateTime.now();
        if (_lastAdCountReset!.day != now.day ||
            _lastAdCountReset!.month != now.month ||
            _lastAdCountReset!.year != now.year) {
          _resetDailyAdCount();
        }
      } else {
        _lastAdCountReset = DateTime.now();
        await _saveAdStats();
      }

      // استعادة وقت آخر إعلان بيني
      final lastInterstitialStr = prefs.getString('last_interstitial_shown');
      if (lastInterstitialStr != null) {
        _lastInterstitialShown = DateTime.parse(lastInterstitialStr);
      }

      // استعادة وقت آخر إعلان مكافأة
      final lastRewardedStr = prefs.getString('last_rewarded_shown');
      if (lastRewardedStr != null) {
        _lastRewardedShown = DateTime.parse(lastRewardedStr);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إحصائيات الإعلانات: $e');
    }
  }

  /// حفظ إحصائيات الإعلانات في التخزين المحلي
  Future<void> _saveAdStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // حفظ عدد الإعلانات اليومية
      await prefs.setInt('daily_ad_count', _dailyAdCount);

      // حفظ تاريخ آخر إعادة تعيين
      if (_lastAdCountReset != null) {
        await prefs.setString(
          'last_ad_count_reset',
          _lastAdCountReset!.toIso8601String(),
        );
      }

      // حفظ وقت آخر إعلان بيني
      if (_lastInterstitialShown != null) {
        await prefs.setString(
          'last_interstitial_shown',
          _lastInterstitialShown!.toIso8601String(),
        );
      }

      // حفظ وقت آخر إعلان مكافأة
      if (_lastRewardedShown != null) {
        await prefs.setString(
          'last_rewarded_shown',
          _lastRewardedShown!.toIso8601String(),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إحصائيات الإعلانات: $e');
    }
  }

  /// إعادة تعيين عداد الإعلانات اليومية
  void _resetDailyAdCount() {
    _dailyAdCount = 0;
    _lastAdCountReset = DateTime.now();
    _saveAdStats();
  }

  /// التحقق مما إذا كان يمكن عرض المزيد من الإعلانات اليوم
  bool _canShowMoreAdsToday() {
    return _dailyAdCount < _maxDailyAds;
  }

  // تم إزالة دالة زيادة عداد الإعلانات اليومية غير المستخدمة

  /// التحقق من حالة عرض الإعلانات
  Future<bool> shouldShowAds() async {
    if (!_isInitialized) {
      await initialize();
    }

    // تحديث حالة عرض الإعلانات
    _shouldShowAds = !(await _subscriptionService.shouldHideAds());

    // التحقق من إعدادات التحكم عن بعد
    if (_remoteConfigService.isInitialized) {
      // إذا كانت الإعلانات معطلة من التحكم عن بعد، نعيد false
      if (!_remoteConfigService.config.showBannerAds &&
          !_remoteConfigService.config.showInterstitialAds &&
          !_remoteConfigService.config.showRewardedAds) {
        debugPrint('ℹ️ جميع الإعلانات معطلة من التحكم عن بعد');
        return false;
      }
    }

    return _shouldShowAds;
  }

  /// تحميل إعلان البانر
  Future<BannerAd?> loadBannerAd() async {
    if (!await shouldShowAds()) {
      return null;
    }

    // التحقق من إعدادات التحكم عن بعد للإعلانات البانر
    if (_remoteConfigService.isInitialized &&
        !_remoteConfigService.config.showBannerAds) {
      debugPrint('ℹ️ إعلانات البانر معطلة من التحكم عن بعد');
      return null;
    }

    try {
      // إذا كان هناك إعلان بانر موجود، قم بتحريره
      _bannerAd?.dispose();

      // إنشاء إعلان بانر جديد باستخدام المعرف الثابت
      _bannerAd = BannerAd(
        adUnitId: _bannerAdUnitId,
        size: AdSize.banner,
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            debugPrint('✅ تم تحميل إعلان البانر بنجاح');
          },
          onAdFailedToLoad: (ad, error) {
            debugPrint('❌ فشل تحميل إعلان البانر: ${error.message}');
            ad.dispose();
            _bannerAd = null;
          },
        ),
      );

      // تحميل الإعلان
      await _bannerAd!.load();
      return _bannerAd;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعلان البانر: $e');
      return null;
    }
  }

  /// تحميل إعلان بيني
  Future<void> loadInterstitialAd() async {
    if (!await shouldShowAds()) {
      return;
    }

    // التحقق من إعدادات التحكم عن بعد للإعلانات البينية
    if (_remoteConfigService.isInitialized &&
        !_remoteConfigService.config.showInterstitialAds) {
      debugPrint('ℹ️ الإعلانات البينية معطلة من التحكم عن بعد');
      return;
    }

    try {
      // استخدام معرف الإعلان البيني الثابت
      await InterstitialAd.load(
        adUnitId: _interstitialAdUnitId,
        request: const AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (ad) {
            _interstitialAd = ad;
            debugPrint('✅ تم تحميل الإعلان البيني بنجاح');

            // إضافة مستمع للإغلاق
            _interstitialAd!
                .fullScreenContentCallback = FullScreenContentCallback(
              onAdDismissedFullScreenContent: (ad) {
                debugPrint('تم إغلاق الإعلان البيني');
                ad.dispose();
                _interstitialAd = null;
                // إعادة تحميل الإعلان للمرة القادمة
                loadInterstitialAd();
              },
              onAdFailedToShowFullScreenContent: (ad, error) {
                debugPrint('❌ فشل عرض الإعلان البيني: ${error.message}');
                ad.dispose();
                _interstitialAd = null;
                // إعادة تحميل الإعلان للمرة القادمة
                loadInterstitialAd();
              },
            );
          },
          onAdFailedToLoad: (error) {
            debugPrint('❌ فشل تحميل الإعلان البيني: ${error.message}');
            _interstitialAd = null;
          },
        ),
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإعلان البيني: $e');
    }
  }

  /// عرض إعلان بيني
  Future<bool> showInterstitialAd() async {
    if (!await shouldShowAds()) {
      return false;
    }

    // التحقق من الحد اليومي للإعلانات
    if (!_canShowMoreAdsToday()) {
      debugPrint('⚠️ تم الوصول إلى الحد اليومي للإعلانات');
      return false;
    }

    // التحقق من الوقت المنقضي منذ آخر إعلان
    final now = DateTime.now();
    if (_lastInterstitialShown != null) {
      final difference = now.difference(_lastInterstitialShown!);
      // عدم عرض الإعلان إذا لم يمر الوقت المحدد على الأقل
      if (difference.inSeconds < _minInterstitialInterval) {
        debugPrint(
          '⚠️ لم يمر وقت كافٍ منذ آخر إعلان بيني (${difference.inSeconds}s)',
        );
        return false;
      }
    }

    // زيادة العداد
    _interstitialCounter++;

    // عرض الإعلان كل 3 مرات فقط
    if (_interstitialCounter % 3 != 0) {
      return false;
    }

    try {
      if (_interstitialAd == null) {
        await loadInterstitialAd();
        // إذا لم يتم تحميل الإعلان بنجاح
        if (_interstitialAd == null) {
          return false;
        }
      }

      // زيادة عداد الإعلانات اليومية
      _dailyAdCount++;
      await _saveAdStats();

      await _interstitialAd!.show();
      _lastInterstitialShown = now;
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في عرض الإعلان البيني: $e');
      return false;
    }
  }

  // دالة عرض إعلان فتح التطبيق
  Future<bool> showAppOpenAd() async {
    if (!await shouldShowAds()) {
      return false;
    }

    // التحقق من الحد اليومي للإعلانات
    if (!_canShowMoreAdsToday()) {
      debugPrint('⚠️ تم الوصول إلى الحد اليومي للإعلانات');
      return false;
    }

    // زيادة عداد الإعلانات اليومية
    _dailyAdCount++;
    await _saveAdStats();

    // في الوقت الحالي، نعيد true لتجنب الأخطاء في التطبيق
    // يمكن تنفيذ منطق عرض إعلان فتح التطبيق لاحقًا
    debugPrint('ℹ️ تم استدعاء دالة عرض إعلان فتح التطبيق');
    return true;
  }

  /// تحميل إعلان المكافأة
  Future<void> loadRewardedAd() async {
    if (!await shouldShowAds()) {
      return;
    }

    // التحقق من إعدادات التحكم عن بعد لإعلانات المكافأة
    if (_remoteConfigService.isInitialized &&
        !_remoteConfigService.config.showRewardedAds) {
      debugPrint('ℹ️ إعلانات المكافأة معطلة من التحكم عن بعد');
      return;
    }

    try {
      // استخدام معرف إعلان المكافأة الثابت
      await RewardedAd.load(
        adUnitId: _rewardedAdUnitId,
        request: const AdRequest(),
        rewardedAdLoadCallback: RewardedAdLoadCallback(
          onAdLoaded: (ad) {
            _rewardedAd = ad;
            debugPrint('✅ تم تحميل إعلان المكافأة بنجاح');
          },
          onAdFailedToLoad: (error) {
            debugPrint('❌ فشل تحميل إعلان المكافأة: ${error.message}');
            _rewardedAd = null;
          },
        ),
      );
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعلان المكافأة: $e');
    }
  }

  /// عرض إعلان المكافأة
  Future<bool> showRewardedAd({
    required Function onRewarded,
    required Function onFailed,
  }) async {
    if (!await shouldShowAds()) {
      // إذا كان المستخدم مشتركًا، نعتبره كأنه شاهد الإعلان ونمنحه المكافأة مباشرة
      onRewarded();
      return true;
    }

    // التحقق من إعدادات التحكم عن بعد لإعلانات المكافأة
    if (_remoteConfigService.isInitialized &&
        !_remoteConfigService.config.showRewardedAds) {
      debugPrint('ℹ️ إعلانات المكافأة معطلة من التحكم عن بعد');
      // إذا كانت إعلانات المكافأة معطلة، نعتبر المستخدم كأنه مشترك ونمنحه المكافأة مباشرة
      onRewarded();
      return true;
    }

    // التحقق من الحد اليومي للإعلانات
    if (!_canShowMoreAdsToday()) {
      debugPrint('⚠️ تم الوصول إلى الحد اليومي للإعلانات');
      // في حالة إعلانات المكافأة، نسمح للمستخدم بالحصول على المكافأة حتى لو وصل للحد اليومي
      onRewarded();
      return true;
    }

    // التحقق من الوقت المنقضي منذ آخر إعلان مكافأة
    final now = DateTime.now();
    if (_lastRewardedShown != null) {
      final difference = now.difference(_lastRewardedShown!);
      // عدم عرض الإعلان إذا لم يمر الوقت المحدد على الأقل
      if (difference.inSeconds < _minRewardedInterval) {
        debugPrint(
          '⚠️ لم يمر وقت كافٍ منذ آخر إعلان مكافأة (${difference.inSeconds}s)',
        );
        // في حالة إعلانات المكافأة، نسمح للمستخدم بالحصول على المكافأة حتى لو لم يمر الوقت الكافي
        onRewarded();
        return true;
      }
    }

    try {
      if (_rewardedAd == null) {
        await loadRewardedAd();
        // إذا لم يتم تحميل الإعلان بنجاح
        if (_rewardedAd == null) {
          // محاولة منح المكافأة للمستخدم حتى لو فشل تحميل الإعلان (تجربة مستخدم أفضل)
          debugPrint('⚠️ فشل تحميل إعلان المكافأة، منح المكافأة للمستخدم');
          onRewarded();
          return true;
        }
      }

      // إضافة مستمع للإغلاق والمكافأة
      _rewardedAd!.fullScreenContentCallback = FullScreenContentCallback(
        onAdDismissedFullScreenContent: (ad) {
          debugPrint('تم إغلاق إعلان المكافأة');
          ad.dispose();
          _rewardedAd = null;
          // إعادة تحميل الإعلان للمرة القادمة
          loadRewardedAd();
        },
        onAdFailedToShowFullScreenContent: (ad, error) {
          debugPrint('❌ فشل عرض إعلان المكافأة: ${error.message}');
          ad.dispose();
          _rewardedAd = null;
          // محاولة منح المكافأة للمستخدم حتى لو فشل عرض الإعلان (تجربة مستخدم أفضل)
          onRewarded();
          // إعادة تحميل الإعلان للمرة القادمة
          loadRewardedAd();
        },
      );

      // زيادة عداد الإعلانات اليومية
      _dailyAdCount++;
      await _saveAdStats();

      // تحديث وقت آخر إعلان مكافأة
      _lastRewardedShown = now;
      await _saveAdStats();

      await _rewardedAd!.show(
        onUserEarnedReward: (ad, reward) {
          debugPrint('✅ تم منح المكافأة: ${reward.amount} ${reward.type}');
          onRewarded();
        },
      );

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في عرض إعلان المكافأة: $e');
      // محاولة منح المكافأة للمستخدم حتى لو حدث خطأ (تجربة مستخدم أفضل)
      onRewarded();
      return true;
    }
  }

  /// التحقق من جاهزية إعلان المكافأة
  Future<bool> isRewardedAdReady() async {
    if (!await shouldShowAds()) {
      // إذا كان المستخدم مشتركًا، نعتبر الإعلان جاهزًا دائمًا
      return true;
    }

    // التحقق من وجود إعلان مكافأة محمل
    if (_rewardedAd == null) {
      // محاولة تحميل إعلان جديد
      await loadRewardedAd();
      // انتظار لحظة للتحميل
      await Future.delayed(const Duration(milliseconds: 500));
    }

    return _rewardedAd != null;
  }

  /// تحرير الموارد
  void dispose() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    _rewardedAd?.dispose();
  }
}
