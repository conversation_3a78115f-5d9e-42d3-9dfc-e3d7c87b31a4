import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:go_router/go_router.dart';
import '../../design_system/colors.dart';
import '../../design_system/dimensions.dart';
import '../../data/models/wallpaper.dart';
import '../../data/services/wallpaper_service.dart';
import '../../data/services/category_service.dart';
import '../../data/services/cache_manager.dart';
import '../../services/remote_config_service.dart';
import '../../services/ads_manager.dart';
import '../components/cards/wallpaper_card.dart';
import '../components/home/<USER>';
import '../components/loaders/custom_refresh_indicator.dart';
import '../components/loaders/enhanced_loading_screen.dart';
import '../components/ads/ad_banner.dart';
import '../components/notifications/custom_message_banner.dart';
import '../components/dialogs/eu_consent_dialog.dart';
import '../components/dialogs/update_dialog.dart';

// كلاس للاحتفاظ بالعناصر في الذاكرة
class KeepAliveWrapper extends StatefulWidget {
  final Widget child;

  const KeepAliveWrapper({super.key, required this.child});

  @override
  State<KeepAliveWrapper> createState() => _KeepAliveWrapperState();
}

class _KeepAliveWrapperState extends State<KeepAliveWrapper>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.child;
  }

  @override
  bool get wantKeepAlive => true;
}

class HomeContent extends StatefulWidget {
  const HomeContent({super.key});

  @override
  State<HomeContent> createState() => _HomeContentState();
}

class _HomeContentState extends State<HomeContent> {
  final WallpaperService _wallpaperService = WallpaperService();
  final CategoryService _categoryService = CategoryService();
  final RemoteConfigService _remoteConfigService = RemoteConfigService();
  final AdsManager _adsManager = AdsManager();

  List<Wallpaper> _wallpapers = [];
  List<Category> _categories = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMoreWallpapers = true;
  String _error = '';
  bool _showMessage = false;
  String _message = '';

  @override
  void initState() {
    super.initState();
    _loadData();
    _initRemoteConfig();
    _checkEuConsent();
  }

  // التحقق من حالة موافقة الاتحاد الأوروبي
  Future<void> _checkEuConsent() async {
    if (mounted) {
      // عرض مربع حوار موافقة الاتحاد الأوروبي إذا لزم الأمر
      await EuConsentDialog.show(context);
    }
  }

  // تهيئة خدمة التحكم عن بعد
  Future<void> _initRemoteConfig() async {
    try {
      // تهيئة خدمة التحكم عن بعد
      await _remoteConfigService.initialize();

      // تحديث التكوين من الخادم
      await _remoteConfigService.fetchConfig(
        "https://drive.google.com/uc?id=1mut1fYcZMmVFFBuFKjmTEwb-ds6KUnZM",
      );

      // تحديث حالة الرسالة
      if (_remoteConfigService.config.message.isNotEmpty) {
        setState(() {
          _showMessage = true;
          _message = _remoteConfigService.config.message;
        });
      }

      // التحقق من التحديثات
      if (_remoteConfigService.needsUpdate) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          UpdateDialog.show(context);
        });
      }

      // تهيئة مدير الإعلانات
      await _adsManager.initialize();

      // تحديث الإعلانات من التحكم عن بعد
      await _adsManager.updateFromRemoteConfig(
        "https://drive.google.com/uc?id=1mut1fYcZMmVFFBuFKjmTEwb-ds6KUnZM",
      );

      // تم إزالة خدمة التعامل مع تقييد الإعلانات
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة التحكم عن بعد: $e');
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _loadData() async {
    if (!mounted) return;

    // حفظ البيانات الحالية قبل التحديث
    final List<Wallpaper> currentWallpapers = List.from(_wallpapers);
    final List<Category> currentCategories = List.from(_categories);
    final bool wasEmpty = _wallpapers.isEmpty;

    setState(() {
      _isLoading = wasEmpty; // نعرض شاشة التحميل فقط إذا كانت القائمة فارغة
      _error = '';
    });

    try {
      // محاولة تحميل البيانات المخزنة محلياً أولاً إذا كانت القائمة فارغة
      if (wasEmpty) {
        await _loadCachedData();
      }

      // جلب الخلفيات من الخادم
      final wallpapers = await _wallpaperService.fetchWallpapers();

      // جلب الفئات
      final categories = await _categoryService.fetchCategories();

      if (!mounted) return;

      setState(() {
        _wallpapers = wallpapers;
        _categories = categories;
        _isLoading = false;
        _hasMoreWallpapers = true; // إعادة تعيين حالة تحميل المزيد
      });

      // تخزين البيانات محلياً للاستخدام في المرة القادمة
      _cacheData(wallpapers, categories);
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');

      if (!mounted) return;

      // استعادة البيانات السابقة في حالة الخطأ
      if (currentWallpapers.isNotEmpty) {
        setState(() {
          _wallpapers = currentWallpapers;
          _categories = currentCategories;
          _isLoading = false;
        });

        // عرض رسالة خطأ للمستخدم
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'حدث خطأ أثناء تحديث البيانات. تم استعادة البيانات السابقة.',
            ),
            duration: Duration(seconds: 3),
          ),
        );
      } else if (_wallpapers.isNotEmpty) {
        // إذا كان لدينا بيانات مخزنة محلياً، نستخدمها
        setState(() {
          _isLoading = false;
        });
      } else {
        setState(() {
          _error =
              'حدث خطأ أثناء تحميل البيانات. يرجى التحقق من اتصالك بالإنترنت والمحاولة مرة أخرى.';
          _isLoading = false;
        });
      }
    }
  }

  // تحميل البيانات المخزنة محلياً
  Future<void> _loadCachedData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تحميل الخلفيات المخزنة
      final wallpapersJson = prefs.getString('cached_wallpapers');
      if (wallpapersJson != null && wallpapersJson.isNotEmpty) {
        try {
          final List<dynamic> jsonData = json.decode(wallpapersJson);
          final cachedWallpapers =
              jsonData.map((data) => Wallpaper.fromJson(data)).toList();

          // تحميل الفئات المخزنة
          final categoriesJson = prefs.getString('cached_categories');
          if (categoriesJson != null && categoriesJson.isNotEmpty) {
            try {
              final List<dynamic> jsonData = json.decode(categoriesJson);
              final cachedCategories =
                  jsonData.map((data) => Category.fromJson(data)).toList();

              if (!mounted) return;

              setState(() {
                _wallpapers = cachedWallpapers;
                _categories = cachedCategories;
              });
            } catch (e) {
              debugPrint('خطأ في تحليل بيانات الفئات المخزنة: $e');
              // حذف البيانات المخزنة غير الصالحة
              await prefs.remove('cached_categories');
            }
          }
        } catch (e) {
          debugPrint('خطأ في تحليل بيانات الخلفيات المخزنة: $e');
          // حذف البيانات المخزنة غير الصالحة
          await prefs.remove('cached_wallpapers');
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات المخزنة محلياً: $e');
    }
  }

  // تخزين البيانات محلياً
  Future<void> _cacheData(
    List<Wallpaper> wallpapers,
    List<Category> categories,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // تخزين الخلفيات
      final wallpapersJson = json.encode(
        wallpapers.map((w) => w.toJson()).toList(),
      );
      await prefs.setString('cached_wallpapers', wallpapersJson);

      // تخزين الفئات
      final categoriesJson = json.encode(
        categories.map((c) => c.toJson()).toList(),
      );
      await prefs.setString('cached_categories', categoriesJson);

      // تخزين وقت آخر تحديث
      await prefs.setString('last_updated', DateTime.now().toIso8601String());

      // تخزين الصور محلياً في الخلفية
      _cacheImages(wallpapers);
    } catch (e) {
      debugPrint('خطأ في تخزين البيانات محلياً: $e');
    }
  }

  // تخزين الصور محلياً
  Future<void> _cacheImages(List<Wallpaper> wallpapers) async {
    try {
      // تجميع روابط الصور
      final List<String> imageUrls = [];
      for (final wallpaper in wallpapers) {
        imageUrls.add(wallpaper.thumbnailUrl);
      }

      // تخزين الصور في الخلفية
      WallpaperCacheManager.cacheAllImages(imageUrls);
    } catch (e) {
      debugPrint('خطأ في تخزين الصور محلياً: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body:
          _isLoading
              ? _buildLoadingState()
              : _error.isNotEmpty
              ? _buildErrorState()
              : _buildContent(),
    );
  }

  Widget _buildLoadingState() {
    return const EnhancedLoadingScreen(
      loadingText: 'جاري تحميل الخلفيات...',
      progress: 0.5,
      showRefreshButton: false,
    );
  }

  Widget _buildErrorState() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: AppColors.backgroundGradient,
        ),
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة الخطأ
              Container(
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  color: AppColors.error.withAlpha(30),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.wifi_off_rounded,
                  color: AppColors.error,
                  size: 50,
                ),
              ),
              const SizedBox(height: 24),

              // عنوان الخطأ
              const Text(
                'لا يمكن تحميل الخلفيات',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // رسالة الخطأ
              Text(
                _error,
                style: const TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // زر إعادة المحاولة
              ElevatedButton.icon(
                onPressed: _loadData,
                icon: const Icon(Icons.refresh_rounded),
                label: const Text(
                  'إعادة المحاولة',
                  style: TextStyle(fontSize: 16),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Column(
      children: [
        // رسالة مخصصة من التحكم عن بعد
        if (_showMessage && _message.isNotEmpty)
          CustomMessageBanner(
            message: _message,
            onDismiss: () {
              setState(() {
                _showMessage = false;
              });
            },
            onTap: () {
              // يمكن إضافة إجراء عند النقر على الرسالة
              if (_remoteConfigService.config.additionalSettings.containsKey(
                'message_url',
              )) {
                final url =
                    _remoteConfigService
                        .config
                        .additionalSettings['message_url'];
                if (url != null && url.toString().isNotEmpty) {
                  // فتح الرابط
                  // يمكن استخدام url_launcher هنا
                }
              }
            },
          ),

        // تم إزالة إشعار تقييد الإعلانات

        // الجزء الثابت (العنوان والفئات وشريط التصفح)
        FixedHomeHeader(
          title: 'إسلامية',
          subtitle: 'اكتشف أجمل الخلفيات الإسلامية',
          onSubscribePressed: () => context.push('/subscription'),
          onViewAllPressed: () => context.push('/all-wallpapers'),
          onShowAllWallpapersPressed: () => context.push('/all-wallpapers'),
          categories: _categories,
          isLoading: _isLoading,
        ),

        // الجزء المتحرك (الخلفيات)
        Expanded(
          child: Container(
            decoration: const BoxDecoration(color: Colors.black),
            child: NotificationListener<ScrollNotification>(
              // تحسين أداء التمرير
              onNotification: (ScrollNotification notification) {
                // تحميل المزيد من الخلفيات عند الوصول إلى نهاية القائمة
                if (notification is ScrollEndNotification) {
                  if (notification.metrics.pixels >=
                          notification.metrics.maxScrollExtent * 0.8 &&
                      !_isLoading &&
                      !_isLoadingMore &&
                      _hasMoreWallpapers) {
                    _loadMoreWallpapers();
                  }
                }
                return false;
              },
              child: CustomRefreshIndicator(
                onRefresh: _loadData,
                color: AppColors.accent,
                refreshText: 'اسحب للتحديث',
                refreshingText: 'جاري تحديث الخلفيات...',
                refreshedText: 'تم تحديث الخلفيات',
                child: CustomScrollView(
                  physics: const BouncingScrollPhysics(
                    parent: AlwaysScrollableScrollPhysics(),
                  ),
                  cacheExtent: 1500, // تخزين مؤقت أكبر للعناصر
                  slivers: [
                    // إعلان بانر - يتم التحكم فيه من خلال التحكم عن بعد
                    SliverToBoxAdapter(
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 300),
                        height:
                            _remoteConfigService.config.showBannerAds ? 76 : 0,
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 300),
                          opacity:
                              _remoteConfigService.config.showBannerAds
                                  ? 1.0
                                  : 0.0,
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                            child: Container(
                              height: 60,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(30),
                                    blurRadius: 8,
                                    spreadRadius: 1,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child:
                                  _remoteConfigService.config.showBannerAds
                                      ? _adsManager.getBannerAdWidget() ??
                                          AdBanner(height: 60)
                                      : const SizedBox(),
                            ),
                          ),
                        ),
                      ),
                    ),

                    // شبكة الخلفيات مع هامش سفلي أقل لرفع شريط التنقل للأعلى
                    SliverPadding(
                      padding: const EdgeInsets.fromLTRB(16, 8, 16, 80),
                      sliver: _buildEnhancedWallpapersGrid(),
                    ),

                    // مؤشر تحميل المزيد
                    if (_isLoadingMore)
                      const SliverToBoxAdapter(
                        child: Padding(
                          padding: EdgeInsets.only(bottom: 32),
                          child: Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.accent,
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEnhancedWallpapersGrid() {
    return SliverMasonryGrid.count(
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      itemBuilder: (context, index) {
        if (index >= _wallpapers.length) {
          return const SizedBox.shrink();
        }

        final wallpaper = _wallpapers[index];

        // استخدام KeepAliveWrapper لتحسين الأداء
        return KeepAliveWrapper(
          child: WallpaperCard(
            wallpaper: wallpaper,
            borderRadius: AppDimensions.radiusMedium,
            showInfo: false, // إزالة العناوين وإظهار الإحصائيات فقط
            onTap: () {
              context.push('/wallpaper/${wallpaper.id}', extra: wallpaper);
            },
          ),
        );
      },
      childCount: _wallpapers.length,
    );
  }

  // تحميل المزيد من الخلفيات

  Future<void> _loadMoreWallpapers() async {
    if (_isLoadingMore || !_hasMoreWallpapers) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      // استخدام نسخة آمنة من الخدمة
      final moreWallpapers = await _wallpaperService.fetchMoreWallpapers(
        offset: _wallpapers.length,
        limit: 10,
      );

      if (!mounted) return;

      if (moreWallpapers.isEmpty) {
        setState(() {
          _hasMoreWallpapers = false;
          _isLoadingMore = false;
        });
      } else {
        setState(() {
          _wallpapers.addAll(moreWallpapers);
          _isLoadingMore = false;
        });

        // تخزين الصور الجديدة محليًا في عملية منفصلة
        Future.microtask(() {
          final imageUrls = moreWallpapers.map((w) => w.thumbnailUrl).toList();
          WallpaperCacheManager.cacheAllImages(imageUrls);
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل المزيد من الخلفيات: $e');
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }
}
