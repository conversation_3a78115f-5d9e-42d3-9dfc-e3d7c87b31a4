import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/gradients.dart';
import '../../../design_system/shadows.dart';
import '../../../design_system/typography.dart';
import '../../../data/models/subscription.dart';
import '../buttons/premium_button.dart';

/// بطاقة عرض الاشتراك
class SubscriptionCard extends StatelessWidget {
  final Subscription subscription;
  final VoidCallback? onSubscribe;
  final bool isSelected;
  final bool isLoading;
  
  const SubscriptionCard({
    super.key,
    required this.subscription,
    this.onSubscribe,
    this.isSelected = false,
    this.isLoading = false,
  });
  
  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        gradient: isSelected ? AppGradients.gold : null,
        color: isSelected ? null : AppColors.card,
        boxShadow: isSelected ? AppShadows.premium : AppShadows.card,
        border: isSelected
            ? Border.all(color: AppColors.premiumHighlight, width: 2)
            : subscription.isRecommended
                ? Border.all(color: AppColors.accent, width: 2)
                : null,
      ),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        child: InkWell(
          onTap: () {
            if (onSubscribe != null && !isLoading) {
              onSubscribe!();
            }
          },
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          splashColor: Colors.white12,
          highlightColor: Colors.white10,
          child: Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // علامة موصى به
                if (subscription.isRecommended && !isSelected)
                  Container(
                    margin: const EdgeInsets.only(bottom: AppDimensions.marginSmall),
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingSmall,
                      vertical: AppDimensions.paddingXSmall,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.accent,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                    ),
                    child: Text(
                      'موصى به',
                      style: AppTypography.labelSmall.copyWith(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                
                // اسم الاشتراك
                Text(
                  subscription.name,
                  style: AppTypography.titleLarge.copyWith(
                    color: isSelected ? Colors.black : AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                
                const SizedBox(height: AppDimensions.marginXSmall),
                
                // وصف الاشتراك
                Text(
                  subscription.description,
                  style: AppTypography.bodyMedium.copyWith(
                    color: isSelected ? Colors.black87 : AppColors.textSecondary,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                
                const SizedBox(height: AppDimensions.marginMedium),
                
                // السعر
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${subscription.price}',
                      style: AppTypography.displaySmall.copyWith(
                        color: isSelected ? Colors.black : AppColors.textPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: AppDimensions.marginXSmall),
                    Text(
                      subscription.currency,
                      style: AppTypography.bodyMedium.copyWith(
                        color: isSelected ? Colors.black87 : AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppDimensions.marginXSmall),
                
                // المدة
                Text(
                  _getDurationText(),
                  style: AppTypography.bodySmall.copyWith(
                    color: isSelected ? Colors.black87 : AppColors.textSecondary,
                  ),
                ),
                
                const Spacer(),
                
                // زر الاشتراك
                PremiumButton(
                  text: isSelected ? 'اشترك الآن' : 'اختر',
                  onPressed: onSubscribe,
                  isLoading: isLoading,
                  isFullWidth: true,
                  gradient: isSelected
                      ? LinearGradient(
                          colors: [Colors.black, Colors.black87],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        )
                      : AppGradients.gold,
                  textColor: isSelected ? Colors.white : Colors.black,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  String _getDurationText() {
    if (subscription.durationDays >= 365 * 5) {
      return 'مدى الحياة';
    } else if (subscription.durationDays >= 365) {
      final years = subscription.durationDays ~/ 365;
      return years == 1 ? 'سنة واحدة' : '$years سنوات';
    } else if (subscription.durationDays >= 30) {
      final months = subscription.durationDays ~/ 30;
      return months == 1 ? 'شهر واحد' : '$months أشهر';
    } else {
      return '${subscription.durationDays} يوم';
    }
  }
}
