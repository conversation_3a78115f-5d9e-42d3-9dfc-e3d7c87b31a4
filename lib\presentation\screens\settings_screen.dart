import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:fluttertoast/fluttertoast.dart';
import '../../design_system/colors.dart';
import '../../utils/back_button_handler.dart';
import '../../services/language_service.dart';
import '../../data/services/subscription_service.dart';
import '../../services/remote_config_service.dart';
import '../components/dialogs/update_dialog.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  String _downloadQuality = 'high';
  bool _isSubscribed = false;
  bool _isCheckingUpdate = false;
  String _currentVersion = '';
  String _latestVersion = '';
  bool _updateAvailable = false;
  final SubscriptionService _subscriptionService = SubscriptionService();

  @override
  void initState() {
    super.initState();
    _loadSettings();
    _checkSubscriptionStatus();
    _loadAppVersion();
    _checkForUpdateInBackground();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _downloadQuality = prefs.getString('download_quality') ?? 'high';
    });
  }

  Future<void> _checkSubscriptionStatus() async {
    try {
      final isSubscribed = await _subscriptionService.isSubscribed();
      setState(() {
        _isSubscribed = isSubscribed;
      });
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من حالة الاشتراك: $e');
    }
  }

  Future<void> _loadAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _currentVersion = packageInfo.version;
      });
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على إصدار التطبيق: $e');
    }
  }

  Future<void> _checkForUpdateInBackground() async {
    try {
      // تم تعطيل التحقق من التحديثات في الخلفية
      debugPrint('✅ تم تعطيل التحقق من التحديثات في الخلفية');
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من التحديثات: $e');
    }
  }

  Future<void> _checkForUpdate() async {
    if (_isCheckingUpdate) return;

    setState(() {
      _isCheckingUpdate = true;
    });

    try {
      Fluttertoast.showToast(
        msg: "جاري التحقق من وجود تحديثات...",
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
      );

      // استخدام خدمة التحكم عن بعد للتحقق من التحديثات
      final remoteConfigService = RemoteConfigService();

      // تحديث التكوين من الخادم
      await remoteConfigService.fetchConfig(
        "https://drive.google.com/uc?id=1mut1fYcZMmVFFBuFKjmTEwb-ds6KUnZM",
      );

      // التحقق من وجود تحديث
      if (remoteConfigService.needsUpdate) {
        // تحديث واجهة المستخدم
        setState(() {
          _updateAvailable = true;
          _latestVersion = remoteConfigService.config.appVersion;
        });

        // عرض مربع حوار التحديث
        if (mounted) {
          UpdateDialog.show(context);
        }

        Fluttertoast.showToast(
          msg: "يوجد تحديث جديد متاح!",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );
      } else {
        // لا يوجد تحديث
        Fluttertoast.showToast(
          msg: "التطبيق محدث بالفعل إلى أحدث إصدار",
          toastLength: Toast.LENGTH_LONG,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من التحديثات: $e');
      Fluttertoast.showToast(
        msg: "حدث خطأ أثناء التحقق من التحديثات",
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: Colors.red,
        textColor: Colors.white,
      );

      // في حالة الخطأ، نفتح متجر Google Play مباشرة
      const playStoreUrl =
          'https://play.google.com/store/apps/details?id=com.islamiclandporta.islam.allahwallpaper.ahmad.np';
      await _openPlayStore(playStoreUrl);
    } finally {
      setState(() {
        _isCheckingUpdate = false;
      });
    }
  }

  Future<void> _saveLanguage(String languageCode) async {
    // استخدام خدمة اللغة لتغيير اللغة
    final languageService = Provider.of<LanguageService>(
      context,
      listen: false,
    );
    final success = await languageService.changeLanguage(languageCode);

    if (success && mounted) {
      // إظهار رسالة نجاح تغيير اللغة
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ تفضيلات اللغة بنجاح'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _saveDownloadQuality(String quality) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('download_quality', quality);
    setState(() {
      _downloadQuality = quality;
    });
  }

  // الحصول على اللغة الحالية
  String get _language {
    final languageService = Provider.of<LanguageService>(
      context,
      listen: false,
    );
    return languageService.currentLocale.languageCode;
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler(
      routeToNavigateOnBack: '/home',
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: AppColors.backgroundGradient,
            ),
          ),
          child: Column(
            children: [
              // رأس الصفحة الثابت
              _buildFixedHeader(),

              // بانر معلومات (المربع الأحمر سابقاً)
              _buildInfoBanner(),

              // محتوى الإعدادات القابل للتمرير
              Expanded(
                child: CustomScrollView(
                  physics: const BouncingScrollPhysics(),
                  slivers: [
                    // محتوى الإعدادات مع هامش سفلي كبير لتجنب اختفاء العناصر تحت شريط التنقل
                    SliverPadding(
                      padding: const EdgeInsets.fromLTRB(16, 16, 16, 120),
                      sliver: SliverList(
                        delegate: SliverChildListDelegate([
                          // قسم الإعدادات العامة
                          _buildSectionHeader('إعدادات عامة'),
                          _buildSettingCard(
                            title: 'الوضع الداكن',
                            subtitle: 'الوضع الداكن مفعل افتراضياً',
                            icon: Icons.dark_mode,
                            trailing: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withAlpha(50),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'مفعل',
                                style: TextStyle(
                                  color: AppColors.primary,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                          _buildSettingCard(
                            title: 'اللغة',
                            subtitle: _language == 'ar' ? 'العربية' : 'English',
                            icon: Icons.language,
                            trailing: const Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: AppColors.textSecondary,
                            ),
                            onTap: _showLanguageDialog,
                          ),
                          _buildSettingCard(
                            title: 'جودة التحميل',
                            subtitle: _getDownloadQualityText(),
                            icon: Icons.high_quality,
                            trailing: const Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: AppColors.textSecondary,
                            ),
                            onTap: _showQualityDialog,
                          ),

                          // قسم الاشتراك
                          _buildSectionHeader('الاشتراك'),
                          _buildSettingCard(
                            title: 'إدارة الاشتراك',
                            subtitle:
                                _isSubscribed
                                    ? 'أنت مشترك في النسخة المميزة'
                                    : 'احصل على مميزات إضافية',
                            icon: Icons.workspace_premium,
                            iconColor: Colors.amber,
                            trailing: Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    _isSubscribed
                                        ? Colors.green.withAlpha(50)
                                        : Colors.amber.withAlpha(50),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                _isSubscribed ? 'مشترك' : 'ترقية',
                                style: TextStyle(
                                  color:
                                      _isSubscribed
                                          ? Colors.green
                                          : Colors.amber,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                            onTap: () {
                              context.push('/subscription');
                            },
                          ),

                          // قسم التطبيق
                          _buildSectionHeader('حول التطبيق'),

                          _buildSettingCard(
                            title: 'روابط مهمة',
                            subtitle:
                                'تقييم التطبيق، التواصل معنا، وروابط أخرى',
                            icon: Icons.link,
                            iconColor: Colors.lightBlueAccent,
                            trailing: const Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: AppColors.textSecondary,
                            ),
                            onTap: () {
                              context.push('/important-links');
                            },
                          ),
                          _buildSettingCard(
                            title: 'مشاركة التطبيق',
                            subtitle: 'مشاركة التطبيق مع الأصدقاء',
                            icon: Icons.share,
                            iconColor: Colors.teal,
                            trailing: const Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: AppColors.textSecondary,
                            ),
                            onTap: _shareApp,
                          ),
                          _buildSettingCard(
                            title: 'سياسة الخصوصية',
                            subtitle: 'قراءة سياسة الخصوصية الخاصة بالتطبيق',
                            icon: Icons.privacy_tip,
                            trailing: const Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: AppColors.textSecondary,
                            ),
                            onTap: () {
                              context.push('/privacy-policy');
                            },
                          ),
                          _buildSettingCard(
                            title: 'عن التطبيق',
                            subtitle: 'الإصدار 6.0.0',
                            icon: Icons.info,
                            trailing: const Icon(
                              Icons.arrow_forward_ios,
                              size: 16,
                              color: AppColors.textSecondary,
                            ),
                            onTap: () {
                              // فتح صفحة عن التطبيق
                              context.push('/about');
                            },
                          ),
                        ]),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // بناء رأس الصفحة الثابت
  Widget _buildFixedHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 16,
        bottom: 16,
        left: 16,
        right: 16,
      ),
      decoration: BoxDecoration(
        color: AppColors.background, // تغيير اللون ليتناسب مع خلفية التطبيق
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'الإعدادات',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'تخصيص تجربتك في التطبيق',
            style: TextStyle(color: Colors.white.withAlpha(204), fontSize: 14),
          ),
        ],
      ),
    );
  }

  // بناء بانر المعلومات (المربع الأحمر سابقاً)
  Widget _buildInfoBanner() {
    return GestureDetector(
      onTap: _checkForUpdate,
      child: Stack(
        children: [
          // الحاوية الرئيسية
          Container(
            width: double.infinity,
            margin: const EdgeInsets.fromLTRB(16, 6, 16, 6),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  const Color(0xFF0A0A0A), // لون داكن متناسق مع خلفية التطبيق
                  const Color(
                    0xFF151515,
                  ).withBlue(25), // لون داكن مع لمسة زرقاء خفيفة
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.primary.withAlpha(40),
                width: 0.8,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(40),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                // أيقونة التحديث
                Stack(
                  alignment: Alignment.center,
                  children: [
                    // دائرة خلفية متوهجة
                    Container(
                      width: 36,
                      height: 36,
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: [
                            AppColors.primary.withAlpha(40),
                            AppColors.primary.withAlpha(10),
                          ],
                          radius: 0.8,
                        ),
                        shape: BoxShape.circle,
                      ),
                    ),
                    // دائرة داخلية
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: AppColors.primary.withAlpha(15),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.primary.withAlpha(15),
                            blurRadius: 4,
                            spreadRadius: 0.5,
                          ),
                        ],
                      ),
                      child:
                          _isCheckingUpdate
                              ? SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.primary,
                                  ),
                                ),
                              )
                              : const Icon(
                                Icons.system_update_alt,
                                color: AppColors.primary,
                                size: 16,
                              ),
                    ),
                    // نقطة إشعار حمراء (تظهر فقط إذا كان هناك تحديث)
                    if (_updateAvailable)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                            border: Border.all(color: Colors.black, width: 0.5),
                          ),
                        ),
                      ),
                  ],
                ),
                const SizedBox(width: 12),

                // نص المعلومات
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            _updateAvailable
                                ? 'تحديث جديد متاح'
                                : 'التحقق من التحديثات',
                            style: const TextStyle(
                              fontSize: 13,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 4),
                          // علامة الإصدار (تظهر فقط إذا كان هناك تحديث)
                          if (_updateAvailable && _latestVersion.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                                vertical: 1,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green.withAlpha(50),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                'v$_latestVersion',
                                style: const TextStyle(
                                  fontSize: 9,
                                  color: Colors.green,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _updateAvailable
                            ? 'قم بتحديث التطبيق للحصول على أحدث الميزات'
                            : 'النسخة الحالية: $_currentVersion',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.white.withAlpha(170),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),

                // زر التحديث
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primary.withAlpha(80),
                        AppColors.primary.withAlpha(40),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withAlpha(20),
                        blurRadius: 2,
                        spreadRadius: 0,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Text(
                    _updateAvailable ? 'تحديث' : 'تحقق',
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // تأثير لامع (شبه شفاف) في الأعلى
          Positioned(
            top: 6,
            left: 16,
            right: 16,
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.white.withAlpha(0),
                    Colors.white.withAlpha(30),
                    Colors.white.withAlpha(0),
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // فتح متجر Google Play
  Future<void> _openPlayStore([String? customUrl]) async {
    final String playStoreUrl =
        customUrl ??
        'https://play.google.com/store/apps/details?id=com.islamiclandporta.islam.allahwallpaper.ahmad.np';
    try {
      final Uri uri = Uri.parse(playStoreUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        debugPrint('✅ تم فتح متجر Google Play بنجاح');
      } else {
        debugPrint('❌ لا يمكن فتح الرابط: $playStoreUrl');
      }
    } catch (e) {
      debugPrint('❌ خطأ في فتح متجر Google Play: $e');
    }
  }

  // مشاركة التطبيق - استخدام طريقة بديلة لتجنب مشكلة الشاشة السوداء
  Future<void> _shareApp() async {
    try {
      // نص المشاركة الثابت
      final String shareText =
          'Download the best Islamic Wallpapers app from Google Play Store: https://play.google.com/store/apps/details?id=com.islamiclandporta.islam.allahwallpaper.ahmad.np';

      // استخدام url_launcher بدلاً من SharePlus
      final Uri uri = Uri.parse(
        'https://api.whatsapp.com/send?text=${Uri.encodeComponent(shareText)}',
      );

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
        debugPrint('✅ تم فتح تطبيق المشاركة بنجاح');
      } else {
        // محاولة استخدام طريقة بديلة للمشاركة
        final Uri fallbackUri = Uri.parse(
          'https://play.google.com/store/apps/details?id=com.islamiclandporta.islam.allahwallpaper.ahmad.np',
        );
        if (await canLaunchUrl(fallbackUri)) {
          await launchUrl(fallbackUri, mode: LaunchMode.externalApplication);
          debugPrint('✅ تم فتح متجر Google Play بنجاح');
        } else {
          throw 'لا يمكن فتح تطبيق المشاركة أو متجر Google Play';
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في مشاركة التطبيق: $e');

      // عرض رسالة خطأ
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء مشاركة التطبيق'),
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
      child: Row(
        children: [
          Container(
            width: 4,
            height: 20,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withAlpha(150),
                    AppColors.primary.withAlpha(0),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String subtitle,
    required IconData icon,
    Color? iconColor,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: AppColors.surface.withAlpha(180),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.primary.withAlpha(30), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(30),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          splashColor: AppColors.primary.withAlpha(30),
          highlightColor: AppColors.primary.withAlpha(15),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                // أيقونة الإعداد
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: (iconColor ?? AppColors.primary).withAlpha(20),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      icon,
                      color: iconColor ?? AppColors.primary,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // معلومات الإعداد
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),

                // عنصر إضافي (مثل زر التبديل أو السهم)
                if (trailing != null) trailing,
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showLanguageDialog() {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            insetPadding: EdgeInsets.symmetric(
              horizontal: MediaQuery.of(context).size.width * 0.05,
              vertical: MediaQuery.of(context).size.height * 0.03,
            ),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              decoration: BoxDecoration(
                color: AppColors.surface,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.white.withAlpha(30), width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(70),
                    blurRadius: 15,
                    spreadRadius: 2,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // عنوان الصفحة
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(30),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withAlpha(50),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.language,
                            color: AppColors.primary,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'اختر اللغة',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // قائمة اللغات
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Column(
                      children: [
                        // العربية - السعودية
                        _buildLanguageOption(
                          languageCode: 'ar_sa',
                          languageName: 'العربية',
                          countryName: 'السعودية',
                          flagEmoji: '🇸🇦',
                        ),

                        const Divider(color: Colors.white10, height: 1),

                        // العربية - مصر
                        _buildLanguageOption(
                          languageCode: 'ar_eg',
                          languageName: 'العربية',
                          countryName: 'مصر',
                          flagEmoji: '🇪🇬',
                        ),

                        const Divider(color: Colors.white10, height: 1),

                        // العربية - الإمارات
                        _buildLanguageOption(
                          languageCode: 'ar_ae',
                          languageName: 'العربية',
                          countryName: 'الإمارات',
                          flagEmoji: '🇦🇪',
                        ),
                      ],
                    ),
                  ),

                  // زر الإغلاق
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: ElevatedButton(
                      onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 45),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('إغلاق'),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  // بناء خيار اللغة
  Widget _buildLanguageOption({
    required String languageCode,
    required String languageName,
    required String countryName,
    required String flagEmoji,
  }) {
    // استخدام خدمة اللغة للتحقق من اللغة الحالية
    final languageService = Provider.of<LanguageService>(
      context,
      listen: false,
    );
    final isSelected = languageService.isCurrentLanguage(
      languageCode.substring(0, 2),
    );

    return InkWell(
      onTap: () {
        final String langCode = languageCode.substring(0, 2);
        // استخدام context من الحوار نفسه
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
        _saveLanguage(langCode);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        color:
            isSelected ? AppColors.primary.withAlpha(30) : Colors.transparent,
        child: Row(
          children: [
            // علم الدولة
            Container(
              width: 40,
              height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: Colors.white.withAlpha(10),
                shape: BoxShape.circle,
              ),
              child: Text(flagEmoji, style: const TextStyle(fontSize: 24)),
            ),
            const SizedBox(width: 16),

            // معلومات اللغة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    languageName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    countryName,
                    style: const TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),

            // علامة الاختيار
            if (isSelected)
              const Icon(
                Icons.check_circle,
                color: AppColors.primary,
                size: 24,
              ),
          ],
        ),
      ),
    );
  }

  void _showQualityDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.surface,
            title: const Text(
              'جودة التحميل',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: const Text(
                    'عالية',
                    style: TextStyle(color: Colors.white),
                  ),
                  subtitle: const Text(
                    'حجم أكبر، جودة أفضل',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                  leading: Radio<String>(
                    value: 'high',
                    groupValue: _downloadQuality,
                    onChanged: (value) {
                      if (value != null) {
                        // حفظ القيمة ثم إغلاق الحوار
                        Navigator.pop(context);
                        _saveDownloadQuality(value);
                      }
                    },
                    activeColor: AppColors.primary,
                  ),
                ),
                ListTile(
                  title: const Text(
                    'متوسطة',
                    style: TextStyle(color: Colors.white),
                  ),
                  subtitle: const Text(
                    'حجم متوسط، جودة جيدة',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                  leading: Radio<String>(
                    value: 'medium',
                    groupValue: _downloadQuality,
                    onChanged: (value) {
                      if (value != null) {
                        // حفظ القيمة ثم إغلاق الحوار
                        Navigator.pop(context);
                        _saveDownloadQuality(value);
                      }
                    },
                    activeColor: AppColors.primary,
                  ),
                ),
                ListTile(
                  title: const Text(
                    'منخفضة',
                    style: TextStyle(color: Colors.white),
                  ),
                  subtitle: const Text(
                    'حجم صغير، توفير البيانات',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                  leading: Radio<String>(
                    value: 'low',
                    groupValue: _downloadQuality,
                    onChanged: (value) {
                      if (value != null) {
                        // حفظ القيمة ثم إغلاق الحوار
                        Navigator.pop(context);
                        _saveDownloadQuality(value);
                      }
                    },
                    activeColor: AppColors.primary,
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  String _getDownloadQualityText() {
    switch (_downloadQuality) {
      case 'high':
        return 'عالية';
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return 'عالية';
    }
  }
}
