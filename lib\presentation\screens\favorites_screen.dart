import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../design_system/colors.dart';
import '../../design_system/dimensions.dart';
import '../../design_system/typography.dart';
import '../../data/models/wallpaper.dart';
import '../../data/services/favorites_service.dart';
import '../../data/services/subscription_service.dart';
import '../../utils/back_button_handler.dart';
import '../../utils/number_formatter.dart';
import '../components/cards/wallpaper_card.dart';
import '../components/navigation/dark_app_header.dart';
import '../components/loaders/shimmer_loader.dart';
import '../components/loaders/custom_refresh_indicator.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  final FavoritesService _favoritesService = FavoritesService();
  final SubscriptionService _subscriptionService = SubscriptionService();
  List<Wallpaper> _favorites = [];
  bool _isLoading = true;
  bool _isPremiumUser = false;

  @override
  void initState() {
    super.initState();
    _loadFavorites();
    _checkSubscriptionStatus();
  }

  Future<void> _checkSubscriptionStatus() async {
    try {
      final isSubscribed = await _subscriptionService.isSubscribed();
      setState(() {
        _isPremiumUser = isSubscribed;
      });
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من حالة الاشتراك: $e');
    }
  }

  Future<void> _loadFavorites() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل المفضلة من الخدمة
      final favorites = await _favoritesService.getFavorites();

      setState(() {
        _favorites = favorites;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _favorites = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BackButtonHandler(
      routeToNavigateOnBack: '/home',
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: AppColors.backgroundGradient,
            ),
          ),
          child: SafeArea(
            child:
                _isLoading
                    ? _buildLoadingState()
                    : _favorites.isEmpty
                    ? _buildEmptyState()
                    : _buildContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return CustomScrollView(
      physics: const NeverScrollableScrollPhysics(),
      slivers: [
        // رأس الصفحة الثابت - تصميم محسن
        SliverPersistentHeader(
          delegate: DarkStickyHeader(
            title: 'المفضلة',
            subtitle:
                _isPremiumUser
                    ? 'مجموعة خلفياتك المفضلة المميزة'
                    : 'الخلفيات التي أعجبتك',
            accentColor: _isPremiumUser ? AppColors.accent : AppColors.primary,
            showBackButton: false,
          ),
          pinned: true,
        ),

        // حالة التحميل
        SliverPadding(
          padding: const EdgeInsets.all(16),
          sliver: SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              childAspectRatio: 0.75,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) =>
                  const WallpaperCardShimmer(height: 250, borderRadius: 16),
              childCount: 6,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return CustomScrollView(
      physics: const NeverScrollableScrollPhysics(),
      slivers: [
        // رأس الصفحة الثابت - تصميم محسن
        SliverPersistentHeader(
          delegate: DarkStickyHeader(
            title: 'المفضلة',
            subtitle:
                _isPremiumUser
                    ? 'مجموعة خلفياتك المفضلة المميزة'
                    : 'الخلفيات التي أعجبتك',
            accentColor: _isPremiumUser ? AppColors.accent : AppColors.primary,
            showBackButton: false,
          ),
          pinned: true,
        ),

        // حالة فارغة
        SliverFillRemaining(
          hasScrollBody: false,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة مع تأثير
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: AppColors.primary.withAlpha(20),
                    border: Border.all(
                      color: AppColors.primary.withAlpha(50),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withAlpha(20),
                        blurRadius: 15,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.favorite_border,
                    color: AppColors.primary,
                    size: 50,
                  ),
                ),
                const SizedBox(height: 24),

                // عنوان
                const Text(
                  'لا توجد خلفيات في المفضلة',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 12),

                // وصف
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    'أضف بعض الخلفيات إلى المفضلة لتظهر هنا. اضغط على أيقونة القلب في أي خلفية لإضافتها إلى المفضلة.',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(height: 32),

                // زر الاستكشاف
                Container(
                  width: 200,
                  height: 50,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withAlpha(70),
                        blurRadius: 10,
                        spreadRadius: 1,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppColors.primary,
                        AppColors.primary.withAlpha(200),
                      ],
                    ),
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => context.go('/home'),
                      borderRadius: BorderRadius.circular(15),
                      splashColor: Colors.white.withAlpha(50),
                      highlightColor: Colors.white.withAlpha(30),
                      child: Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(
                              Icons.explore,
                              color: Colors.white,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            const Text(
                              'استكشاف الخلفيات',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContent() {
    return RefreshIndicator(
      onRefresh: _loadFavorites,
      color: AppColors.primary,
      child: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // رأس الصفحة الثابت - تصميم محسن
          SliverPersistentHeader(
            delegate: DarkStickyHeader(
              title: 'المفضلة',
              subtitle:
                  _isPremiumUser
                      ? 'مجموعة خلفياتك المفضلة المميزة'
                      : 'الخلفيات التي أعجبتك',
              accentColor:
                  _isPremiumUser ? AppColors.accent : AppColors.primary,
              showBackButton: false,
            ),
            pinned: true,
          ),

          // معلومات المفضلة
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.surface.withAlpha(150),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: AppColors.primary.withAlpha(50),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: AppColors.primary.withAlpha(30),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.favorite,
                            color: AppColors.primary,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        const Text(
                          'الخلفيات المفضلة',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.accent.withAlpha(50),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${_favorites.length} خلفية',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      'يمكنك الوصول إلى خلفياتك المفضلة في أي وقت، حتى بدون اتصال بالإنترنت.',
                      style: TextStyle(color: Colors.white70, fontSize: 13),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // شبكة الخلفيات
          SliverPadding(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
            sliver: SliverAnimatedGrid(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childAspectRatio: 0.75,
              ),
              itemBuilder: (context, index, animation) {
                final wallpaper = _favorites[index];
                return FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.5),
                      end: Offset.zero,
                    ).animate(animation),
                    child: Stack(
                      children: [
                        WallpaperCard(
                          wallpaper: wallpaper,
                          showInfo: false,
                          borderRadius: 16,
                          isFavorite: true,
                          onFavoriteToggle: (isFav) async {
                            if (!isFav) {
                              // حفظ السياق
                              final scaffoldMessenger = ScaffoldMessenger.of(
                                context,
                              );

                              // إزالة من المفضلة
                              await _favoritesService.removeFromFavorites(
                                wallpaper.id,
                              );

                              // إعادة تحميل المفضلة
                              if (mounted) {
                                _loadFavorites();

                                // إظهار رسالة
                                scaffoldMessenger.showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'تمت إزالة الخلفية من المفضلة',
                                    ),
                                    backgroundColor: AppColors.surface,
                                    behavior: SnackBarBehavior.floating,
                                  ),
                                );
                              }
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                );
              },
              initialItemCount: _favorites.length,
            ),
          ),
        ],
      ),
    );
  }
}
