import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/gradients.dart';
import '../../../design_system/shadows.dart';
import '../../../constants/app_strings.dart';

/// رأس صفحة ثابت عند التمرير
class StickyAppHeader extends SliverPersistentHeaderDelegate {
  final String title;
  final String subtitle;
  final Color badgeColor;
  final VoidCallback? onPremiumPressed;
  final double collapsedHeight;
  final double expandedHeight;

  StickyAppHeader({
    required this.title,
    required this.subtitle,
    this.badgeColor = AppColors.accent,
    this.onPremiumPressed,
    this.collapsedHeight = 70,
    this.expandedHeight = 120,
  });

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    final double percentage = shrinkOffset / maxExtent;
    final double opacity = 1 - percentage;

    return Container(
      color: AppColors.background,
      child: Stack(
        fit: StackFit.expand,
        children: [
          // خلفية متدرجة محسنة
          Opacity(
            opacity: opacity.clamp(0, 1),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    AppColors.primary.withAlpha(40),
                    AppColors.background,
                  ],
                ),
              ),
            ),
          ),

          // رأس الصفحة
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: collapsedHeight + MediaQuery.of(context).padding.top,
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top,
                left: AppDimensions.paddingMedium,
                right: AppDimensions.paddingMedium,
              ),
              child: Row(
                textDirection: TextDirection.rtl, // RTL layout for Arabic
                children: [
                  // زر الاشتراك المميز
                  GestureDetector(
                    onTap:
                        onPremiumPressed ?? () => context.push('/subscription'),
                    child: _buildPremiumButton(),
                  ),

                  const SizedBox(width: AppDimensions.marginMedium),

                  // عنوان الصفحة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          title,
                          style: AppTypography.headingMedium,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),

                        // العنوان الفرعي (يظهر فقط عند التمدد)
                        AnimatedOpacity(
                          duration: const Duration(milliseconds: 200),
                          opacity: opacity.clamp(0, 1),
                          child: Transform.translate(
                            offset: Offset(0, 10 * percentage),
                            child: Text(
                              subtitle,
                              style: AppTypography.bodySmall.copyWith(
                                color: AppColors.textSecondary,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // تم حذف شريط البحث
        ],
      ),
    );
  }

  Widget _buildPremiumButton() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
        vertical: AppDimensions.paddingXSmall,
      ),
      decoration: BoxDecoration(
        gradient: AppGradients.gold,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: AppColors.premiumColor.withAlpha(77), // 0.3 * 255 = 77
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(Icons.workspace_premium, color: Colors.black, size: 18),
          const SizedBox(width: 4),
          Text(
            'مميز',
            style: AppTypography.labelMedium.copyWith(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  @override
  double get maxExtent => expandedHeight + 24; // 24 is an approximate value for status bar height

  @override
  double get minExtent => collapsedHeight + 24; // 24 is an approximate value for status bar height

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
