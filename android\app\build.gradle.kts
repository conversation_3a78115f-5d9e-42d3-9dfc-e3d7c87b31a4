plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.islamiclandporta.islam.allahwallpaper.ahmad.np"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973" // تحديث إصدار NDK للتوافق مع المكتبات

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
        // تفعيل desugaring لدعم ميزات Java 8 في الأجهزة القديمة
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
    }

    // إعدادات NDK لحل مشاكل المكتبات الأصلية
    defaultConfig {
        // تحديد المعماريات المدعومة لتجنب مشاكل libMGcgl.so
        ndk {
            abiFilters.addAll(listOf("arm64-v8a", "armeabi-v7a"))
        }
    }

    // إعدادات التعبئة لحل مشاكل split APK
    packagingOptions {
        // استبعاد المكتبات المتضاربة
        excludes.addAll(listOf(
            "META-INF/DEPENDENCIES",
            "META-INF/LICENSE",
            "META-INF/LICENSE.txt",
            "META-INF/license.txt",
            "META-INF/NOTICE",
            "META-INF/NOTICE.txt",
            "META-INF/notice.txt",
            "META-INF/ASL2.0",
            "META-INF/*.kotlin_module"
        ))

        // دمج المكتبات المتشابهة
        pickFirsts.addAll(listOf(
            "**/libc++_shared.so",
            "**/libfbjni.so"
        ))
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.islamiclandporta.islam.allahwallpaper.ahmad.np"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdk = 21 // تحديد الحد الأدنى لإصدار SDK لدعم المكتبات المستخدمة
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig = signingConfigs.getByName("debug")

            // تفعيل ProGuard لتحسين الأداء وتقليل حجم التطبيق
            isMinifyEnabled = true
            isShrinkResources = true
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }

        debug {
            // تحسينات للتطوير لتجنب ANR
            isDebuggable = true
            isMinifyEnabled = false
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // إضافة مكتبة desugaring لدعم ميزات Java 8 في الأجهزة القديمة
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")

    // إضافة مكتبة SplashScreen للتحكم في شاشة البداية
    implementation("androidx.core:core-splashscreen:1.0.1")

    // إضافة تبعيات Google Mobile Ads بشكل صريح لحل مشاكل البناء
    implementation("com.google.android.gms:play-services-ads:22.6.0")
    implementation("androidx.work:work-runtime:2.8.1")

    // تبعيات إضافية لحل مشاكل التوافق
    implementation("androidx.lifecycle:lifecycle-process:2.6.2")
    implementation("androidx.startup:startup-runtime:1.1.1")
}
