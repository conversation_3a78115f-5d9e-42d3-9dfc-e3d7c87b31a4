import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'logger.dart';

class ErrorHandler {
  // التحقق من حالة الاتصال بالإنترنت
  static Future<bool> isConnected() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      if (connectivityResults.isEmpty ||
          connectivityResults.contains(ConnectivityResult.none)) {
        Logger.info('لا يوجد اتصال بالإنترنت - حسب Connectivity');
        return false;
      }

      // التحقق من الاتصال الفعلي بالإنترنت
      try {
        final result = await InternetAddress.lookup('google.com');
        final isConnected =
            result.isNotEmpty && result[0].rawAddress.isNotEmpty;
        Logger.info('نتيجة فحص الاتصال بالإنترنت: $isConnected');
        return isConnected;
      } on SocketException catch (e) {
        Logger.error('خطأ في الاتصال بالإنترنت: ${e.message}');
        return false;
      }
    } catch (e) {
      Logger.error('خطأ في فحص الاتصال بالإنترنت: $e');
      return false;
    }
  }

  // مراقبة حالة الاتصال بالإنترنت
  static Stream<List<ConnectivityResult>> connectivityStream() {
    return Connectivity().onConnectivityChanged;
  }

  // الحصول على حالة الاتصال الحالية كقيمة واحدة (للتوافق مع الكود القديم)
  static Future<ConnectivityResult> getCurrentConnectivity() async {
    final results = await Connectivity().checkConnectivity();
    // إذا كانت القائمة فارغة أو تحتوي على نتيجة واحدة فقط بقيمة none، نرجع none
    if (results.isEmpty || results.contains(ConnectivityResult.none)) {
      return ConnectivityResult.none;
    }
    // إرجاع أول نتيجة إذا كانت هناك أي اتصال متاح
    return results.first;
  }

  // عرض رسالة خطأ
  static void showErrorSnackBar(BuildContext context, String message) {
    Logger.error('عرض رسالة خطأ: $message');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'إعادة المحاولة',
          textColor: Colors.white,
          onPressed: () {
            // يمكن إضافة إجراء إعادة المحاولة هنا
            Logger.info('تم النقر على زر إعادة المحاولة');
          },
        ),
      ),
    );
  }

  // عرض رسالة عدم وجود اتصال بالإنترنت
  static void showNoInternetSnackBar(
    BuildContext context, {
    VoidCallback? onRetry,
  }) {
    Logger.warning('عرض رسالة عدم وجود اتصال بالإنترنت');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('لا يوجد اتصال بالإنترنت'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'إعادة المحاولة',
          textColor: Colors.white,
          onPressed: () {
            Logger.info('تم النقر على زر إعادة المحاولة بعد فقدان الاتصال');
            if (onRetry != null) {
              onRetry();
            }
          },
        ),
      ),
    );
  }

  // التحقق من صلاحية رابط الصورة
  static Future<bool> isValidImageUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      final request = await HttpClient().headUrl(uri);
      final response = await request.close();
      final isValid = response.statusCode >= 200 && response.statusCode < 300;
      Logger.info('التحقق من صلاحية رابط الصورة $url: $isValid');
      return isValid;
    } catch (e) {
      Logger.error('خطأ في التحقق من صلاحية رابط الصورة $url: $e');
      return false;
    }
  }

  // معالجة أخطاء التحميل
  static Future<T> handleNetworkOperation<T>(
    Future<T> Function() operation,
    BuildContext context, {
    T? defaultValue,
    VoidCallback? onRetry,
    String? operationName,
  }) async {
    final opName = operationName ?? 'عملية شبكة';
    Logger.info('بدء $opName');

    try {
      if (!await isConnected()) {
        Logger.warning('$opName: لا يوجد اتصال بالإنترنت');
        showNoInternetSnackBar(context, onRetry: onRetry);
        return defaultValue as T;
      }

      final result = await operation();
      Logger.info('$opName: تمت بنجاح');
      return result;
    } on SocketException catch (e) {
      Logger.error('$opName: خطأ في الاتصال - ${e.message}');
      showErrorSnackBar(context, 'خطأ في الاتصال');
      return defaultValue as T;
    } on TimeoutException catch (e) {
      Logger.error('$opName: انتهت مهلة الطلب - $e');
      showErrorSnackBar(context, 'انتهت مهلة الطلب');
      return defaultValue as T;
    } catch (e) {
      Logger.error('$opName: خطأ غير متوقع - $e');
      showErrorSnackBar(context, 'خطأ غير متوقع: $e');
      return defaultValue as T;
    }
  }

  // معالجة أخطاء حفظ الملفات
  static Future<bool> handleFileOperation(
    Future<bool> Function() operation,
    BuildContext context, {
    String? operationName,
  }) async {
    final opName = operationName ?? 'عملية ملف';
    Logger.info('بدء $opName');

    try {
      final result = await operation();
      Logger.info('$opName: تمت بنجاح');
      return result;
    } catch (e) {
      Logger.error('$opName: خطأ - $e');
      showErrorSnackBar(context, 'خطأ في عملية الملف: $e');
      return false;
    }
  }

  // تسجيل الأخطاء غير المتوقعة
  static void logUnexpectedError(
    dynamic error, {
    StackTrace? stackTrace,
    String? context,
  }) {
    final contextInfo = context != null ? '[$context] ' : '';
    Logger.error('${contextInfo}خطأ غير متوقع: $error');

    if (stackTrace != null) {
      Logger.error('تفاصيل الخطأ: $stackTrace');
    }
  }

  // إعداد معالج الأخطاء العام
  static void setupGlobalErrorHandling() {
    // التقاط الأخطاء غير المعالجة في Flutter
    FlutterError.onError = (FlutterErrorDetails details) {
      try {
        Logger.error('خطأ في Flutter: ${details.exception}');
        if (details.stack != null) {
          Logger.error('تفاصيل الخطأ: ${details.stack}');
        }

        // معالجة خاصة لأخطاء ViewGroup
        if (details.exception.toString().contains(
          'offsetRectBetweenParentAndChild',
        )) {
          Logger.warning(
            'تم اكتشاف خطأ ViewGroup - سيتم تجاهله لتجنب تعطل التطبيق',
          );
          return;
        }

        // معالجة خاصة لأخطاء DartMessenger
        if (details.exception.toString().contains('DartMessenger') ||
            details.exception.toString().contains('Reply.reply')) {
          Logger.warning(
            'تم اكتشاف خطأ DartMessenger - سيتم تجاهله لتجنب تعطل التطبيق',
          );
          return;
        }

        // معالجة خاصة لأخطاء IllegalArgumentException
        if (details.exception is ArgumentError ||
            details.exception.toString().contains('IllegalArgumentException')) {
          Logger.warning(
            'تم اكتشاف خطأ في المعاملات - سيتم تجاهله لتجنب تعطل التطبيق',
          );
          return;
        }
      } catch (e) {
        // تجنب حلقة لا نهائية من الأخطاء
        debugPrint('خطأ في معالج الأخطاء العام: $e');
      }
    };

    // التقاط الأخطاء غير المعالجة في Dart
    PlatformDispatcher.instance.onError = (error, stack) {
      try {
        Logger.error('خطأ غير معالج في Dart: $error');
        Logger.error('Stack trace: $stack');

        // إرجاع true لمنع تعطل التطبيق
        return true;
      } catch (e) {
        debugPrint('خطأ في معالج أخطاء PlatformDispatcher: $e');
        return true;
      }
    };
  }
}
