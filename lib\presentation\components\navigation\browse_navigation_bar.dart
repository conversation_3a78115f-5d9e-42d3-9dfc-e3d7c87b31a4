import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/dimensions.dart';
import '../effects/glass_container.dart';

/// شريط تصفح محسن للخلفيات - تصميم احترافي موحد
class BrowseNavigationBar extends StatelessWidget {
  final VoidCallback onViewAllPressed;
  final VoidCallback onShowAllWallpapersPressed;
  final bool isGlass;
  final bool isFixed;

  const BrowseNavigationBar({
    super.key,
    required this.onViewAllPressed,
    required this.onShowAllWallpapersPressed,
    this.isGlass = false,
    this.isFixed = true,
  });

  @override
  Widget build(BuildContext context) {
    // استخدام نفس الوظيفة لكلا الزرين
    void handlePress() {
      // استدعاء نفس الوظيفة لتوحيد السلوك
      onShowAllWallpapersPressed();
    }

    final content = Container(
      height: 50, // تقليل الارتفاع
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: handlePress,
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          child: Ink(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.black.withAlpha(80),
                  Colors.black.withAlpha(50),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
              border: Border.all(color: Colors.white.withAlpha(40), width: 1),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(30),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Container(
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min, // جعل الصف بأصغر حجم ممكن
                textDirection: TextDirection.rtl,
                children: [
                  // أيقونة الشبكة
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(15),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.grid_view_rounded,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  // نص الزر
                  Text(
                    'جميع الخلفيات',
                    style: AppTypography.titleMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 15,
                      letterSpacing: 0.2,
                      height: 1.1,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // أيقونة السهم
                  Container(
                    padding: const EdgeInsets.all(3),
                    decoration: BoxDecoration(
                      color: Colors.white.withAlpha(15),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: Colors.white,
                      size: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );

    if (isGlass) {
      return GlassContainer(
        borderRadius: AppDimensions.radiusLarge,
        blur: 12,
        opacity: 0.75,
        child: content,
      );
    }

    return content;
  }
}
