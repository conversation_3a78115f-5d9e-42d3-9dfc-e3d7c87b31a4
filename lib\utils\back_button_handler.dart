import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../design_system/colors.dart';
import '../presentation/dialogs/exit_dialog.dart';

/// مكون للتعامل مع زر الرجوع في التطبيق
class BackButtonHandler extends StatelessWidget {
  final Widget child;
  final bool isRootScreen;
  final String? routeToNavigateOnBack;

  const BackButtonHandler({
    super.key,
    required this.child,
    this.isRootScreen = false,
    this.routeToNavigateOnBack,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !isRootScreen,
      onPopInvoked: (didPop) async {
        if (didPop) {
          return;
        }

        if (isRootScreen) {
          // عرض مربع حوار تأكيد الخروج من التطبيق
          final shouldPop = await _showExitConfirmationDialog(context);
          if (shouldPop) {
            // الخروج من التطبيق - استخدام SystemNavigator بدلاً من Navigator.pop
            // لتجنب خطأ "popped the last page off the stack"
            SystemNavigator.pop();
          }
        } else if (routeToNavigateOnBack != null) {
          // الانتقال إلى المسار المحدد بدون تحديث الصفحة
          if (context.mounted) {
            // استخدام go بدلاً من push للانتقال بدون تحديث الصفحة
            context.go(routeToNavigateOnBack!);
          }
        }
      },
      child: child,
    );
  }

  /// عرض مربع حوار تأكيد الخروج من التطبيق
  Future<bool> _showExitConfirmationDialog(BuildContext context) async {
    return await showExitDialog(context);
  }
}
