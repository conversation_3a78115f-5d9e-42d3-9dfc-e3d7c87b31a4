import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/dimensions.dart';

/// زر مميز بتصميم ذهبي
class PremiumButton extends StatelessWidget {
  final String text;
  final IconData? icon;
  final VoidCallback? onPressed;
  final double height;
  final double? width;
  final bool isSmall;
  final bool isOutlined;
  final bool isAnimated;
  final bool isLoading;
  final bool isFullWidth;
  final Gradient? gradient;
  final Color? textColor;

  const PremiumButton({
    super.key,
    required this.text,
    this.icon,
    this.onPressed,
    this.height = 48,
    this.width,
    this.isSmall = false,
    this.isOutlined = false,
    this.isAnimated = true,
    this.isLoading = false,
    this.isFullWidth = false,
    this.gradient,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle =
        isOutlined
            ? _buildOutlinedButton(context)
            : _buildFilledButton(context);

    if (isAnimated) {
      return TweenAnimationBuilder<double>(
        tween: Tween<double>(begin: 1.0, end: onPressed == null ? 0.8 : 1.0),
        duration: const Duration(milliseconds: 200),
        builder: (context, value, child) {
          return Transform.scale(scale: value, child: child);
        },
        child: buttonStyle,
      );
    }

    return buttonStyle;
  }

  Widget _buildContent() {
    final Color iconColor =
        textColor ?? (isOutlined ? AppColors.accent : Colors.black);
    final Color textContentColor =
        textColor ?? (isOutlined ? AppColors.accent : Colors.black);

    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (icon != null) ...[
          Icon(icon, color: iconColor, size: isSmall ? 16 : 20),
          SizedBox(width: isSmall ? 4 : 8),
        ],
        Text(
          text,
          style: (isSmall
                  ? AppTypography.labelMedium
                  : AppTypography.labelLarge)
              .copyWith(color: textContentColor, fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  Widget _buildFilledButton(BuildContext context) {
    return InkWell(
      onTap: isLoading ? null : onPressed,
      borderRadius: BorderRadius.circular(
        isSmall ? AppDimensions.radiusSmall : AppDimensions.radiusMedium,
      ),
      child: Container(
        height: height,
        width: isFullWidth ? double.infinity : width,
        padding: EdgeInsets.symmetric(
          horizontal:
              isSmall
                  ? AppDimensions.paddingMedium
                  : AppDimensions.paddingLarge,
        ),
        decoration: BoxDecoration(
          gradient:
              gradient ??
              const LinearGradient(
                colors: [Color(0xFFD4AF37), Color(0xFFFFD700)],
              ),
          borderRadius: BorderRadius.circular(
            isSmall ? AppDimensions.radiusSmall : AppDimensions.radiusMedium,
          ),
          boxShadow:
              onPressed != null && !isLoading
                  ? [
                    BoxShadow(
                      color: const Color(
                        0xFFD4AF37,
                      ).withAlpha(76), // 0.3 * 255 = 76
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                  : null,
        ),
        child: Center(
          child:
              isLoading
                  ? SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        textColor ?? Colors.black,
                      ),
                    ),
                  )
                  : _buildContent(),
        ),
      ),
    );
  }

  Widget _buildOutlinedButton(BuildContext context) {
    return InkWell(
      onTap: isLoading ? null : onPressed,
      borderRadius: BorderRadius.circular(
        isSmall ? AppDimensions.radiusSmall : AppDimensions.radiusMedium,
      ),
      child: Container(
        height: height,
        width: isFullWidth ? double.infinity : width,
        padding: EdgeInsets.symmetric(
          horizontal:
              isSmall
                  ? AppDimensions.paddingMedium
                  : AppDimensions.paddingLarge,
        ),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(
            isSmall ? AppDimensions.radiusSmall : AppDimensions.radiusMedium,
          ),
          border: Border.all(color: textColor ?? AppColors.accent, width: 2),
        ),
        child: Center(
          child:
              isLoading
                  ? SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        textColor ?? AppColors.accent,
                      ),
                    ),
                  )
                  : _buildContent(),
        ),
      ),
    );
  }
}
