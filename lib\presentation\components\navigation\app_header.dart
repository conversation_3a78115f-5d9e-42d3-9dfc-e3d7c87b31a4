import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../buttons/circular_icon_button.dart';
import '../buttons/subscription_button.dart';
import '../effects/glass_container.dart';

/// رأس الصفحة المخصص
class AppHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool isFixed;
  final bool isTransparent;
  final bool showBackButton;
  final bool showPremiumButton;
  final VoidCallback? onPremiumPressed;
  final List<Widget>? actions;
  final double height;
  final Color? backgroundColor;
  final Gradient? gradient;

  const AppHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.isFixed = false,
    this.isTransparent = false,
    this.showBackButton = false,
    this.showPremiumButton = true,
    this.onPremiumPressed,
    this.actions,
    this.height = 80,
    this.backgroundColor,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    if (isFixed) {
      return _buildFixedHeader(context);
    } else {
      return _buildScrollableHeader(context);
    }
  }

  Widget _buildScrollableHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(
        AppDimensions.paddingMedium,
        AppDimensions.paddingMedium,
        AppDimensions.paddingMedium,
        AppDimensions.paddingLarge,
      ),
      child: _buildHeaderContent(context),
    );
  }

  Widget _buildFixedHeader(BuildContext context) {
    if (isTransparent) {
      return Container(
        height: height,
        padding: EdgeInsets.only(
          left: AppDimensions.paddingMedium,
          right: AppDimensions.paddingMedium,
          top: MediaQuery.of(context).padding.top,
        ),
        child: _buildHeaderContent(context),
      );
    }

    return Container(
      height: height + MediaQuery.of(context).padding.top,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.background,
        gradient: gradient,
      ),
      padding: EdgeInsets.only(
        left: AppDimensions.paddingMedium,
        right: AppDimensions.paddingMedium,
        top: MediaQuery.of(context).padding.top,
      ),
      child: _buildHeaderContent(context),
    );
  }

  Widget _buildHeaderContent(BuildContext context) {
    return Row(
      textDirection: TextDirection.rtl, // RTL layout for Arabic
      children: [
        // زر الاشتراك المميز
        if (showPremiumButton)
          GestureDetector(
            onTap: onPremiumPressed ?? () => context.push('/subscription'),
            child: _buildPremiumButton(),
          ),

        // أزرار إضافية
        if (actions != null) ...actions!,

        const SizedBox(width: AppDimensions.marginMedium),

        // عنوان الصفحة
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title,
                style: AppTypography.headingMedium,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 2),
                Text(
                  subtitle!,
                  style: AppTypography.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),

        // زر الرجوع
        if (showBackButton) ...[
          const SizedBox(width: AppDimensions.marginSmall),
          CircularIconButton(
            icon: Icons.arrow_forward_ios,
            size: 40,
            backgroundColor: AppColors.surface,
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ],
    );
  }

  Widget _buildPremiumButton() {
    return SubscriptionButton(isCompact: true, text: 'مميز', height: 40);
  }
}

/// رأس صفحة زجاجي
class GlassAppHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool showBackButton;
  final bool showPremiumButton;
  final VoidCallback? onPremiumPressed;
  final List<Widget>? actions;
  final double height;

  const GlassAppHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.showBackButton = false,
    this.showPremiumButton = true,
    this.onPremiumPressed,
    this.actions,
    this.height = 80,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height + MediaQuery.of(context).padding.top,
      margin: const EdgeInsets.fromLTRB(
        AppDimensions.marginMedium,
        AppDimensions.marginMedium,
        AppDimensions.marginMedium,
        0,
      ),
      child: GlassContainer(
        borderRadius: AppDimensions.radiusLarge,
        padding: EdgeInsets.only(
          left: AppDimensions.paddingMedium,
          right: AppDimensions.paddingMedium,
          top: MediaQuery.of(context).padding.top,
        ),
        child: AppHeader(
          title: title,
          subtitle: subtitle,
          isFixed: true,
          isTransparent: true,
          showBackButton: showBackButton,
          showPremiumButton: showPremiumButton,
          onPremiumPressed: onPremiumPressed,
          actions: actions,
          height: height,
        ),
      ),
    );
  }
}
