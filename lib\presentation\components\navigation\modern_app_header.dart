import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../effects/glass_container.dart';
import '../buttons/premium_button.dart';
import '../buttons/subscription_button.dart';

/// رأس صفحة حديث وثابت عند التمرير
class ModernAppHeader extends SliverPersistentHeaderDelegate {
  final String title;
  final String subtitle;
  final Color accentColor;
  final VoidCallback? onPremiumPressed;
  final double collapsedHeight;
  final double expandedHeight;
  final bool showBackButton;
  final String? backRoute;
  final Widget? leadingIcon;
  final List<Widget>? actions;

  ModernAppHeader({
    required this.title,
    required this.subtitle,
    this.accentColor = AppColors.accent,
    this.onPremiumPressed,
    this.collapsedHeight = 70,
    this.expandedHeight = 120,
    this.showBackButton = false,
    this.backRoute,
    this.leadingIcon,
    this.actions,
  });

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    final double percentage = shrinkOffset / maxExtent;
    final double opacity = 1 - percentage;
    final double scale = 1 - (percentage * 0.3);

    return Container(
          color: Colors.transparent,
          child: Stack(
            fit: StackFit.expand,
            children: [
              // خلفية زجاجية متدرجة
              Positioned.fill(
                child: GlassContainer(
                  blur: 10,
                  opacity: 0.7 + (percentage * 0.3),
                  customBorderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(24 * (1 - percentage)),
                    bottomRight: Radius.circular(24 * (1 - percentage)),
                  ),
                  backgroundColor: AppColors.primary.withAlpha(40),
                  border: 0,
                  child: Container(),
                ),
              ),

              // خط مضيء في الأسفل
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 1,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        accentColor.withAlpha(0),
                        accentColor.withAlpha((100 * percentage).toInt()),
                        accentColor.withAlpha(0),
                      ],
                      stops: const [0.0, 0.5, 1.0],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                  ),
                ),
              ),

              // محتوى الرأس
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: collapsedHeight + MediaQuery.of(context).padding.top,
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top,
                    left: 16,
                    right: 16,
                  ),
                  child: Row(
                    textDirection: TextDirection.rtl, // RTL layout for Arabic
                    children: [
                      // زر الاشتراك المميز
                      if (actions != null) ...actions!,

                      if (actions == null)
                        GestureDetector(
                          onTap:
                              onPremiumPressed ??
                              () => context.push('/subscription'),
                          child: _buildPremiumButton(percentage),
                        ),

                      const SizedBox(width: 16),

                      // عنوان الصفحة
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // العنوان الرئيسي
                            Transform.scale(
                              scale: scale,
                              alignment: Alignment.centerRight,
                              child: Text(
                                title,
                                style: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                  letterSpacing: 0.5,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),

                            // العنوان الفرعي (يظهر فقط عند التمدد)
                            AnimatedOpacity(
                              duration: const Duration(milliseconds: 200),
                              opacity: opacity.clamp(0, 1),
                              child: Transform.translate(
                                offset: Offset(0, 10 * percentage),
                                child: Text(
                                  subtitle,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.white70,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // زر الرجوع
                      if (showBackButton)
                        Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: _buildBackButton(context),
                        ),

                      // أيقونة مخصصة
                      if (leadingIcon != null && !showBackButton)
                        Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: leadingIcon!,
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        )
        .animate()
        .fadeIn(duration: 600.ms, curve: Curves.easeOutQuad)
        .slideY(
          begin: -0.1,
          end: 0,
          duration: 600.ms,
          curve: Curves.easeOutQuad,
        );
  }

  Widget _buildPremiumButton(double percentage) {
    return SubscriptionButton(isCompact: true, text: 'مميز', height: 40)
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .shimmer(duration: 2.seconds, color: Colors.white.withAlpha(100));
  }

  Widget _buildBackButton(BuildContext context) {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(20),
        shape: BoxShape.circle,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (backRoute != null) {
              context.go(backRoute!);
            } else {
              Navigator.pop(context);
            }
          },
          customBorder: const CircleBorder(),
          splashColor: accentColor.withAlpha(50),
          highlightColor: accentColor.withAlpha(30),
          child: const Icon(
            Icons.arrow_back_ios_new,
            color: Colors.white,
            size: 20,
          ),
        ),
      ),
    );
  }

  @override
  double get maxExtent => expandedHeight + 24; // 24 is an approximate value for status bar height

  @override
  double get minExtent => collapsedHeight + 24; // 24 is an approximate value for status bar height

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
