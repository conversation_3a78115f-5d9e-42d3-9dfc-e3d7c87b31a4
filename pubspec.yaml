name: islamic_wallpapers
description: "تطبيق خلفيات إسلامية جميلة ومتنوعة"
publish_to: 'none'
version: 6.0.0+49

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  # مكتبة السبلاش سكرين للتوافق مع الإصدارات القديمة
  flutter_native_splash: ^2.3.10
  flutter_localizations:
    sdk: flutter

  # Icons and UI
  cupertino_icons: ^1.0.8
  phosphor_flutter: ^2.0.1
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.9
  lottie: ^3.3.1
  shimmer: ^3.0.0
  flutter_animate: ^4.5.2
  glassmorphism: ^3.0.0
  blur: ^4.0.2

  # Layout and Grid
  flutter_staggered_grid_view: ^0.7.0
  # استخدام flutter_staggered_grid_view بدلاً من flutter_masonry_view

  # Network and API
  http: ^1.1.2
  dio: ^5.4.0
  cached_network_image: ^3.3.1
  connectivity_plus: ^6.1.4

  # State Management
  provider: ^6.1.1
  equatable: ^2.0.5

  # Navigation
  go_router: ^15.1.1

  # Storage and Preferences
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path_provider: ^2.1.2

  # Utilities
  permission_handler: ^12.0.0+1
  flutter_cache_manager: ^3.3.1
  package_info_plus: ^8.3.0
  device_info_plus: ^11.4.0
  share_plus: ^11.0.0
  url_launcher: ^6.2.5
  intl: ^0.19.0
  fluttertoast: ^8.2.4
  # image_gallery_saver: ^2.0.3 # Comentado debido a problemas de compatibilidad
  path: ^1.8.3
# workmanager: ^0.5.2 # Comentado temporalmente debido a problemas de compatibilidad
# flutter_wallpaper_manager: ^0.0.4 # Comentado debido a problemas de compatibilidad con Android Gradle Plugin

  # Animations and Effects
  flutter_staggered_animations: ^1.1.1

  # Material You Support
  dynamic_color: ^1.7.0

  # In-App Purchase (Android only)
  in_app_purchase: ^3.1.11
  in_app_purchase_android: ^0.4.0+1
  convex_bottom_bar: ^3.2.0

  # Ads
  google_mobile_ads: ^4.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.13.1

# تكوين أيقونات التطبيق
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/app_icon.png"
  adaptive_icon_background: "#121212"
  adaptive_icon_foreground: "assets/images/app_icon.png"
  min_sdk_android: 21

# تكوين شاشة البداية
flutter_native_splash:
  color: "#000000"
  image: "assets/images/app_logo.png"

  android_12:
    color: "#000000"
    image: "assets/images/app_logo.png"
    icon_background_color: "#000000"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/categories/
    - assets/animations/
    - assets/localization/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
