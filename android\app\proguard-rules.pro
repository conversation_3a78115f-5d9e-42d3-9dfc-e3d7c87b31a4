# قواعد ProGuard لتحسين أداء التطبيق وتجنب ANR

# الاحتفاظ بجميع فئات Flutter
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }

# الاحتفاظ بفئات Google Mobile Ads
-keep class com.google.android.gms.ads.** { *; }
-keep class com.google.ads.** { *; }

# الاحتفاظ بفئات MethodChannel
-keep class com.islamiclandporta.islam.allahwallpaper.ahmad.np.** { *; }

# تحسين الأداء
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# تجنب تحذيرات WebView
-dontwarn android.webkit.**
-keep class android.webkit.** { *; }

# تجنب تحذيرات MediaScanner
-dontwarn android.media.**
-keep class android.media.** { *; }

# حل مشاكل ViewGroup وoffsetRectBetweenParentAndChild
-keep class android.view.ViewGroup { *; }
-keep class android.view.View { *; }
-keepclassmembers class android.view.ViewGroup {
    public void offsetRectBetweenParentAndChild(android.view.View, android.graphics.Rect, boolean, boolean);
}

# حل مشاكل DartMessenger
-keep class io.flutter.embedding.engine.dart.DartMessenger { *; }
-keep class io.flutter.embedding.engine.dart.DartMessenger$Reply { *; }
-keepclassmembers class io.flutter.embedding.engine.dart.DartMessenger$Reply {
    public void reply(java.lang.Object);
}

# حل مشاكل IllegalArgumentException
-keep class java.lang.IllegalArgumentException { *; }
-dontwarn java.lang.IllegalArgumentException

# حل مشاكل المكتبات الأصلية
-keep class android.opengl.** { *; }
-dontwarn android.opengl.**

# حل مشاكل split APK
-keep class android.content.pm.** { *; }
-dontwarn android.content.pm.**

# تجنب تحذيرات النظام
-dontwarn java.lang.invoke.**
-dontwarn javax.annotation.**
-dontwarn org.conscrypt.**

# الاحتفاظ بفئات الاستثناءات
-keepattributes Exceptions

# تحسين الذاكرة
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose
