import 'package:intl/intl.dart';

/// أدوات مساعدة لتنسيق البيانات
class FormatUtils {
  /// تنسيق الأرقام بشكل مختصر (مثل 1K, 1M)
  static String formatNumber(int number) {
    if (number < 1000) {
      return number.toString();
    } else if (number < 1000000) {
      return '${(number / 1000).toStringAsFixed(1).replaceAll('.0', '')}K';
    } else if (number < 1000000000) {
      return '${(number / 1000000).toStringAsFixed(1).replaceAll('.0', '')}M';
    } else {
      return '${(number / 1000000000).toStringAsFixed(1).replaceAll('.0', '')}B';
    }
  }

  /// تنسيق التاريخ
  static String formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  /// تنسيق الوقت
  static String formatTime(DateTime time) {
    return DateFormat('HH:mm').format(time);
  }

  /// تنسيق التاريخ والوقت
  static String formatDateTime(DateTime dateTime) {
    return DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
  }
}
