import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../data/services/subscription_service.dart';
import '../../design_system/colors.dart';
import '../../utils/back_button_handler.dart';
import '../components/navigation/advanced_bottom_nav.dart';

/// الشاشة الرئيسية مع شريط التنقل السفلي
class HomeScreen extends StatefulWidget {
  final Widget child;

  const HomeScreen({super.key, required this.child});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  final SubscriptionService _subscriptionService = SubscriptionService();

  @override
  void initState() {
    super.initState();
    // إضافة مراقب لتغييرات واجهة المستخدم
    WidgetsBinding.instance.addObserver(this);
    // التحقق من حالة الاشتراك عند بدء التطبيق
    _checkSubscriptionStatus();
  }

  // التحقق من حالة الاشتراك
  Future<void> _checkSubscriptionStatus() async {
    try {
      // تهيئة خدمة الاشتراك إذا لم تكن مهيأة
      await _subscriptionService.initialize();

      // التحقق من وجود اشتراك نشط
      final isSubscribed = await _subscriptionService.isSubscribed();
      debugPrint('حالة الاشتراك: ${isSubscribed ? 'مشترك' : 'غير مشترك'}');

      // التحقق من إمكانية تنزيل الخلفيات
      final canDownload = await _subscriptionService.canDownloadWallpapers();
      debugPrint(
        'إمكانية تنزيل الخلفيات: ${canDownload ? 'متاحة' : 'غير متاحة'}',
      );

      // التحقق من إخفاء الإعلانات
      final hideAds = await _subscriptionService.shouldHideAds();
      debugPrint('إخفاء الإعلانات: ${hideAds ? 'نعم' : 'لا'}');
    } catch (e) {
      debugPrint('خطأ في التحقق من حالة الاشتراك: $e');
    }
  }

  @override
  void dispose() {
    // إزالة المراقب عند التخلص من الويدجت
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = _getCurrentIndex(context);

    return BackButtonHandler(
      isRootScreen: true,
      child: Scaffold(
        extendBody: true,
        extendBodyBehindAppBar: true,
        resizeToAvoidBottomInset: false,
        backgroundColor: AppColors.background,
        body: widget.child, // المحتوى الرئيسي فقط بدون أي إعلانات
        bottomNavigationBar: AdvancedBottomNav(
          currentIndex: currentIndex,
          onTap: _onItemTapped,
          backgroundColor: AppColors.surface,
          activeColor: AppColors.primary, // تغيير اللون النشط
          backgroundGradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [AppColors.surface, AppColors.background],
          ),
          items: [
            NavItem(
              icon: Icons.home_outlined,
              title: 'الرئيسية',
              activeIcon: Icons.home_rounded,
            ),
            NavItem(
              icon: Icons.favorite_outline,
              title: 'المفضلة',
              activeIcon: Icons.favorite_rounded,
            ),
            NavItem(
              icon: Icons.settings_outlined,
              title: 'الإعدادات',
              activeIcon: Icons.settings_rounded,
            ),
          ],
        ),
      ),
    );
  }

  int _getCurrentIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.path;

    if (location.startsWith('/favorites')) {
      return 1;
    } else if (location.startsWith('/settings')) {
      return 2;
    }

    return 0; // الصفحة الرئيسية هي الافتراضية
  }

  void _onItemTapped(int index) {
    switch (index) {
      case 0:
        context.go('/home');
        break;
      case 1:
        context.go('/favorites');
        break;
      case 2:
        context.go('/settings');
        break;
    }
  }
}
