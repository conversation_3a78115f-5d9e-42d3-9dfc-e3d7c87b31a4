import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../design_system/colors.dart';
import '../../../data/services/subscription_service.dart';

/// مكون لعرض المحتوى المدفوع مع قفل للمستخدمين غير المشتركين
class PremiumContentOverlay extends StatelessWidget {
  final Widget child;
  final Widget? placeholder;
  final VoidCallback? onSubscribePressed;
  final bool showSubscribeButton;
  final String title;
  final String description;
  
  const PremiumContentOverlay({
    Key? key,
    required this.child,
    this.placeholder,
    this.onSubscribePressed,
    this.showSubscribeButton = true,
    this.title = 'محتوى مميز',
    this.description = 'اشترك للوصول إلى هذا المحتوى',
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
      future: SubscriptionService().isSubscribed(),
      builder: (context, snapshot) {
        final isSubscribed = snapshot.data ?? false;
        
        if (isSubscribed) {
          return child;
        }
        
        return Stack(
          children: [
            // المحتوى المحجوب (مع تأثير الضبابية)
            ClipRect(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                child: Container(
                  color: Colors.black.withOpacity(0.5),
                  child: placeholder ?? child,
                ),
              ),
            ),
            
            // أيقونة القفل وزر الاشتراك
            Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.accent,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 10,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.lock,
                      color: Colors.black,
                      size: 32,
                    ),
                  )
                  .animate(onPlay: (controller) => controller.repeat(reverse: true))
                  .scale(
                    duration: const Duration(seconds: 1),
                    begin: const Offset(1, 1),
                    end: const Offset(1.1, 1.1),
                    curve: Curves.easeInOut,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    description,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (showSubscribeButton) ...[
                    const SizedBox(height: 16),
                    ElevatedButton.icon(
                      onPressed: onSubscribePressed ?? () {
                        context.push('/subscription');
                      },
                      icon: const Icon(Icons.workspace_premium),
                      label: const Text('اشترك الآن'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.accent,
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(30),
                        ),
                      ),
                    )
                    .animate()
                    .fadeIn(duration: const Duration(milliseconds: 500))
                    .scale(
                      begin: const Offset(0.95, 0.95),
                      end: const Offset(1, 1),
                      duration: const Duration(milliseconds: 500),
                    ),
                  ],
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
