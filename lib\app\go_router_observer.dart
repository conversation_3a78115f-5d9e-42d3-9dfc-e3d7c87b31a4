import 'package:flutter/material.dart';
import '../presentation/components/ads/interstitial_ad_manager.dart';

/// مراقب للتنقل بين الشاشات لعرض الإعلانات البينية
class GoRouterObserver extends NavigatorObserver {
  final InterstitialAdManager _adManager = InterstitialAdManager();

  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    _showAdOnNavigation();
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    _showAdOnNavigation();
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    _showAdOnNavigation();
  }

  /// عرض إعلان بيني عند التنقل بين الشاشات
  Future<void> _showAdOnNavigation() async {
    try {
      await _adManager.showAdOnScreenChange();
    } catch (e) {
      debugPrint('❌ خطأ في عرض الإعلان البيني: $e');
    }
  }
}
