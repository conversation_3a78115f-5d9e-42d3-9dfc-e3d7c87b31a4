{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Users\\android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AndroidStudioProjects\\Slmeh.Pro\\android\\app\\.cxx\\Debug\\2t6o5t5l\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\Users\\android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AndroidStudioProjects\\Slmeh.Pro\\android\\app\\.cxx\\Debug\\2t6o5t5l\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\Users\\android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\Users\\android\\Sdk\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}