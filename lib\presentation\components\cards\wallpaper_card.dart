import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:shimmer/shimmer.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';
import '../../../design_system/typography.dart';
import '../../../data/models/wallpaper.dart';
import '../../../utils/format_utils.dart';
import '../../../data/services/favorites_service.dart';
import '../buttons/circular_icon_button.dart';

/// بطاقة عرض الخلفية
class WallpaperCard extends StatefulWidget {
  final Wallpaper wallpaper;
  final double borderRadius;
  final bool showInfo;
  final VoidCallback? onTap;
  final Function(bool)? onFavoriteToggle;
  final bool isFavorite;
  final double? height;
  final BoxFit fit;
  final bool showFeaturedLabel;

  const WallpaperCard({
    super.key,
    required this.wallpaper,
    this.borderRadius = AppDimensions.radiusMedium,
    this.showInfo = false,
    this.onTap,
    this.onFavoriteToggle,
    this.isFavorite = false,
    this.height,
    this.fit = BoxFit.cover,
    this.showFeaturedLabel = false,
  });

  @override
  State<WallpaperCard> createState() => _WallpaperCardState();
}

class _WallpaperCardState extends State<WallpaperCard> {
  final FavoritesService _favoritesService = FavoritesService();
  bool _isFavorite = false;
  bool _isCheckingFavorite = false;

  @override
  void initState() {
    super.initState();
    _isFavorite = widget.isFavorite;
    _checkIfFavorite();
  }

  Future<void> _checkIfFavorite() async {
    if (widget.onFavoriteToggle == null && !_isCheckingFavorite) {
      _isCheckingFavorite = true;
      final isFav = await _favoritesService.isFavorite(widget.wallpaper.id);
      if (mounted) {
        setState(() {
          _isFavorite = isFav;
          _isCheckingFavorite = false;
        });
      }
    }
  }

  Future<void> _toggleFavorite() async {
    final newState = !_isFavorite;

    if (widget.onFavoriteToggle != null) {
      widget.onFavoriteToggle!(newState);
    } else {
      setState(() => _isFavorite = newState);

      if (newState) {
        await _favoritesService.addToFavorites(widget.wallpaper);
      } else {
        await _favoritesService.removeFromFavorites(widget.wallpaper.id);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          widget.onTap ??
          () {
            context.push(
              '/wallpaper/${widget.wallpaper.id}',
              extra: widget.wallpaper,
            );
          },
      child: Container(
        height: widget.height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          boxShadow: AppShadows.card,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(widget.borderRadius),
          child: Stack(
            fit: StackFit.passthrough,
            children: [
              // صورة الخلفية
              SizedBox(
                width: double.infinity,
                height: widget.height,
                child: Hero(
                  tag: 'wallpaper_${widget.wallpaper.id}',
                  child: Material(
                    type: MaterialType.transparency,
                    child: CachedNetworkImage(
                      imageUrl: widget.wallpaper.thumbnailUrl,
                      fit: widget.fit,
                      memCacheHeight: 600,
                      memCacheWidth: 400,
                      fadeInDuration: const Duration(milliseconds: 300),
                      placeholder: (context, url) => _buildPlaceholder(),
                      errorWidget:
                          (context, url, error) => Container(
                            color: AppColors.card,
                            child: const Center(
                              child: Icon(
                                Icons.error_outline,
                                color: AppColors.error,
                                size: 32,
                              ),
                            ),
                          ),
                    ),
                  ),
                ),
              ),

              // زر المفضلة - دائماً مرئي
              Positioned(
                top: AppDimensions.marginSmall,
                right: AppDimensions.marginSmall,
                child: CircularIconButton(
                  icon: _isFavorite ? Icons.favorite : Icons.favorite_border,
                  iconColor: _isFavorite ? Colors.red : Colors.white,
                  backgroundColor: Colors.black.withAlpha(128),
                  size: 42, // زيادة حجم الزر
                  onPressed: _toggleFavorite,
                ),
              ),

              // علامة مميزة
              if (widget.showFeaturedLabel && widget.wallpaper.isFeatured)
                Positioned(
                  top: AppDimensions.marginSmall,
                  left: AppDimensions.marginSmall,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingSmall,
                      vertical: AppDimensions.paddingXSmall,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.accent,
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusSmall,
                      ),
                    ),
                    child: Text(
                      'مميز',
                      style: AppTypography.labelSmall.copyWith(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

              // Eliminamos la etiqueta premium según lo solicitado

              // إحصائيات الخلفية
              Positioned(
                bottom: AppDimensions.marginSmall,
                left: AppDimensions.marginSmall,
                child: Row(
                  children: [
                    // عدد المشاهدات
                    if (widget.wallpaper.views > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(180),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(77),
                              blurRadius: 4,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.visibility,
                              color: Colors.white,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              FormatUtils.formatNumber(widget.wallpaper.views),
                              style: AppTypography.labelMedium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),

                    const SizedBox(width: 8),

                    // عدد التحميلات
                    if (widget.wallpaper.downloads > 0)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(180),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(77),
                              blurRadius: 4,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.download,
                              color: Colors.white,
                              size: 14,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              FormatUtils.formatNumber(
                                widget.wallpaper.downloads,
                              ),
                              style: AppTypography.labelMedium.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Shimmer.fromColors(
      baseColor: AppColors.card,
      highlightColor: AppColors.surfaceLight,
      child: Container(color: Colors.white),
    );
  }
}
