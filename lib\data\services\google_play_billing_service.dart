import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import '../models/subscription.dart';

/// خدمة للتعامل مع عمليات الشراء داخل التطبيق باستخدام Google Play Billing
class GooglePlayBillingService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط
  static final GooglePlayBillingService _instance =
      GooglePlayBillingService._internal();
  factory GooglePlayBillingService() => _instance;
  GooglePlayBillingService._internal();

  // كائن InAppPurchase
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;

  // قائمة المنتجات المتاحة
  List<ProductDetails> _products = [];

  // قائمة المشتريات
  List<PurchaseDetails> _purchases = [];

  // تدفق المشتريات
  StreamSubscription<List<PurchaseDetails>>? _subscription;

  // حالة التهيئة
  bool _isAvailable = false;
  bool _isLoading = true;
  String? _error;

  // معرفات المنتجات - تم تحديثها لتتوافق مع معرفات المنتجات في Google Play Console
  final Set<String> _productIds = {
    'com.islamiclandporta.islam.allahwallpaper.ahmad.np.monthly', // الاشتراك الشهري
    'com.islamiclandporta.islam.allahwallpaper.ahmad.np.yearly', // الاشتراك السنوي
    'com.islamiclandporta.islam.allahwallpaper.ahmad.np.lifetime', // الاشتراك مدى الحياة
  };

  // الحصول على حالة التهيئة
  bool get isAvailable => _isAvailable;
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<ProductDetails> get products => _products;
  List<PurchaseDetails> get purchases => _purchases;

  /// تهيئة خدمة الفوترة
  Future<void> initialize() async {
    // إعادة تعيين الحالة
    _isLoading = true;
    _error = null;

    if (!Platform.isAndroid) {
      _isAvailable = false;
      _isLoading = false;
      _error = 'المنصة غير مدعومة';
      return;
    }

    // إلغاء الاشتراك السابق إذا كان موجوداً
    try {
      await _subscription?.cancel();
      _subscription = null;
      debugPrint('✅ تم إلغاء الاشتراك السابق بنجاح');
    } catch (e) {
      debugPrint('ملاحظة: لا يوجد اشتراك سابق للإلغاء');
    }

    // التحقق من توفر خدمة الفوترة
    final bool available = await _inAppPurchase.isAvailable();
    if (!available) {
      _isAvailable = false;
      _isLoading = false;
      _error = 'خدمة الفوترة غير متوفرة';
      debugPrint('❌ خدمة الفوترة غير متوفرة');
      return;
    }

    // الاستماع لتحديثات المشتريات
    _subscription = _inAppPurchase.purchaseStream.listen(
      _onPurchaseUpdate,
      onDone: _onPurchaseDone,
      onError: _onPurchaseError,
    );

    debugPrint('✅ تم إعداد مستمع المشتريات');

    // الحصول على المنتجات المتاحة
    await _loadProducts();

    // الحصول على المشتريات السابقة
    await _loadPastPurchases();

    _isAvailable = true;
    _isLoading = false;

    debugPrint('✅ تم تهيئة خدمة Google Play Billing بنجاح');
  }

  /// تحميل المنتجات المتاحة
  Future<void> _loadProducts() async {
    try {
      debugPrint('بدء تحميل المنتجات...');
      debugPrint('معرفات المنتجات: $_productIds');

      final ProductDetailsResponse response = await _inAppPurchase
          .queryProductDetails(_productIds);

      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('المنتجات غير الموجودة: ${response.notFoundIDs}');
      }

      _products = response.productDetails;

      if (_products.isNotEmpty) {
        for (var product in _products) {
          debugPrint(
            'تم تحميل المنتج: ${product.id} - ${product.title} - ${product.price}',
          );
        }
      }

      debugPrint('تم تحميل ${_products.length} منتج');
    } catch (e) {
      debugPrint('خطأ في تحميل المنتجات: $e');
      _error = 'فشل تحميل المنتجات';
    }
  }

  /// تحميل المشتريات السابقة
  Future<void> _loadPastPurchases() async {
    try {
      // استخدام طريقة الاستعلام عن المشتريات السابقة
      final bool available = await _inAppPurchase.isAvailable();
      if (!available) {
        debugPrint('خدمة المشتريات غير متاحة');
        return;
      }

      // الحصول على المشتريات السابقة
      _purchases = [];

      try {
        debugPrint('محاولة استعادة المشتريات على Android...');

        // الحصول على إضافة منصة Android
        final InAppPurchaseAndroidPlatformAddition androidAddition =
            _inAppPurchase
                .getPlatformAddition<InAppPurchaseAndroidPlatformAddition>();

        // محاولة استعادة المشتريات
        debugPrint('استدعاء queryPastPurchases على منصة Android');
        final QueryPurchaseDetailsResponse response =
            await androidAddition.queryPastPurchases();

        if (response.pastPurchases.isNotEmpty) {
          debugPrint(
            'تم العثور على ${response.pastPurchases.length} مشتريات سابقة',
          );
          _purchases = response.pastPurchases;
        } else {
          debugPrint('لم يتم العثور على مشتريات سابقة');
        }

        // تحديث المشتريات من خلال الاستماع للتحديثات
        _subscription?.cancel(); // إلغاء الاشتراك السابق إذا كان موجوداً
        _subscription = _inAppPurchase.purchaseStream.listen(
          _onPurchaseUpdate,
          onDone: _onPurchaseDone,
          onError: _onPurchaseError,
        );

        debugPrint('تم إعداد مستمع المشتريات بنجاح');
      } catch (e) {
        debugPrint('خطأ في استعادة المشتريات على Android: $e');
      }
    } catch (e) {
      debugPrint('خطأ في تحميل المشتريات السابقة: $e');
    }
  }

  /// معالجة تحديثات المشتريات
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    _purchases = purchaseDetailsList;

    for (final purchase in purchaseDetailsList) {
      if (purchase.status == PurchaseStatus.pending) {
        // المشتريات المعلقة
        debugPrint('المشتريات المعلقة: ${purchase.productID}');
      } else if (purchase.status == PurchaseStatus.purchased ||
          purchase.status == PurchaseStatus.restored) {
        // المشتريات المكتملة أو المستعادة
        if (purchase.pendingCompletePurchase) {
          _inAppPurchase.completePurchase(purchase);
        }
      } else if (purchase.status == PurchaseStatus.error) {
        // المشتريات التي حدث بها خطأ
        debugPrint('خطأ في المشتريات: ${purchase.error}');
      }
    }
  }

  /// معالجة اكتمال المشتريات
  void _onPurchaseDone() {
    _subscription?.cancel();
  }

  /// معالجة أخطاء المشتريات
  void _onPurchaseError(dynamic error) {
    debugPrint('خطأ في المشتريات: $error');
    _error = 'حدث خطأ أثناء المشتريات';
  }

  /// شراء منتج
  Future<bool> buyProduct(String productId) async {
    if (!_isAvailable) {
      debugPrint('خدمة الفوترة غير متاحة');
      return false;
    }

    try {
      // التحقق من وجود منتجات
      if (_products.isEmpty) {
        debugPrint('لا توجد منتجات متاحة للشراء');
        // محاولة تحميل المنتجات مرة أخرى
        await _loadProducts();
        if (_products.isEmpty) {
          debugPrint('فشل تحميل المنتجات');
          return false;
        }
      }

      // البحث عن المنتج
      ProductDetails? product;
      try {
        product = _products.firstWhere((product) => product.id == productId);
        debugPrint('تم العثور على المنتج: ${product.id} - ${product.title}');
      } catch (e) {
        debugPrint('المنتج غير موجود: $productId');
        // استخدام منتج افتراضي إذا كان متاحاً
        if (_products.isNotEmpty) {
          product = _products.first;
          debugPrint('استخدام منتج بديل: ${product.id} - ${product.title}');
        } else {
          throw PlatformException(
            code: 'product_not_found',
            message: 'المنتج غير موجود: $productId',
          );
        }
      }

      // إنشاء طلب الشراء
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: product,
        applicationUserName: null,
      );

      debugPrint('بدء عملية الشراء للمنتج: ${product.id}');

      // شراء المنتج
      final result = await _inAppPurchase.buyNonConsumable(
        purchaseParam: purchaseParam,
      );

      debugPrint('نتيجة طلب الشراء: $result');
      return result;
    } catch (e) {
      debugPrint('خطأ في شراء المنتج: $e');
      return false;
    }
  }

  /// استعادة المشتريات
  Future<bool> restorePurchases() async {
    if (!_isAvailable) {
      return false;
    }

    try {
      // إعادة تحميل المشتريات السابقة
      await _loadPastPurchases();

      // إعادة تحميل المنتجات
      await _loadProducts();

      return true;
    } catch (e) {
      debugPrint('خطأ في استعادة المشتريات: $e');
      return false;
    }
  }

  /// التحقق من وجود اشتراك نشط
  bool hasActiveSubscription() {
    if (_purchases.isEmpty) {
      return false;
    }

    // البحث عن اشتراك نشط
    for (final purchase in _purchases) {
      if (purchase.status == PurchaseStatus.purchased ||
          purchase.status == PurchaseStatus.restored) {
        // التحقق من نوع المنتج
        if (_productIds.contains(purchase.productID)) {
          return true;
        }
      }
    }

    return false;
  }

  /// الحصول على معلومات الاشتراك النشط
  Subscription? getActiveSubscription() {
    if (!hasActiveSubscription()) {
      return null;
    }

    // البحث عن اشتراك نشط
    for (final purchase in _purchases) {
      if (purchase.status == PurchaseStatus.purchased ||
          purchase.status == PurchaseStatus.restored) {
        // التحقق من نوع المنتج
        if (_productIds.contains(purchase.productID)) {
          // البحث عن معلومات المنتج
          ProductDetails? product;
          try {
            product = _products.firstWhere(
              (product) => product.id == purchase.productID,
            );
          } catch (e) {
            // المنتج غير موجود
            product = null;
          }

          if (product != null) {
            // إنشاء كائن الاشتراك
            return _createSubscriptionFromProduct(product, purchase);
          }
        }
      }
    }

    return null;
  }

  /// إنشاء كائن اشتراك من معلومات المنتج
  Subscription _createSubscriptionFromProduct(
    ProductDetails product,
    PurchaseDetails purchase,
  ) {
    // تحديد نوع الاشتراك
    String id = 'unknown';
    String name = 'اشتراك غير معروف';
    String description = 'اشتراك غير معروف';
    int durationDays = 0;
    bool isRecommended = false;
    List<String> features = [];
    bool allowsDownloads = true;
    bool removesAds = true;

    if (product.id ==
        'com.islamiclandporta.islam.allahwallpaper.ahmad.np.monthly') {
      id = 'monthly';
      name = 'اشتراك شهري';
      description =
          'اشترك في النسخة المميزة من تطبيق خلفيات إسلامية واستمتع بمزايا حصرية لمدة شهر كامل';
      durationDays = 30;
      isRecommended = true;
      features = [
        'تنزيل جميع الخلفيات بجودة عالية',
        'إزالة جميع الإعلانات',
        'الوصول المبكر للخلفيات الجديدة',
        'دعم تطوير المحتوى الإسلامي',
      ];
    } else if (product.id ==
        'com.islamiclandporta.islam.allahwallpaper.ahmad.np.yearly') {
      id = 'yearly';
      name = 'اشتراك سنوي';
      description =
          'اشترك في النسخة المميزة من تطبيق خلفيات إسلامية واستمتع بمزايا حصرية لمدة عام كامل بسعر مخفض';
      durationDays = 365;
      features = [
        'تنزيل جميع الخلفيات بجودة عالية',
        'إزالة جميع الإعلانات',
        'الوصول المبكر للخلفيات الجديدة',
        'دعم تطوير المحتوى الإسلامي',
        'توفير أكثر من 50% مقارنة بالاشتراك الشهري',
      ];
    } else if (product.id ==
        'com.islamiclandporta.islam.allahwallpaper.ahmad.np.lifetime') {
      id = 'lifetime';
      name = 'اشتراك مدى الحياة';
      description =
          'احصل على النسخة المميزة من تطبيق خلفيات إسلامية إلى الأبد بدفعة واحدة';
      durationDays = 36500; // ~100 سنة
      features = [
        'تنزيل جميع الخلفيات بجودة عالية مدى الحياة',
        'إزالة جميع الإعلانات نهائياً',
        'الوصول المبكر للخلفيات الجديدة',
        'دعم تطوير المحتوى الإسلامي',
        'لا توجد مدفوعات متكررة أو تجديد تلقائي',
        'دفعة واحدة تمنحك جميع المزايا المميزة إلى الأبد',
      ];
    }

    // استخراج السعر والعملة
    final String priceString = product.price;
    double price = 0.0;
    String currency = 'USD';

    // محاولة استخراج السعر والعملة من النص
    try {
      // استخراج الرقم من النص
      final RegExp regExp = RegExp(r'(\d+[.,]?\d*)');
      final match = regExp.firstMatch(priceString);
      if (match != null) {
        final String priceText = match.group(1)!.replaceAll(',', '.');
        price = double.parse(priceText);
      }

      // استخراج العملة من النص
      if (priceString.contains('\$')) {
        currency = 'USD';
      } else if (priceString.contains('€')) {
        currency = 'EUR';
      } else if (priceString.contains('£')) {
        currency = 'GBP';
      } else if (priceString.contains('¥')) {
        currency = 'JPY';
      } else if (priceString.contains('₹')) {
        currency = 'INR';
      } else if (priceString.contains('SAR') || priceString.contains('ر.س')) {
        currency = 'SAR';
      }
    } catch (e) {
      debugPrint('خطأ في استخراج السعر والعملة: $e');
    }

    // تحديد تاريخ البدء والانتهاء
    final DateTime now = DateTime.now();
    DateTime? endDate;

    if (id == 'lifetime') {
      // اشتراك مدى الحياة
      endDate = now.add(Duration(days: durationDays));
    } else if (purchase.status == PurchaseStatus.purchased ||
        purchase.status == PurchaseStatus.restored) {
      // اشتراك نشط
      final GooglePlayPurchaseDetails googlePlayPurchaseDetails =
          purchase as GooglePlayPurchaseDetails;

      // محاولة استخراج تاريخ انتهاء الاشتراك من بيانات الشراء
      try {
        final int purchaseTimeMillis =
            googlePlayPurchaseDetails.billingClientPurchase.purchaseTime;
        final DateTime purchaseTime = DateTime.fromMillisecondsSinceEpoch(
          purchaseTimeMillis,
        );
        endDate = purchaseTime.add(Duration(days: durationDays));
      } catch (e) {
        debugPrint('خطأ في استخراج تاريخ انتهاء الاشتراك: $e');
        endDate = now.add(Duration(days: durationDays));
      }
    }

    // إنشاء كائن الاشتراك
    return Subscription(
      id: id,
      name: name,
      description: description,
      price: price,
      currency: currency,
      durationDays: durationDays,
      isActive: true,
      startDate: now,
      endDate: endDate,
      productId: product.id,
      isRecommended: isRecommended,
      features: features,
      purchaseToken:
          (purchase as GooglePlayPurchaseDetails)
              .billingClientPurchase
              .purchaseToken,
      allowsDownloads: allowsDownloads,
      removesAds: removesAds,
    );
  }

  /// إلغاء الاشتراك
  Future<bool> cancelSubscription(String productId) async {
    // ملاحظة: لا يمكن إلغاء الاشتراكات برمجياً من التطبيق
    // يجب على المستخدم الذهاب إلى Google Play Store لإلغاء الاشتراك
    return false;
  }

  /// التخلص من الموارد
  void dispose() {
    _subscription?.cancel();
  }
}
