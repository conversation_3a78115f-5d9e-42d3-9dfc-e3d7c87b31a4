import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:convex_bottom_bar/convex_bottom_bar.dart' as convex;
import '../../../design_system/colors.dart';
import '../../../design_system/shadows.dart';
import '../../../design_system/gradients.dart';

/// شريط تنقل احترافي باستخدام مكتبة convex_bottom_bar
class ProfessionalBottomNav extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<TabItem> items;
  final Color? activeColor;
  final Color? backgroundColor;
  final Gradient? backgroundGradient;

  const ProfessionalBottomNav({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.activeColor,
    this.backgroundColor,
    this.backgroundGradient,
  }) : super(key: key);

  @override
  State<ProfessionalBottomNav> createState() => _ProfessionalBottomNavState();
}

class _ProfessionalBottomNavState extends State<ProfessionalBottomNav>
    with WidgetsBindingObserver {
  // ارتفاع شريط التنقل النظامي
  double _systemNavBarHeight = 0;
  // حالة ظهور شريط التنقل النظامي
  bool _isSystemNavBarVisible = false;
  // ارتفاع شريط التنقل الخاص بنا
  static const double _navBarHeight = 70.0; // زيادة ارتفاع شريط التنقل

  @override
  void initState() {
    super.initState();
    // إضافة مراقب لتغييرات واجهة المستخدم
    WidgetsBinding.instance.addObserver(this);
    // لا نقوم بتحديث حالة شريط التنقل النظامي هنا
    // سيتم تحديثها في didChangeDependencies
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تحديث حالة شريط التنقل النظامي عند تغيير التبعيات
    _updateSystemNavBarState();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    // تحديث حالة شريط التنقل النظامي عند تغيير أبعاد الشاشة
    if (mounted) {
      _updateSystemNavBarState();
    }
  }

  @override
  void dispose() {
    // إزالة المراقب عند التخلص من الويدجت
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // تحديث حالة شريط التنقل النظامي
  void _updateSystemNavBarState() {
    if (!mounted) return;

    try {
      final mediaQuery = MediaQuery.of(context);
      final viewPadding = mediaQuery.viewPadding;
      final newSystemNavBarHeight = viewPadding.bottom;
      final newIsSystemNavBarVisible = newSystemNavBarHeight > 0;

      // تحديث الحالة فقط إذا تغيرت
      if (newSystemNavBarHeight != _systemNavBarHeight ||
          newIsSystemNavBarVisible != _isSystemNavBarVisible) {
        setState(() {
          _systemNavBarHeight = newSystemNavBarHeight;
          _isSystemNavBarVisible = newIsSystemNavBarVisible;
        });
      }
    } catch (e) {
      // تجاهل الأخطاء التي قد تحدث أثناء الوصول إلى MediaQuery
      // قبل اكتمال بناء الويدجت
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحويل عناصر TabItem إلى عناصر convex.TabItem
    final convexItems =
        widget.items
            .map(
              (item) => convex.TabItem(
                icon: item.icon,
                title: item.title,
                activeIcon: item.activeIcon,
              ),
            )
            .toList();

    // إنشاء حاوية خارجية تتكيف مع شريط التنقل النظامي
    return Container(
          // استخدام حاوية خارجية لتطبيق الظل
          decoration: BoxDecoration(
            gradient: widget.backgroundGradient,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(80),
                blurRadius: 25,
                spreadRadius: 3,
                offset: const Offset(0, -6),
              ),
            ],
            border: Border(
              top: BorderSide(color: Colors.white.withAlpha(30), width: 1.5),
            ),
          ),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height:
                _navBarHeight +
                (_isSystemNavBarVisible ? _systemNavBarHeight : 0),
            padding: EdgeInsets.only(
              bottom: _isSystemNavBarVisible ? _systemNavBarHeight : 0,
            ),
            child: convex.StyleProvider(
              style: _CustomStyle(),
              child: convex.ConvexAppBar(
                style: convex.TabStyle.reactCircle,
                backgroundColor: widget.backgroundColor ?? AppColors.surface,
                color: AppColors.textMuted,
                activeColor: widget.activeColor ?? AppColors.accent,
                elevation: 0,
                height: _navBarHeight, // ارتفاع ثابت
                top: -25, // رفع موضع الزر المحدد أكثر للأعلى
                curveSize: 90, // زيادة حجم المنحنى أكثر
                items: convexItems,
                initialActiveIndex: widget.currentIndex,
                onTap: widget.onTap,
              ),
            ),
          ),
        )
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .shimmer(
          duration: const Duration(seconds: 4),
          color: (widget.activeColor ?? AppColors.accent).withAlpha(25),
          size: 0.02,
          delay: const Duration(seconds: 1),
        );
  }
}

/// نمط مخصص لشريط التنقل
class _CustomStyle extends convex.StyleHook {
  @override
  double get activeIconSize => 32; // زيادة حجم الأيقونة النشطة

  @override
  double get activeIconMargin => 10; // زيادة هامش الأيقونة النشطة

  @override
  double get iconSize => 26; // زيادة حجم الأيقونة

  @override
  TextStyle textStyle(Color color, String? fontFamily) {
    return TextStyle(
      fontSize: 13, // زيادة حجم الخط
      color: color,
      fontWeight: FontWeight.w600,
      fontFamily: fontFamily,
      letterSpacing: 0.2,
      height: 1.0, // زيادة ارتفاع النص
      shadows: [
        Shadow(
          color: Colors.black.withAlpha(100),
          blurRadius: 4,
          offset: const Offset(0, 1),
        ),
      ],
    );
  }
}

/// عنصر شريط التنقل
class TabItem {
  final IconData icon;
  final String title;
  final Widget? activeIcon;
  final Color? activeColor;
  final Color? inactiveColor;
  final bool showBadge;
  final String? badgeText;
  final Color? badgeColor;

  TabItem({
    required this.icon,
    required this.title,
    this.activeColor,
    this.inactiveColor,
    this.activeIcon,
    this.showBadge = false,
    this.badgeText,
    this.badgeColor,
  });
}
