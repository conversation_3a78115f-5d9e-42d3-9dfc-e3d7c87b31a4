import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';

/// شاشة تحميل محسنة واحترافية للتطبيق
class EnhancedLoadingScreen extends StatelessWidget {
  final String loadingText;
  final double progress;
  final VoidCallback? onRefresh;
  final bool showRefreshButton;

  const EnhancedLoadingScreen({
    super.key,
    this.loadingText = 'جاري تحميل الخلفيات...',
    this.progress = 0.5,
    this.onRefresh,
    this.showRefreshButton = true,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        color: Colors.black,
      ),
      child: Stack(
        children: [
          // المحتوى الرئيسي
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة التحميل
                _buildLoadingIcon(screenWidth)
                    .animate()
                    .fadeIn(duration: const Duration(milliseconds: 600))
                    .scale(
                      begin: const Offset(0.8, 0.8),
                      end: const Offset(1.0, 1.0),
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeOutBack,
                    ),

                // مسافة ديناميكية
                SizedBox(height: screenHeight * 0.04),

                // نص التحميل
                Text(
                  loadingText,
                  style: AppTypography.titleMedium.copyWith(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                )
                    .animate()
                    .fadeIn(
                      delay: const Duration(milliseconds: 200),
                      duration: const Duration(milliseconds: 400),
                    ),

                // مسافة ديناميكية
                SizedBox(height: screenHeight * 0.03),

                // شريط التقدم
                _buildProgressBar(screenWidth)
                    .animate()
                    .fadeIn(
                      delay: const Duration(milliseconds: 400),
                      duration: const Duration(milliseconds: 400),
                    ),
              ],
            ),
          ),

          // زر التحديث في الأسفل
          if (showRefreshButton)
            Positioned(
              bottom: 40,
              left: 20,
              right: 20,
              child: _buildRefreshButton(context)
                  .animate()
                  .fadeIn(
                    delay: const Duration(milliseconds: 600),
                    duration: const Duration(milliseconds: 400),
                  ),
            ),
        ],
      ),
    );
  }

  /// بناء أيقونة التحميل
  Widget _buildLoadingIcon(double screenWidth) {
    final iconSize = (screenWidth * 0.25).clamp(100.0, 140.0);

    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        color: Colors.grey.shade900,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(100),
            blurRadius: 20,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Center(
        child: Icon(
          Icons.image_outlined,
          color: AppColors.primary,
          size: iconSize * 0.5,
        ),
      ),
    );
  }

  /// بناء شريط التقدم
  Widget _buildProgressBar(double screenWidth) {
    final barWidth = screenWidth * 0.7;

    return Column(
      children: [
        // شريط التقدم
        Container(
          width: barWidth,
          height: 8,
          decoration: BoxDecoration(
            color: Colors.grey.shade800,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Stack(
            children: [
              // الجزء المكتمل من شريط التقدم
              FractionallySizedBox(
                widthFactor: progress,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [
                        Color(0xFF2196F3),
                        Color(0xFF0D47A1),
                      ],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(4),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primary.withAlpha(100),
                        blurRadius: 8,
                        spreadRadius: -2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء زر التحديث
  Widget _buildRefreshButton(BuildContext context) {
    return Container(
      height: 56,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFFD4AF37),
            Color(0xFFC9A227),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(50),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onRefresh,
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          child: Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              textDirection: TextDirection.rtl,
              children: [
                const Icon(
                  Icons.refresh_rounded,
                  color: Colors.white,
                  size: 22,
                ),
                const SizedBox(width: 10),
                Text(
                  'جاري تحديث الخلفيات...',
                  style: AppTypography.titleMedium.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
