import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'wallpaper_platform_service.dart';

/// خدمة إدارة الخلفيات
/// تستخدم لتحميل وتعيين الخلفيات
class WallpaperService {
  /// تحميل الخلفية إلى جهاز المستخدم
  Future<String?> downloadWallpaper(String imageUrl, String fileName) async {
    try {
      // التحقق من الأذونات
      if (!await _checkPermissions()) {
        debugPrint('فشل تحميل الخلفية: لم يتم منح الأذونات المطلوبة');
        return null;
      }

      // محاولة الحصول على مسار التخزين
      Directory? directory;

      try {
        // أولاً نحاول الحصول على مسار التخزين الخارجي
        directory = await getExternalStorageDirectory();
      } catch (e) {
        debugPrint('فشل الحصول على مسار التخزين الخارجي: $e');
      }

      // إذا فشلت المحاولة الأولى، نحاول الحصول على مسار التخزين المؤقت
      if (directory == null) {
        try {
          directory = await getTemporaryDirectory();
        } catch (e) {
          debugPrint('فشل الحصول على مسار التخزين المؤقت: $e');
        }
      }

      // إذا فشلت جميع المحاولات، نحاول الحصول على مسار التخزين الداخلي
      if (directory == null) {
        try {
          directory = await getApplicationDocumentsDirectory();
        } catch (e) {
          debugPrint('فشل الحصول على مسار التخزين الداخلي: $e');
          return null;
        }
      }

      // إنشاء مجلد للخلفيات إذا لم يكن موجودًا
      final wallpaperDir = Directory('${directory.path}/Wallpapers');
      if (!await wallpaperDir.exists()) {
        await wallpaperDir.create(recursive: true);
      }

      // تنزيل الصورة
      final response = await http.get(Uri.parse(imageUrl));
      if (response.statusCode != 200) {
        debugPrint('فشل تحميل الخلفية: رمز الاستجابة ${response.statusCode}');
        return null;
      }

      // حفظ الصورة
      final file = File('${wallpaperDir.path}/$fileName.jpg');
      await file.writeAsBytes(response.bodyBytes);

      // التأكد من أن الملف تم إنشاؤه بنجاح
      if (await file.exists()) {
        debugPrint('تم تحميل الخلفية بنجاح: ${file.path}');
        return file.path;
      } else {
        debugPrint('فشل تحميل الخلفية: لم يتم إنشاء الملف');
        return null;
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الخلفية: $e');
      return null;
    }
  }

  /// مشاركة الخلفية
  Future<bool> shareWallpaper(String imageUrl, String title) async {
    try {
      // تحميل الصورة أولاً
      final filePath = await downloadWallpaper(
        imageUrl,
        'shared_wallpaper_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (filePath == null) {
        return false;
      }

      // مشاركة الملف باستخدام SharePlus
      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(filePath)],
          text: 'شارك هذه الخلفية الإسلامية الرائعة: $title',
          subject: 'خلفية إسلامية: $title',
        ),
      );

      return true;
    } catch (e) {
      debugPrint('خطأ في مشاركة الخلفية: $e');
      return false;
    }
  }

  /// تعيين الخلفية على شاشة القفل أو الشاشة الرئيسية
  Future<bool> setWallpaper(String imageUrl, WallpaperType type) async {
    try {
      // التحقق من الأذونات أولاً
      if (!await _checkPermissions()) {
        debugPrint('فشل تعيين الخلفية: لم يتم منح الأذونات المطلوبة');
        return false;
      }

      // محاولة تعيين الخلفية باستخدام الخدمة الأصلية
      bool success = false;

      try {
        // استخدام الخدمة الأصلية لتعيين الخلفية مباشرة
        switch (type) {
          case WallpaperType.home:
            success = await WallpaperPlatformService.setHomeScreenWallpaper(
              imageUrl,
            );
            break;
          case WallpaperType.lock:
            success = await WallpaperPlatformService.setLockScreenWallpaper(
              imageUrl,
            );
            break;
          case WallpaperType.both:
            success = await WallpaperPlatformService.setBothScreensWallpaper(
              imageUrl,
            );
            break;
        }

        if (success) {
          debugPrint('تم تعيين الخلفية بنجاح باستخدام الخدمة الأصلية');
          return true;
        }
      } catch (e) {
        debugPrint('فشل تعيين الخلفية باستخدام الخدمة الأصلية: $e');
      }

      // إذا فشلت الطريقة الأولى، نستخدم الطريقة البديلة
      // تحميل الصورة أولاً
      final filePath = await downloadWallpaper(
        imageUrl,
        'wallpaper_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (filePath == null) {
        debugPrint('فشل تحميل الخلفية: لم يتم الحصول على مسار الملف');
        return false;
      }

      // التأكد من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('فشل تعيين الخلفية: الملف غير موجود');
        return false;
      }

      // محاولة فتح الصورة باستخدام تطبيق المعرض الافتراضي
      try {
        // إنشاء Uri للملف
        final fileUri = Uri.file(filePath);

        // فتح الملف باستخدام تطبيق المعرض الافتراضي
        if (await canLaunchUrl(fileUri)) {
          await launchUrl(fileUri, mode: LaunchMode.externalApplication);
          return true;
        }
      } catch (e) {
        debugPrint('فشل فتح الملف باستخدام المعرض: $e');
      }

      // إذا فشلت الطريقة الثانية، نحاول استخدام مشاركة الملف
      try {
        await SharePlus.instance.share(
          ShareParams(
            files: [XFile(filePath)],
            text: 'تم تحميل الخلفية. يمكنك تعيينها كخلفية للشاشة.',
          ),
        );
        return true;
      } catch (e) {
        debugPrint('فشل مشاركة الملف: $e');
        return false;
      }
    } catch (e) {
      debugPrint('خطأ في تعيين الخلفية: $e');
      return false;
    }
  }

  /// التحقق من الأذونات المطلوبة
  Future<bool> _checkPermissions() async {
    // في أندرويد 13 وما فوق، نحتاج إلى أذونات مختلفة
    if (Platform.isAndroid) {
      try {
        // التحقق من أذونات الوصول للتخزين
        final storageStatus = await Permission.storage.status;
        final photosStatus = await Permission.photos.status;
        final mediaStatus = await Permission.mediaLibrary.status;

        // إذا كانت أي من الأذونات مرفوضة، نطلب جميع الأذونات المطلوبة
        if (storageStatus.isDenied ||
            photosStatus.isDenied ||
            mediaStatus.isDenied) {
          // طلب الأذونات
          await Permission.storage.request();
          await Permission.photos.request();
          await Permission.mediaLibrary.request();

          // التحقق مرة أخرى بعد طلب الأذونات
          final newStorageStatus = await Permission.storage.status;
          final newPhotosStatus = await Permission.photos.status;
          final newMediaStatus = await Permission.mediaLibrary.status;

          // يجب أن تكون جميع الأذونات ممنوحة
          return newStorageStatus.isGranted ||
              newPhotosStatus.isGranted ||
              newMediaStatus.isGranted;
        }

        // إذا كانت جميع الأذونات ممنوحة بالفعل
        return storageStatus.isGranted ||
            photosStatus.isGranted ||
            mediaStatus.isGranted;
      } catch (e) {
        debugPrint('خطأ في التحقق من الأذونات: $e');
        // في حالة حدوث خطأ، نحاول طلب إذن التخزين فقط
        final result = await Permission.storage.request();
        return result.isGranted;
      }
    } else {
      // لأنظمة التشغيل الأخرى
      return true;
    }
  }

  /// فتح الصورة في معرض الصور
  Future<bool> openImageInGallery(String filePath) async {
    try {
      // التحقق من وجود الملف
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('فشل فتح الصورة: الملف غير موجود');
        return false;
      }

      // إنشاء Uri للملف
      final fileUri = Uri.file(filePath);

      // فتح الملف باستخدام تطبيق المعرض الافتراضي
      if (await canLaunchUrl(fileUri)) {
        await launchUrl(fileUri, mode: LaunchMode.externalApplication);
        return true;
      }

      // إذا فشلت الطريقة الأولى، نحاول استخدام مشاركة الملف
      await SharePlus.instance.share(
        ShareParams(files: [XFile(filePath)], text: 'تم تنزيل الخلفية بنجاح'),
      );
      return true;
    } catch (e) {
      debugPrint('خطأ في فتح الصورة في المعرض: $e');
      return false;
    }
  }
}

/// أنواع الخلفيات
enum WallpaperType {
  home, // الشاشة الرئيسية
  lock, // شاشة القفل
  both, // كلاهما
}
