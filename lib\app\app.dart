import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'package:provider/provider.dart';
import 'routes.dart';
import '../design_system/theme_provider.dart';
import '../localization/app_localizations.dart';
import '../services/language_service.dart';
import '../services/app_initialization_service.dart';

class WallpaperApp extends StatefulWidget {
  const WallpaperApp({super.key});

  @override
  State<WallpaperApp> createState() => _WallpaperAppState();
}

// Alias للتوافق مع الكود القديم
typedef MyApp = WallpaperApp;

class _WallpaperAppState extends State<WallpaperApp>
    with WidgetsBindingObserver {
  final _themeProvider = AppThemeProvider();
  final _languageService = LanguageService();
  final _initService = AppInitializationService();
  @override
  void initState() {
    super.initState();

    // تعطيل رسم الإطار الأول حتى نكمل الإعدادات الأساسية
    final binding = WidgetsBinding.instance;
    binding.deferFirstFrame();
    binding.addObserver(this);

    // تهيئة خدمة اللغة فوراً (ضرورية لواجهة المستخدم) - بدون انتظار
    _initLanguageService();

    // السماح برسم الإطار الأول
    binding.allowFirstFrame();
  }

  @override
  void dispose() {
    // إغلاق جميع الخيوط النشطة
    _initService.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // تحديث الخدمات عند استئناف التطبيق إذا لزم الأمر
      _initService.initializeBackgroundServices();
    }
  }

  // تهيئة خدمة اللغة (ضرورية لواجهة المستخدم)
  void _initLanguageService() {
    try {
      // تهيئة خدمة اللغة فوراً بدون انتظار
      // استخدام microtask للتنفيذ بأولوية عالية
      Future.microtask(() {
        _languageService.init();
        debugPrint('✅ تم تهيئة خدمة اللغة بنجاح');
      });
    } catch (e) {
      debugPrint('❌ فشل تهيئة خدمة اللغة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: _themeProvider),
        ChangeNotifierProvider.value(value: _languageService),
      ],
      child: Consumer2<AppThemeProvider, LanguageService>(
        builder: (context, themeProvider, languageService, _) {
          return _buildApp(themeProvider, languageService);
        },
      ),
    );
  }

  // بناء التطبيق مع دعم الألوان الديناميكية
  Widget _buildApp(
    AppThemeProvider themeProvider,
    LanguageService languageService,
  ) {
    return DynamicColorBuilder(
      builder: (ColorScheme? lightDynamic, ColorScheme? darkDynamic) {
        // استخدام الثيم الموحد
        final ThemeData theme = AppThemeProvider.darkTheme;

        return MaterialApp.router(
          title: 'خلفيات إسلامية',
          debugShowCheckedModeBanner: false,
          theme: theme,
          themeMode: themeProvider.themeMode,
          routerConfig: AppRouter.router,
          // استخدام مفتاح التنقل العام من AppRouter
          localizationsDelegates: const [
            AppLocalizationsDelegate(),
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [
            Locale('ar', ''), // العربية فقط
          ],
          locale: languageService.currentLocale,
          // تعيين اتجاه النص دائمًا من اليمين إلى اليسار
          builder: (context, child) {
            // تطبيق اتجاه RTL على كل التطبيق بغض النظر عن اللغة
            return Directionality(
              textDirection: TextDirection.rtl,
              child: child!,
            );
          },
        );
      },
    );
  }
}
