import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../design_system/colors.dart';
import '../../design_system/dimensions.dart';
import '../../design_system/shadows.dart';
import '../../design_system/typography.dart';

/// عرض مربع حوار تأكيد الخروج من التطبيق
Future<bool> showExitDialog(BuildContext context) async {
  final result = await showDialog<bool>(
    context: context,
    barrierDismissible: true,
    barrierColor: Colors.black.withOpacity(0.7),
    builder: (context) => const ExitDialog(),
  );

  return result ?? false;
}

/// مربع حوار تأكيد الخروج من التطبيق
class ExitDialog extends StatelessWidget {
  const ExitDialog({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      insetPadding: const EdgeInsets.symmetric(horizontal: 24),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          border: Border.all(color: Colors.white.withOpacity(0.2), width: 1.5),
          boxShadow: AppShadows.large,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // رأس مربع الحوار
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingMedium),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primary.withOpacity(0.7),
                    AppColors.primaryDark.withOpacity(0.9),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.radiusLarge),
                  topRight: Radius.circular(AppDimensions.radiusLarge),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.exit_to_app,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: AppDimensions.marginSmall),
                  Text(
                    'تأكيد الخروج',
                    style: AppTypography.titleMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // محتوى مربع الحوار
            Padding(
              padding: const EdgeInsets.all(AppDimensions.paddingLarge),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة الخروج
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.3),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.exit_to_app,
                      color: AppColors.primary,
                      size: 40,
                    ),
                  ).animate().scale(
                    duration: const Duration(milliseconds: 400),
                    curve: Curves.easeOutBack,
                    begin: const Offset(0.5, 0.5),
                    end: const Offset(1.0, 1.0),
                  ),

                  const SizedBox(height: AppDimensions.marginLarge),

                  // نص السؤال
                  Text(
                    'هل تريد الخروج من التطبيق؟',
                    style: AppTypography.titleMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ).animate().fadeIn(
                    duration: const Duration(milliseconds: 300),
                    delay: const Duration(milliseconds: 200),
                  ),

                  const SizedBox(height: AppDimensions.marginMedium),

                  // نص إضافي
                  Text(
                    'يمكنك العودة في أي وقت لاستكشاف المزيد من الخلفيات الإسلامية الرائعة',
                    style: AppTypography.bodySmall.copyWith(
                      color: AppColors.textMuted,
                    ),
                    textAlign: TextAlign.center,
                  ).animate().fadeIn(
                    duration: const Duration(milliseconds: 300),
                    delay: const Duration(milliseconds: 300),
                  ),

                  const SizedBox(height: AppDimensions.marginXLarge),

                  // أزرار الإجراءات
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // زر البقاء
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.of(context).pop(false),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.white,
                            side: BorderSide(
                              color: Colors.white.withOpacity(0.3),
                              width: 1.5,
                            ),
                            padding: const EdgeInsets.symmetric(
                              vertical: AppDimensions.paddingMedium,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                AppDimensions.radiusSmall,
                              ),
                            ),
                          ),
                          child: const Text('البقاء'),
                        ),
                      ),

                      const SizedBox(width: AppDimensions.marginMedium),

                      // زر الخروج
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(true),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              vertical: AppDimensions.paddingMedium,
                            ),
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                AppDimensions.radiusSmall,
                              ),
                            ),
                          ),
                          child: const Text('خروج'),
                        ),
                      ),
                    ],
                  ).animate().fadeIn(
                    duration: const Duration(milliseconds: 300),
                    delay: const Duration(milliseconds: 400),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
