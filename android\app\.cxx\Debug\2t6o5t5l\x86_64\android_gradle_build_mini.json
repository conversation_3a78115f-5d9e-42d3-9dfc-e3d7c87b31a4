{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\Users\\android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AndroidStudioProjects\\Slmeh.Pro\\android\\app\\.cxx\\Debug\\2t6o5t5l\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\Users\\android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\AndroidStudioProjects\\Slmeh.Pro\\android\\app\\.cxx\\Debug\\2t6o5t5l\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}