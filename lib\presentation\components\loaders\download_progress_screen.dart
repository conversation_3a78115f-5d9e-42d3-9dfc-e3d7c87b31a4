import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';
import '../effects/glass_container.dart';

/// شاشة تحميل الخلفيات المحسنة مع شريط تقدم وزر إلغاء
class DownloadProgressScreen extends StatelessWidget {
  final String loadingText;
  final double progress;
  final VoidCallback? onCancel;
  final bool showCancelButton;

  const DownloadProgressScreen({
    super.key,
    this.loadingText = 'جاري تحميل الخلفية...',
    required this.progress,
    this.onCancel,
    this.showCancelButton = true,
  });

  @override
  Widget build(BuildContext context) {
    // حساب أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;
    final screenWidth = screenSize.width;
    final screenHeight = screenSize.height;

    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: AppColors.backgroundGradient,
        ),
      ),
      child: Stack(
        children: [
          // المحتوى الرئيسي
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // أيقونة التحميل
                _buildDownloadIcon(screenWidth),

                // مسافة ديناميكية
                SizedBox(height: screenHeight * 0.04),

                // نص التحميل
                Text(
                  loadingText,
                  style: AppTypography.titleMedium.copyWith(
                    color: AppColors.textPrimary,
                    fontSize: screenWidth * 0.045,
                  ),
                  textAlign: TextAlign.center,
                ),

                // مسافة ديناميكية
                SizedBox(height: screenHeight * 0.03),

                // شريط التقدم
                _buildProgressBar(screenWidth),

                // مسافة ديناميكية
                SizedBox(height: screenHeight * 0.02),

                // نسبة التقدم
                Text(
                  '${(progress * 100).toInt()}%',
                  style: AppTypography.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: screenWidth * 0.035,
                  ),
                ),

                // مسافة ديناميكية
                SizedBox(height: screenHeight * 0.06),

                // زر الإلغاء
                if (showCancelButton) _buildCancelButton(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء أيقونة التحميل
  Widget _buildDownloadIcon(double screenWidth) {
    final iconSize = (screenWidth * 0.25).clamp(100.0, 140.0);
    final innerIconSize = iconSize * 0.45;

    return Container(
      width: iconSize,
      height: iconSize,
      decoration: BoxDecoration(
        color: AppColors.surface,
        shape: BoxShape.circle,
        boxShadow: AppShadows.medium,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF1A1A1A), Color(0xFF0A0A0A)],
        ),
      ),
      child: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            // حلقة التقدم
            SizedBox(
              width: innerIconSize * 1.4,
              height: innerIconSize * 1.4,
              child: CircularProgressIndicator(
                value: progress,
                strokeWidth: 3,
                backgroundColor: AppColors.surface,
                valueColor: const AlwaysStoppedAnimation<Color>(
                  AppColors.primary,
                ),
              ),
            ),

            // أيقونة التحميل
            Icon(
              Icons.image_outlined,
              color: AppColors.primary,
              size: innerIconSize,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شريط التقدم
  Widget _buildProgressBar(double screenWidth) {
    final progressWidth = screenWidth * 0.6;

    return Container(
      width: progressWidth,
      height: 8,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Stack(
        children: [
          // شريط التقدم
          FractionallySizedBox(
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [AppColors.primary, AppColors.primaryLight],
                ),
                borderRadius: BorderRadius.circular(4),
                boxShadow: [
                  BoxShadow(
                    color: Colors.blue.withOpacity(0.5),
                    blurRadius: 6,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر الإلغاء
  Widget _buildCancelButton(BuildContext context) {
    return GlassContainer(
      borderRadius: AppDimensions.radiusLarge,
      blur: 10,
      opacity: 0.1,
      border: 1,
      borderColor: Colors.red.withOpacity(0.5),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onCancel,
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingLarge,
              vertical: AppDimensions.paddingMedium,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.close_rounded, color: AppColors.error, size: 20),
                const SizedBox(width: 8),
                Text(
                  'إلغاء',
                  style: AppTypography.labelLarge.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
