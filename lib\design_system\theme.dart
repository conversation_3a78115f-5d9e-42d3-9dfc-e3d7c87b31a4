import 'package:flutter/material.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'colors.dart';
import 'typography.dart';
import 'dimensions.dart';
import 'animations.dart';

/// نظام الثيم الموحد للتطبيق
class AppTheme {
  // الثيم الداكن للتطبيق
  static ThemeData get darkTheme => ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    primaryColor: AppColors.primary,
    scaffoldBackgroundColor: AppColors.background,
    cardColor: AppColors.card,
    dividerColor: AppColors.divider,
    colorScheme: const ColorScheme.dark(
      primary: AppColors.primary,
      primaryContainer: AppColors.primaryDark,
      secondary: AppColors.secondary,
      secondaryContainer: AppColors.secondaryDark,
      tertiary: AppColors.accent,
      tertiaryContainer: AppColors.accentDark,
      surface: AppColors.surface,
      surfaceVariant: AppColors.surfaceLight,
      background: AppColors.background,
      error: AppColors.error,
      onPrimary: AppColors.textPrimary,
      onSecondary: AppColors.textPrimary,
      onTertiary: Colors.black,
      onSurface: AppColors.textPrimary,
      onBackground: AppColors.textPrimary,
      onError: AppColors.textPrimary,
    ),
    textTheme: AppTypography.textTheme,
    
    // تكوين شريط التطبيق
    appBarTheme: AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      titleTextStyle: AppTypography.titleLarge,
      iconTheme: const IconThemeData(color: AppColors.textPrimary),
      scrolledUnderElevation: 0,
    ),
    
    // تكوين شريط التنقل السفلي
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColors.navBackground,
      selectedItemColor: AppColors.navActiveIcon,
      unselectedItemColor: AppColors.navInactiveIcon,
      showSelectedLabels: true,
      showUnselectedLabels: true,
      type: BottomNavigationBarType.fixed,
      elevation: 0,
    ),
    
    // تكوين الأزرار المرتفعة
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primary,
        foregroundColor: AppColors.textPrimary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingLarge,
          vertical: AppDimensions.paddingMedium,
        ),
        textStyle: AppTypography.labelLarge,
        elevation: AppDimensions.elevationSmall,
      ),
    ),
    
    // تكوين الأزرار المحددة
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primary,
        side: const BorderSide(color: AppColors.primary),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingLarge,
          vertical: AppDimensions.paddingMedium,
        ),
        textStyle: AppTypography.labelLarge,
      ),
    ),
    
    // تكوين أزرار النص
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingMedium,
          vertical: AppDimensions.paddingSmall,
        ),
        textStyle: AppTypography.labelLarge,
      ),
    ),
    
    // تكوين البطاقات
    cardTheme: CardTheme(
      color: AppColors.surface,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
      ),
      clipBehavior: Clip.antiAlias,
      margin: const EdgeInsets.all(AppDimensions.marginSmall),
    ),
    
    // تكوين شريط الإشعارات
    snackBarTheme: SnackBarThemeData(
      backgroundColor: AppColors.surface,
      contentTextStyle: AppTypography.bodyMedium,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
      ),
      behavior: SnackBarBehavior.floating,
    ),
    
    // تكوين مربعات الحوار
    dialogTheme: DialogTheme(
      backgroundColor: AppColors.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
      ),
      titleTextStyle: AppTypography.headingSmall,
      contentTextStyle: AppTypography.bodyMedium,
      elevation: AppDimensions.elevationLarge,
    ),
    
    // تكوين حقول الإدخال
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.card,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        borderSide: const BorderSide(color: AppColors.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        borderSide: const BorderSide(color: AppColors.error, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
        vertical: AppDimensions.paddingMedium,
      ),
      hintStyle: AppTypography.bodyMedium.copyWith(color: AppColors.textMuted),
      labelStyle: AppTypography.bodyMedium,
    ),
    
    // تكوين مربعات الاختيار
    checkboxTheme: CheckboxThemeData(
      fillColor: MaterialStateProperty.resolveWith<Color>((states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.primary;
        }
        return AppColors.card;
      }),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusXSmall),
      ),
    ),
    
    // تكوين مفاتيح التبديل
    switchTheme: SwitchThemeData(
      thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.primary;
        }
        return AppColors.textMuted;
      }),
      trackColor: MaterialStateProperty.resolveWith<Color>((states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.primary.withOpacity(0.5);
        }
        return AppColors.card;
      }),
    ),
    
    // تكوين مؤشرات التقدم
    progressIndicatorTheme: const ProgressIndicatorThemeData(
      color: AppColors.primary,
      circularTrackColor: AppColors.card,
    ),
    
    // تكوين الشرائح
    chipTheme: ChipThemeData(
      backgroundColor: AppColors.card,
      labelStyle: AppTypography.bodySmall,
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
        vertical: AppDimensions.paddingSmall,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
      ),
    ),
    
    // تكوين الانتقالات بين الصفحات
    pageTransitionsTheme: PageTransitionsTheme(
      builders: {
        TargetPlatform.android: AppAnimations.pageTransition,
        TargetPlatform.iOS: AppAnimations.pageTransition,
      },
    ),
  );
  
  // إنشاء ثيم ديناميكي يعتمد على Material You (Monet) للأجهزة الحديثة (أندرويد 12+)
  static ThemeData createDynamicTheme(ColorScheme dynamicColorScheme) {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: dynamicColorScheme,
      textTheme: AppTypography.textTheme,
      
      // تطبيق نفس إعدادات الثيم الداكن مع استخدام الألوان الديناميكية
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: AppTypography.titleLarge,
        iconTheme: IconThemeData(color: dynamicColorScheme.onSurface),
        scrolledUnderElevation: 0,
      ),
      
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: dynamicColorScheme.surfaceContainerLow,
        selectedItemColor: dynamicColorScheme.primary,
        unselectedItemColor: dynamicColorScheme.onSurfaceVariant,
        showSelectedLabels: true,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
        elevation: 0,
      ),
      
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: dynamicColorScheme.primary,
          foregroundColor: dynamicColorScheme.onPrimary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingLarge,
            vertical: AppDimensions.paddingMedium,
          ),
          textStyle: AppTypography.labelLarge,
          elevation: AppDimensions.elevationSmall,
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: dynamicColorScheme.primary,
          side: BorderSide(color: dynamicColorScheme.primary),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingLarge,
            vertical: AppDimensions.paddingMedium,
          ),
          textStyle: AppTypography.labelLarge,
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: dynamicColorScheme.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
            vertical: AppDimensions.paddingSmall,
          ),
          textStyle: AppTypography.labelLarge,
        ),
      ),
      
      cardTheme: CardTheme(
        color: dynamicColorScheme.surfaceContainerHigh,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        ),
        clipBehavior: Clip.antiAlias,
        margin: const EdgeInsets.all(AppDimensions.marginSmall),
      ),
      
      snackBarTheme: SnackBarThemeData(
        backgroundColor: dynamicColorScheme.surfaceContainerHigh,
        contentTextStyle: AppTypography.bodyMedium,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        ),
        behavior: SnackBarBehavior.floating,
      ),
      
      dialogTheme: DialogTheme(
        backgroundColor: dynamicColorScheme.surfaceContainerHigh,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        ),
        titleTextStyle: AppTypography.headingSmall,
        contentTextStyle: AppTypography.bodyMedium,
        elevation: AppDimensions.elevationLarge,
      ),
      
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: dynamicColorScheme.surfaceContainerHighest,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: dynamicColorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
          borderSide: BorderSide(color: dynamicColorScheme.error, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingMedium,
          vertical: AppDimensions.paddingMedium,
        ),
        hintStyle: AppTypography.bodyMedium.copyWith(
          color: dynamicColorScheme.onSurfaceVariant,
        ),
        labelStyle: AppTypography.bodyMedium,
      ),
      
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return dynamicColorScheme.primary;
          }
          return dynamicColorScheme.surfaceContainerHighest;
        }),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusXSmall),
        ),
      ),
      
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return dynamicColorScheme.primary;
          }
          return dynamicColorScheme.onSurfaceVariant;
        }),
        trackColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return dynamicColorScheme.primary.withAlpha(128);
          }
          return dynamicColorScheme.surfaceContainerHighest;
        }),
      ),
      
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: dynamicColorScheme.primary,
        circularTrackColor: dynamicColorScheme.surfaceContainerHighest,
      ),
      
      chipTheme: ChipThemeData(
        backgroundColor: dynamicColorScheme.surfaceContainerHighest,
        labelStyle: AppTypography.bodySmall,
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingMedium,
          vertical: AppDimensions.paddingSmall,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        ),
      ),
      
      pageTransitionsTheme: PageTransitionsTheme(
        builders: {
          TargetPlatform.android: AppAnimations.pageTransition,
          TargetPlatform.iOS: AppAnimations.pageTransition,
        },
      ),
    );
  }
}
