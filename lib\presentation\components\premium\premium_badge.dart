import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../design_system/colors.dart';
import '../../../data/services/subscription_service.dart';

/// شارة لعرض حالة المحتوى المدفوع
class PremiumBadge extends StatelessWidget {
  final double size;
  final bool isLocked;

  const PremiumBadge({Key? key, this.size = 16, this.isLocked = true})
    : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<bool>(
          future: SubscriptionService().isSubscribed(),
          builder: (context, snapshot) {
            final isSubscribed = snapshot.data ?? false;

            // إذا كان المستخدم مشتركًا، نعرض شارة مختلفة
            if (isSubscribed) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Color(0xFF1E8449), Color(0xFF27AE60)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF27AE60).withOpacity(0.5),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: size * 0.7,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'متاح',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: size * 0.5,
                      ),
                    ),
                  ],
                ),
              );
            }

            // للمستخدمين غير المشتركين، نعرض شارة القفل
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: AppColors.goldGradient,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.accent.withOpacity(0.5),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isLocked ? Icons.lock : Icons.workspace_premium,
                    color: Colors.black,
                    size: size * 0.7,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    isLocked ? 'مميز' : 'متاح',
                    style: TextStyle(
                      color: Colors.black,
                      fontWeight: FontWeight.bold,
                      fontSize: size * 0.5,
                    ),
                  ),
                ],
              ),
            );
          },
        )
        .animate(onPlay: (controller) => controller.repeat(reverse: true))
        .shimmer(
          duration: const Duration(seconds: 2),
          color: Colors.white.withAlpha(150),
          curve: Curves.easeInOut,
        );
  }
}
