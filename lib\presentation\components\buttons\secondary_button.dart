import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';
import '../../../design_system/typography.dart';

/// زر ثانوي بتصميم متطور
class SecondaryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final double height;
  final Color? backgroundColor;
  final Color? textColor;
  final List<BoxShadow>? boxShadow;
  final double borderRadius;
  final EdgeInsetsGeometry padding;
  
  const SecondaryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.height = AppDimensions.buttonHeight,
    this.backgroundColor,
    this.textColor,
    this.boxShadow,
    this.borderRadius = AppDimensions.radiusMedium,
    this.padding = const EdgeInsets.symmetric(
      horizontal: AppDimensions.paddingLarge,
      vertical: AppDimensions.paddingMedium,
    ),
  });
  
  @override
  Widget build(BuildContext context) {
    final isDisabled = onPressed == null || isLoading;
    final bgColor = backgroundColor ?? AppColors.secondary;
    final txtColor = textColor ?? AppColors.textPrimary;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: isFullWidth ? double.infinity : null,
      height: height,
      decoration: BoxDecoration(
        color: isDisabled ? bgColor.withOpacity(0.6) : bgColor,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: isDisabled ? null : (boxShadow ?? AppShadows.secondary),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isDisabled ? null : onPressed,
          borderRadius: BorderRadius.circular(borderRadius),
          splashColor: Colors.white24,
          highlightColor: Colors.white10,
          child: Padding(
            padding: padding,
            child: Center(
              child: isLoading
                  ? SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.5,
                        valueColor: AlwaysStoppedAnimation<Color>(txtColor),
                      ),
                    )
                  : Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (icon != null) ...[
                          Icon(icon, color: txtColor, size: 20),
                          const SizedBox(width: AppDimensions.marginSmall),
                        ],
                        Text(
                          text,
                          style: AppTypography.labelLarge.copyWith(color: txtColor),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
            ),
          ),
        ),
      ),
    );
  }
}
