import 'package:flutter/material.dart';

/// نظام الرسوم المتحركة الموحد للتطبيق
class AppAnimations {
  // مدة الرسوم المتحركة
  static const Duration fast = Duration(milliseconds: 200);
  static const Duration medium = Duration(milliseconds: 300);
  static const Duration slow = Duration(milliseconds: 500);
  
  // منحنيات الرسوم المتحركة
  static const Curve standard = Curves.easeInOut;
  static const Curve emphasized = Curves.easeOutCubic;
  static const Curve decelerate = Curves.easeOutCirc;
  static const Curve accelerate = Curves.easeInCirc;
  static const Curve bounce = Curves.elasticOut;
  
  // تأخيرات الرسوم المتحركة
  static const Duration noDelay = Duration.zero;
  static const Duration shortDelay = Duration(milliseconds: 50);
  static const Duration mediumDelay = Duration(milliseconds: 100);
  static const Duration longDelay = Duration(milliseconds: 200);
  
  // تكوينات الرسوم المتحركة الشائعة
  static AnimationConfig get fadeIn => AnimationConfig(
    duration: medium,
    curve: standard,
    delay: noDelay,
  );
  
  static AnimationConfig get fadeInSlow => AnimationConfig(
    duration: slow,
    curve: decelerate,
    delay: shortDelay,
  );
  
  static AnimationConfig get slideIn => AnimationConfig(
    duration: medium,
    curve: emphasized,
    delay: noDelay,
  );
  
  static AnimationConfig get scaleIn => AnimationConfig(
    duration: medium,
    curve: emphasized,
    delay: noDelay,
  );
  
  static AnimationConfig get staggered => AnimationConfig(
    duration: medium,
    curve: standard,
    delay: mediumDelay,
  );
  
  // تكوينات الانتقالات بين الصفحات
  static PageTransitionsBuilder get pageTransition => 
    const ZoomPageTransitionsBuilder(
      allowEnterRouteSnapshotting: false,
    );
  
  // تكوينات الانتقالات المشتركة
  static SharedAxisTransitionType get sharedAxisTransitionType => 
    SharedAxisTransitionType.scaled;
  
  static Duration get sharedAxisTransitionDuration => medium;
}

/// تكوين الرسوم المتحركة
class AnimationConfig {
  final Duration duration;
  final Curve curve;
  final Duration delay;
  
  const AnimationConfig({
    required this.duration,
    required this.curve,
    required this.delay,
  });
}

/// أنواع انتقالات المحور المشترك
enum SharedAxisTransitionType {
  vertical,
  horizontal,
  scaled,
}
