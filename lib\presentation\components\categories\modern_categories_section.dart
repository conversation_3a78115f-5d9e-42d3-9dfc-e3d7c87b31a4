import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:go_router/go_router.dart';
import '../../../data/models/wallpaper.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/shadows.dart';
import '../loaders/shimmer_loader.dart';

/// قسم الفئات بتصميم عصري
class ModernCategoriesSection extends StatelessWidget {
  final List<Category> categories;
  final bool isLoading;
  final String title;
  final VoidCallback? onSeeAllPressed;
  final CategoryStyle style;

  const ModernCategoriesSection({
    Key? key,
    required this.categories,
    this.isLoading = false,
    this.title = 'الفئات',
    this.onSeeAllPressed,
    this.style = CategoryStyle.card,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        _buildSectionHeader(context),

        // عرض الفئات
        _buildCategoriesContent(context),
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(
        AppDimensions.paddingMedium,
        AppDimensions.paddingSmall, // تقليل الهامش العلوي
        AppDimensions.paddingMedium,
        AppDimensions.paddingSmall, // تقليل الهامش السفلي
      ),
      child: Row(
        textDirection: TextDirection.rtl,
        children: [
          // أيقونة الفئات
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              color: AppColors.accent.withAlpha(40),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.category_rounded,
              color: AppColors.accent,
              size: 20,
            ),
          ),
          const SizedBox(width: AppDimensions.marginSmall),

          // عنوان القسم
          Text(
            title,
            style: AppTypography.titleMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
              shadows: AppShadows.textSmall,
            ),
          ),

          const Spacer(),

          // زر عرض الكل
          if (onSeeAllPressed != null)
            TextButton(
              onPressed: onSeeAllPressed,
              style: TextButton.styleFrom(
                foregroundColor: AppColors.accent,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingSmall,
                  vertical: AppDimensions.paddingXSmall,
                ),
              ),
              child: Row(
                children: [
                  Text(
                    'عرض الكل',
                    style: AppTypography.labelMedium.copyWith(
                      color: AppColors.accent,
                    ),
                  ),
                  const SizedBox(width: 4),
                  const Icon(Icons.arrow_forward_ios_rounded, size: 12),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCategoriesContent(BuildContext context) {
    if (isLoading) {
      return _buildLoadingCategories();
    }

    if (style == CategoryStyle.pill) {
      return _buildPillCategories(context);
    } else if (style == CategoryStyle.grid) {
      return _buildGridCategories(context);
    } else {
      return _buildCardCategories(context);
    }
  }

  Widget _buildLoadingCategories() {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingMedium,
        ),
        itemCount: 5, // عدد العناصر الوهمية
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.only(
              right: index == 0 ? 0 : AppDimensions.paddingMedium,
            ),
            child: CategoryCardShimmer(
              width: 120,
              height: 120,
              isCircular: style == CategoryStyle.card,
            ),
          );
        },
      ),
    );
  }

  // تصميم بطاقات الفئات
  Widget _buildCardCategories(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final cardWidth = screenSize.width * 0.35; // تقليل عرض البطاقة قليلاً

    return SizedBox(
      height: cardWidth * 1.2, // تقليل ارتفاع القسم أكثر
      child: AnimationLimiter(
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
          ),
          itemCount: categories.length,
          cacheExtent: 1000,
          itemBuilder: (context, index) {
            final category = categories[index];

            // تحميل مسبق للصورة
            if (category.imageUrl.isNotEmpty) {
              precacheImage(
                CachedNetworkImageProvider(category.imageUrl),
                context,
              );
            }

            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 500),
              child: SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: Padding(
                    padding: EdgeInsets.only(
                      right: index == 0 ? 0 : AppDimensions.paddingMedium,
                    ),
                    child: ModernCategoryCard(
                      category: category,
                      width: cardWidth,
                      height: cardWidth * 1.1, // تقليل نسبة الارتفاع أكثر
                      onTap: () {
                        context.push(
                          '/category/${category.id}',
                          extra: category,
                        );
                      },
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // تصميم فئات على شكل حبوب
  Widget _buildPillCategories(BuildContext context) {
    return SizedBox(
      height: 50,
      child: AnimationLimiter(
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
          ),
          itemCount: categories.length,
          cacheExtent: 1000,
          itemBuilder: (context, index) {
            final category = categories[index];

            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(milliseconds: 500),
              child: SlideAnimation(
                horizontalOffset: 50.0,
                child: FadeInAnimation(
                  child: Padding(
                    padding: EdgeInsets.only(
                      right: index == 0 ? 0 : AppDimensions.paddingMedium,
                    ),
                    child: CategoryPill(
                      category: category,
                      onTap: () {
                        context.push(
                          '/category/${category.id}',
                          extra: category,
                        );
                      },
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // تصميم فئات على شكل شبكة
  Widget _buildGridCategories(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final itemWidth =
        (screenSize.width -
            (AppDimensions.paddingMedium * 2) -
            (AppDimensions.paddingMedium * 2)) /
        3;

    return SizedBox(
      height: (itemWidth * 2) + AppDimensions.paddingMedium,
      child: AnimationLimiter(
        child: GridView.builder(
          scrollDirection: Axis.horizontal,
          physics: const BouncingScrollPhysics(),
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
          ),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            mainAxisSpacing: AppDimensions.paddingMedium,
            crossAxisSpacing: AppDimensions.paddingMedium,
            childAspectRatio: 1,
          ),
          itemCount: categories.length,
          cacheExtent: 1000,
          itemBuilder: (context, index) {
            final category = categories[index];

            return AnimationConfiguration.staggeredGrid(
              position: index,
              columnCount: 2,
              duration: const Duration(milliseconds: 500),
              child: ScaleAnimation(
                child: FadeInAnimation(
                  child: CategoryGridItem(
                    category: category,
                    onTap: () {
                      context.push('/category/${category.id}', extra: category);
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

/// أنماط عرض الفئات
enum CategoryStyle {
  card, // بطاقات
  pill, // حبوب
  grid, // شبكة
}

/// بطاقة فئة عصرية
class ModernCategoryCard extends StatelessWidget {
  final Category category;
  final double width;
  final double height;
  final VoidCallback? onTap;

  const ModernCategoryCard({
    super.key,
    required this.category,
    required this.width,
    required this.height,
    this.onTap,
  });

  // بناء صورة الفئة (محلية أو من الإنترنت)
  Widget _buildCategoryImage(Category category) {
    final imageUrl = category.imageUrl;

    // التحقق مما إذا كانت الصورة محلية
    if (imageUrl.startsWith('assets/')) {
      return Image.asset(imageUrl, fit: BoxFit.cover);
    } else {
      // صورة من الإنترنت
      return CachedNetworkImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover,
        placeholder:
            (context, url) => Container(
              color: (category.color ?? Colors.blue).withAlpha(50),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 2,
                ),
              ),
            ),
        errorWidget:
            (context, url, error) => Container(
              color: (category.color ?? Colors.blue).withAlpha(50),
              child: const Icon(
                Icons.error_outline_rounded,
                color: Colors.white,
              ),
            ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
            width: width,
            height: height,
            decoration: BoxDecoration(
              color: AppColors.card,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(40),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // صورة الفئة
                  if (category.imageUrl.isNotEmpty)
                    _buildCategoryImage(category),

                  // تراكب تدرج
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withAlpha(150),
                          Colors.black.withAlpha(200),
                        ],
                        stops: const [0.6, 0.8, 1.0],
                      ),
                    ),
                  ),

                  // اسم الفئة
                  Positioned(
                    bottom: 12,
                    right: 12,
                    left: 12,
                    child: Text(
                      category.name,
                      style: AppTypography.titleSmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        shadows: AppShadows.textMedium,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // تأثير عند الضغط
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: onTap,
                      splashColor: Colors.white.withAlpha(30),
                      highlightColor: Colors.white.withAlpha(20),
                    ),
                  ),
                ],
              ),
            ),
          )
          .animate()
          .fadeIn(duration: const Duration(milliseconds: 500))
          .scale(
            begin: const Offset(0.9, 0.9),
            end: const Offset(1, 1),
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOutBack,
          ),
    );
  }
}

/// فئة على شكل حبة
class CategoryPill extends StatelessWidget {
  final Category category;
  final VoidCallback? onTap;

  const CategoryPill({super.key, required this.category, this.onTap});

  // بناء أيقونة الفئة (محلية أو من الإنترنت)
  Widget _buildCategoryIcon() {
    final imageUrl = category.imageUrl;

    return Container(
      width: 24,
      height: 24,
      margin: const EdgeInsets.only(left: 8),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        image: DecorationImage(
          image:
              imageUrl.startsWith('assets/')
                  ? AssetImage(imageUrl) as ImageProvider
                  : CachedNetworkImageProvider(imageUrl),
          fit: BoxFit.cover,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
            height: 40,
            padding: const EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingMedium,
            ),
            decoration: BoxDecoration(
              color: (category.color ?? AppColors.accent).withAlpha(40),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: (category.color ?? AppColors.accent).withAlpha(100),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // أيقونة الفئة
                if (category.imageUrl.isNotEmpty)
                  _buildCategoryIcon()
                else
                  Container(
                    width: 24,
                    height: 24,
                    margin: const EdgeInsets.only(left: 8),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: (category.color ?? AppColors.accent).withAlpha(
                        100,
                      ),
                    ),
                    child: const Icon(
                      Icons.category_rounded,
                      color: Colors.white,
                      size: 14,
                    ),
                  ),

                // اسم الفئة
                Text(
                  category.name,
                  style: AppTypography.labelMedium.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          )
          .animate()
          .fadeIn(duration: const Duration(milliseconds: 500))
          .scale(
            begin: const Offset(0.9, 0.9),
            end: const Offset(1, 1),
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOutBack,
          ),
    );
  }
}

/// فئة على شكل عنصر شبكة
class CategoryGridItem extends StatelessWidget {
  final Category category;
  final VoidCallback? onTap;

  const CategoryGridItem({super.key, required this.category, this.onTap});

  // بناء صورة الفئة (محلية أو من الإنترنت)
  Widget _buildCategoryImage(Category category) {
    final imageUrl = category.imageUrl;

    // التحقق مما إذا كانت الصورة محلية
    if (imageUrl.startsWith('assets/')) {
      return Image.asset(imageUrl, fit: BoxFit.cover);
    } else {
      // صورة من الإنترنت
      return CachedNetworkImage(
        imageUrl: imageUrl,
        fit: BoxFit.cover,
        placeholder:
            (context, url) => Container(
              color: (category.color ?? Colors.blue).withAlpha(50),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  strokeWidth: 2,
                ),
              ),
            ),
        errorWidget:
            (context, url, error) => Container(
              color: (category.color ?? Colors.blue).withAlpha(50),
              child: const Icon(
                Icons.error_outline_rounded,
                color: Colors.white,
              ),
            ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
            decoration: BoxDecoration(
              color: AppColors.card,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(40),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: Stack(
                fit: StackFit.expand,
                children: [
                  // صورة الفئة
                  if (category.imageUrl.isNotEmpty)
                    _buildCategoryImage(category),

                  // تراكب تدرج
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withAlpha(150),
                          Colors.black.withAlpha(200),
                        ],
                        stops: const [0.6, 0.8, 1.0],
                      ),
                    ),
                  ),

                  // اسم الفئة
                  Positioned(
                    bottom: 12,
                    right: 12,
                    left: 12,
                    child: Text(
                      category.name,
                      style: AppTypography.labelMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        shadows: AppShadows.textMedium,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // تأثير عند الضغط
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: onTap,
                      splashColor: Colors.white.withAlpha(30),
                      highlightColor: Colors.white.withAlpha(20),
                    ),
                  ),
                ],
              ),
            ),
          )
          .animate()
          .fadeIn(duration: const Duration(milliseconds: 500))
          .scale(
            begin: const Offset(0.9, 0.9),
            end: const Offset(1, 1),
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOutBack,
          ),
    );
  }
}
