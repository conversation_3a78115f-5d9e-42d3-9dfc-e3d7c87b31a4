import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../../../data/services/ad_service.dart';
import '../../../design_system/colors.dart';
import '../../../services/remote_config_service.dart';

/// مكون لعرض إعلانات البانر مع التحقق من حالة الاشتراك
class AdBanner extends StatefulWidget {
  final double height;
  final bool isSmallBanner;

  const AdBanner({super.key, this.height = 60, this.isSmallBanner = false});

  @override
  State<AdBanner> createState() => _AdBannerState();
}

class _AdBannerState extends State<AdBanner> {
  final AdService _adService = AdService();
  final RemoteConfigService _remoteConfigService = RemoteConfigService();
  bool _isLoading = true;
  bool _shouldHideAds = false;
  BannerAd? _bannerAd;

  @override
  void initState() {
    super.initState();
    _initServices();
  }

  /// تهيئة الخدمات
  Future<void> _initServices() async {
    try {
      // تهيئة خدمة التحكم عن بعد إذا لم تكن مهيأة
      if (!_remoteConfigService.isInitialized) {
        await _remoteConfigService.initialize();
      }

      // التحقق من حالة الإعلانات
      _checkAdStatus();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمات: $e');
      _checkAdStatus();
    }
  }

  @override
  void dispose() {
    _bannerAd?.dispose();
    super.dispose();
  }

  Future<void> _checkAdStatus() async {
    try {
      // التحقق من إعدادات التحكم عن بعد
      final bool showBannerAds =
          _remoteConfigService.isInitialized &&
          _remoteConfigService.config.showBannerAds;

      // التحقق من حالة الاشتراك
      final shouldShowAds = await _adService.shouldShowAds();

      // إذا كان المستخدم مشترك أو الإعلانات معطلة من التحكم عن بعد
      if (!shouldShowAds || !showBannerAds) {
        if (mounted) {
          setState(() {
            _shouldHideAds = true;
            _isLoading = false;
          });
        }
        return;
      }

      // تحميل إعلان البانر
      final bannerAd = await _adService.loadBannerAd();

      if (mounted) {
        setState(() {
          _bannerAd = bannerAd;
          _shouldHideAds = bannerAd == null;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من حالة الإعلانات: $e');
      if (mounted) {
        setState(() {
          _shouldHideAds = true;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // إذا كان المستخدم مشتركًا، لا نعرض الإعلانات
    if (_shouldHideAds) {
      return const SizedBox.shrink();
    }

    // أثناء التحميل، نعرض مساحة فارغة
    if (_isLoading) {
      return SizedBox(height: widget.height);
    }

    // إذا تم تحميل الإعلان بنجاح
    if (_bannerAd != null) {
      return Container(
        height: _bannerAd!.size.height.toDouble(),
        width: _bannerAd!.size.width.toDouble(),
        alignment: Alignment.center,
        child: AdWidget(ad: _bannerAd!),
      );
    }

    // إذا فشل تحميل الإعلان، نعرض مساحة بديلة
    return Container(
      height: widget.height,
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surface.withAlpha(204), // 0.8 opacity
        border: Border.all(
          color: AppColors.primary.withAlpha(51),
        ), // 0.2 opacity
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Text(
          'مساحة إعلانية',
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: widget.isSmallBanner ? 12 : 14,
          ),
        ),
      ),
    );
  }
}
