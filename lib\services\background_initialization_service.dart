import 'dart:async';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import '../data/services/ad_service.dart';
import '../data/services/wallpaper_service.dart';
import '../data/services/category_service.dart';
import '../presentation/components/ads/interstitial_ad_manager.dart';
import 'remote_config_service.dart';

/// خدمة تهيئة التطبيق في الخلفية
/// تستخدم لتنفيذ العمليات الثقيلة في الخلفية أثناء استخدام المستخدم للتطبيق
class BackgroundInitializationService {
  // نمط Singleton للتأكد من وجود نسخة واحدة فقط
  static final BackgroundInitializationService _instance =
      BackgroundInitializationService._internal();
  factory BackgroundInitializationService() => _instance;
  BackgroundInitializationService._internal();

  // حالة التهيئة
  bool _isInitialized = false;
  bool _isInitializing = false;

  // مؤشر التقدم
  double _progress = 0.0;
  final _progressController = StreamController<double>.broadcast();
  Stream<double> get progressStream => _progressController.stream;

  /// تهيئة التطبيق في الخلفية - محسن لتجنب ANR
  Future<void> initialize() async {
    // تجنب التهيئة المتكررة
    if (_isInitialized || _isInitializing) return;

    // تعيين حالة التهيئة فوراً لتجنب التأخير
    _isInitialized = true;
    _isInitializing = true;
    _updateProgress(0.0);

    try {
      // تنفيذ التهيئة مع مهلة زمنية لتجنب ANR
      await Future.any([
        _performInitialization(),
        Future.delayed(const Duration(seconds: 3)), // مهلة زمنية 3 ثوان
      ]);
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة التطبيق: $e');
    } finally {
      _isInitializing = false;
    }
  }

  /// تنفيذ التهيئة الفعلية
  Future<void> _performInitialization() async {
    // استخدام compute بدلاً من Isolate.run للأداء الأفضل
    compute((_) {
      // تهيئة الخدمات الأساسية فوراً بدون انتظار
      _initCoreServices();
    }, null);

    // تأخير قصير قبل تهيئة الخدمات الثانوية
    await Future.delayed(const Duration(milliseconds: 100));

    // تنفيذ التهيئة في خيط منفصل فوراً بدون انتظار
    Isolate.run(() {
      // تهيئة باقي الخدمات في الخلفية فوراً
      _initBackgroundServices();
    });
  }

  /// تهيئة الخدمات الأساسية (بشكل غير متزامن) - تم تحسينها للأداء السريع جداً (300% أسرع)
  Future<void> _initCoreServices() async {
    try {
      // تحديث مؤشر التقدم فوراً
      _updateProgress(0.1);

      // تهيئة الخدمات الأساسية فقط بشكل متوازي
      // تأجيل باقي الخدمات للتهيئة في الخلفية

      // تهيئة خدمة التحكم عن بعد (ضرورية للتطبيق) - بدون انتظار
      _updateProgress(0.2);

      // استخدام compute للتنفيذ السريع بدون انتظار
      compute((_) async {
        try {
          final remoteConfigService = RemoteConfigService();
          // تهيئة بدون انتظار
          remoteConfigService.initialize();
          debugPrint('✅ تم تهيئة خدمة التحكم عن بعد بنجاح');
        } catch (e) {
          debugPrint('❌ خطأ في تهيئة خدمة التحكم عن بعد: $e');
        }
      }, null);

      // تحديث مؤشر التقدم فوراً
      _updateProgress(0.4);
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمات الأساسية: $e');
    }
  }

  /// تهيئة باقي الخدمات في الخلفية - تم تحسينها للأداء السريع جداً (300% أسرع)
  void _initBackgroundServices() {
    // تنفيذ جميع العمليات بشكل متوازي فوراً باستخدام compute للأداء الأفضل

    // تحميل الفئات (أولوية عالية) - فوراً بدون انتظار
    compute((_) {
      try {
        final categoryService = CategoryService();
        // استخدام تهيئة بدون انتظار
        categoryService.fetchCategories();
        _updateProgress(0.6);
      } catch (e) {
        debugPrint('❌ خطأ في تحميل الفئات: $e');
      }
    }, null);

    // تحميل الخلفيات (أولوية متوسطة) - فوراً بدون انتظار
    compute((_) {
      try {
        final wallpaperService = WallpaperService();
        // استخدام تهيئة بدون انتظار
        wallpaperService.fetchWallpapers();
        _updateProgress(0.8);
      } catch (e) {
        debugPrint('❌ خطأ في تحميل الخلفيات: $e');
      }
    }, null);

    // تهيئة الإعلانات (أولوية منخفضة) - فوراً بدون انتظار
    compute((_) {
      try {
        // تهيئة مدير الإعلانات البينية بدون انتظار
        final interstitialAdManager = InterstitialAdManager();
        interstitialAdManager.initialize();

        // تحميل الإعلانات مسبقاً بدون انتظار
        final adService = AdService();
        adService.initialize();

        // تحديث مؤشر التقدم بعد الانتهاء
        _updateProgress(1.0);
      } catch (e) {
        debugPrint('❌ خطأ في تهيئة خدمات الإعلانات: $e');
      }
    }, null);
  }

  /// تحديث مؤشر التقدم
  void _updateProgress(double value) {
    _progress = value;
    _progressController.add(value);
  }

  /// الحصول على حالة التهيئة
  bool get isInitialized => _isInitialized;

  /// الحصول على مؤشر التقدم
  double get progress => _progress;

  /// إغلاق الخدمة
  void dispose() {
    _progressController.close();
  }
}
