import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../services/geo_service.dart';

/// شريط موافقة الاتحاد الأوروبي
class EuConsentBanner extends StatelessWidget {
  final VoidCallback? onAccept;
  final VoidCallback? onLearnMore;

  const EuConsentBanner({super.key, this.onAccept, this.onLearnMore});

  /// التحقق مما إذا كان يجب عرض الشريط
  static Future<bool> shouldShow() async {
    final prefs = await SharedPreferences.getInstance();

    // التحقق مما إذا كان المستخدم قد وافق بالفعل
    final hasAccepted = prefs.getBool('eu_consent_accepted') ?? false;
    if (hasAccepted) {
      // إعادة تعيين حالة الموافقة للاختبار - قم بإزالة هذا السطر في الإصدار النهائي
      await prefs.setBool('eu_consent_accepted', false);
      return true;
    }

    // عرض الشريط لجميع المستخدمين للاختبار
    return true;

    // في الإصدار النهائي، استخدم الكود التالي بدلاً من السطر أعلاه:
    // final isInEU = await GeoService.isInEuropeanUnion();
    // return isInEU;
  }

  /// حفظ حالة الموافقة
  static Future<void> saveConsent() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('eu_consent_accepted', true);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: const Color(
              0xFF0A3170,
            ), // لون أزرق داكن يشبه علم الاتحاد الأوروبي
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(70),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // محتوى الرسالة
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 16, 20, 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان الرسالة
                      Center(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.white.withAlpha(40),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(40),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.privacy_tip_rounded,
                                color: Colors.white,
                                size: 18,
                              ),
                            ),
                            const SizedBox(width: 10),
                            Text(
                              'إشعار الخصوصية',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 18,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withAlpha(80),
                                    blurRadius: 2,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),

                      // نص الرسالة
                      Container(
                        padding: const EdgeInsets.symmetric(
                          vertical: 10,
                          horizontal: 12,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(15),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.white.withAlpha(30),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'وفقًا للائحة العامة لحماية البيانات (GDPR) في الاتحاد الأوروبي، نطلب موافقتك على استخدام ملفات تعريف الارتباط وجمع بيانات الاستخدام المحدودة لتحسين تجربتك وتقديم محتوى مخصص. يمكنك معرفة المزيد عن كيفية استخدامنا للبيانات في سياسة الخصوصية.',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: Colors.white.withAlpha(230),
                            fontSize: 13,
                            height: 1.5,
                            letterSpacing: 0.2,
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),

                      // أزرار الإجراءات
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // زر معرفة المزيد
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.white.withAlpha(128),
                                width: 1,
                              ),
                              gradient: LinearGradient(
                                colors: [
                                  Colors.white.withAlpha(20),
                                  Colors.white.withAlpha(10),
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                            ),
                            child: TextButton.icon(
                              onPressed: onLearnMore,
                              icon: Icon(
                                Icons.info_outline,
                                color: Colors.white,
                                size: 16,
                              ),
                              label: const Text('معرفة المزيد'),
                              style: TextButton.styleFrom(
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 10,
                                ),
                                textStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),

                          // زر الموافقة
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient: LinearGradient(
                                colors: [
                                  Colors.white,
                                  Colors.white.withAlpha(240),
                                ],
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(60),
                                  blurRadius: 6,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: TextButton.icon(
                              onPressed: onAccept,
                              icon: Icon(
                                Icons.check_circle_outline,
                                color: const Color(0xFF0A3170),
                                size: 16,
                              ),
                              label: const Text('موافق'),
                              style: TextButton.styleFrom(
                                foregroundColor: const Color(0xFF0A3170),
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 10,
                                ),
                                textStyle: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )
        .animate()
        .fadeIn(duration: const Duration(milliseconds: 500))
        .slideY(
          begin: -0.2,
          end: 0,
          duration: const Duration(milliseconds: 600),
          curve: Curves.easeOutQuad,
        );
  }
}
