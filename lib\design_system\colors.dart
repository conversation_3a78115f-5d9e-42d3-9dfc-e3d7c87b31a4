import 'package:flutter/material.dart';

/// نظام الألوان الموحد للتطبيق - تصميم إسلامي معاصر
class AppColors {
  // ألوان الهوية الأساسية
  static const Color primary = Color(0xFF1E88E5); // أزرق إسلامي
  static const Color primaryLight = Color(0xFF6AB7FF); // أزرق فاتح
  static const Color primaryDark = Color(0xFF005CB2); // أزرق داكن

  static const Color secondary = Color(0xFF26A69A); // أخضر فيروزي
  static const Color secondaryLight = Color(0xFF64D8CB); // أخضر فاتح
  static const Color secondaryDark = Color(0xFF00766C); // أخضر داكن

  static const Color accent = Color(0xFFD4AF37); // ذهبي إسلامي
  static const Color accentLight = Color(0xFFFFE082); // ذهبي فاتح
  static const Color accentDark = Color(0xFFC9A227); // ذهبي داكن

  // ألوان الخلفية - تدرج أسود داكن
  static const Color background = Color(
    0xFF050505,
  ); // أسود داكن جدًا (خلفية التطبيق)
  static const Color surface = Color(0xFF101010); // أسود داكن (خلفية العناصر)
  static const Color surfaceLight = Color(
    0xFF151515,
  ); // أسود داكن فاتح (بطاقات)
  static const Color card = Color(0xFF1A1A1A); // أسود متوسط (بطاقات مميزة)
  static const Color divider = Color(0xFF252525); // لون الفواصل

  // ألوان النص
  static const Color textPrimary = Color(0xFFFFFFFF); // أبيض
  static const Color textSecondary = Color(0xFFE0E0E0); // رمادي فاتح
  static const Color textMuted = Color(0xFFABBBCF); // رمادي أزرق
  static const Color textDisabled = Color(0xFF78909C); // رمادي معطل

  // ألوان الحالة
  static const Color success = Color(0xFF66BB6A); // أخضر
  static const Color error = Color(0xFFEF5350); // أحمر
  static const Color warning = Color(0xFFFFCA28); // أصفر
  static const Color info = Color(0xFF42A5F5); // أزرق معلومات

  // تدرجات لونية
  static const List<Color> primaryGradient = [
    Color(0xFF005CB2),
    Color(0xFF1E88E5),
  ];

  static const List<Color> secondaryGradient = [
    Color(0xFF00766C),
    Color(0xFF26A69A),
  ];

  static const List<Color> goldGradient = [
    Color(0xFFC9A227),
    Color(0xFFD4AF37),
    Color(0xFFFFD700),
  ];

  static const List<Color> backgroundGradient = [
    Color(0xFF050505),
    Color(0xFF101010),
  ];

  // ألوان الفئات - متناسقة مع الألوان الأساسية
  static const Color categoryMosque = Color(0xFF1E88E5); // مساجد - أزرق
  static const Color categoryQuran = Color(0xFFD4AF37); // قرآن - ذهبي
  static const Color categoryAllah = Color(
    0xFF26A69A,
  ); // أسماء الله الحسنى - أخضر
  static const Color categoryCalligraphy = Color(
    0xFF7E57C2,
  ); // خط عربي - بنفسجي
  static const Color categoryPattern = Color(
    0xFFEF6C00,
  ); // نقوش إسلامية - برتقالي
  static const Color categoryKaaba = Color(0xFF5C6BC0); // الكعبة - أزرق بنفسجي
  static const Color categoryRamadan = Color(0xFFEC407A); // رمضان - وردي

  // ألوان الاشتراك المميز
  static const Color premiumColor = Color(0xFFD4AF37); // ذهبي
  static const Color premiumBackground = Color(0xFF1A1500); // ذهبي داكن جدًا
  static const Color premiumSurface = Color(0xFF33290A); // ذهبي داكن
  static const Color premiumHighlight = Color(0xFFFFD700); // ذهبي مشرق

  // ألوان شريط التنقل
  static const Color navBackground = Color(
    0xFF050505,
  ); // متطابق مع خلفية التطبيق
  static const Color navSurface = Color(0xFF101010); // سطح شريط التنقل
  static const Color navActiveIcon = Color(0xFFD4AF37); // أيقونة نشطة - ذهبي
  static const Color navInactiveIcon = Color(
    0xFF666666,
  ); // أيقونة غير نشطة - رمادي داكن

  // ألوان الظلال
  static const Color shadowLight = Color(0x40000000); // ظل خفيف
  static const Color shadowMedium = Color(0x80000000); // ظل متوسط
  static const Color shadowDark = Color(0xC0000000); // ظل داكن

  // ألوان شفافة للتراكب
  static const Color overlay10 = Color(0x1AFFFFFF); // تراكب أبيض 10%
  static const Color overlay20 = Color(0x33FFFFFF); // تراكب أبيض 20%
  static const Color overlay30 = Color(0x4DFFFFFF); // تراكب أبيض 30%
}
