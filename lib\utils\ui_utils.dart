import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// أدوات مساعدة لواجهة المستخدم
class UiUtils {
  /// إخفاء شريط التنقل الخاص بالهاتف مع السماح بإظهاره عند السحب
  static void hideSystemNavigationBar() {
    // استخدام وضع حافة إلى حافة الذي يسمح بإظهار شريط التنقل عند السحب
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge, // وضع حافة إلى حافة للسماح بالسحب
      overlays: [SystemUiOverlay.top], // إظهار شريط الحالة فقط
    );

    // تعيين نمط شريط الحالة وشريط التنقل بشكل ثابت
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // شفاف لشريط الحالة
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black, // لون أسود لشريط التنقل
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarContrastEnforced:
            false, // تعطيل تباين الألوان الإجباري
      ),
    );

    // تأخير قصير ثم إعادة تطبيق الإعدادات للتأكد من تطبيقها بشكل صحيح
    Future.delayed(const Duration(milliseconds: 100), () {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.black,
          systemNavigationBarIconBrightness: Brightness.light,
          systemNavigationBarDividerColor: Colors.transparent,
          systemNavigationBarContrastEnforced: false,
        ),
      );
    });
  }

  /// إظهار شريط التنقل الخاص بالهاتف
  static void showSystemNavigationBar() {
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge, // استخدام وضع حافة إلى حافة للحصول على مظهر أفضل
      overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom],
    );

    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // شفاف لشريط الحالة
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black, // لون أسود لشريط التنقل
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarContrastEnforced:
            false, // تعطيل تباين الألوان الإجباري
      ),
    );

    // تأخير قصير ثم إعادة تطبيق الإعدادات للتأكد من تطبيقها بشكل صحيح
    Future.delayed(const Duration(milliseconds: 100), () {
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          systemNavigationBarColor: Colors.black,
          systemNavigationBarIconBrightness: Brightness.light,
          systemNavigationBarDividerColor: Colors.transparent,
          systemNavigationBarContrastEnforced: false,
        ),
      );
    });
  }

  /// إخفاء شريط التنقل بشكل كامل (بدون إمكانية السحب)
  static void hideSystemNavigationBarCompletely() {
    // إخفاء شريط التنقل النظامي بشكل كامل
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [], // إخفاء جميع عناصر واجهة النظام
    );

    // تعيين نمط شريط الحالة بشكل ثابت
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent, // شفاف لشريط الحالة
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black, // لون أسود لشريط التنقل
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarDividerColor: Colors.transparent,
      ),
    );
  }

  /// تعيين وضع الشاشة الكاملة مع إمكانية السحب لإظهار شريط التنقل
  static void setImmersiveMode() {
    // استخدام وضع الشاشة الكاملة مع إمكانية السحب
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [], // إخفاء جميع عناصر واجهة النظام
    );

    // تعيين نمط شريط الحالة وشريط التنقل
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarContrastEnforced: false,
      ),
    );
  }
}
