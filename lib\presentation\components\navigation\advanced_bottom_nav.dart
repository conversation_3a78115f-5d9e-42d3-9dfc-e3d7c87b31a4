import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';

/// شريط تنقل احترافي متكيف مع شريط التنقل النظامي
class AdvancedBottomNav extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<NavItem> items;
  final Color? activeColor;
  final Color? backgroundColor;
  final Gradient? backgroundGradient;

  const AdvancedBottomNav({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.activeColor,
    this.backgroundColor,
    this.backgroundGradient,
  }) : super(key: key);

  @override
  State<AdvancedBottomNav> createState() => _AdvancedBottomNavState();
}

class _AdvancedBottomNavState extends State<AdvancedBottomNav>
    with WidgetsBindingObserver {
  // ارتفاع شريط التنقل الخاص بنا
  static const double _navBarHeight = 50.0;
  // حجم الأيقونة النشطة
  static const double _activeIconSize = 26.0;
  // حجم الأيقونة العادية
  static const double _iconSize = 22.0;
  // حجم الخط
  static const double _fontSize = 11.0;

  @override
  void initState() {
    super.initState();
    // إضافة مراقب لتغييرات واجهة المستخدم
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    // إزالة المراقب عند التخلص من الويدجت
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final activeColor = widget.activeColor ?? AppColors.accent;

    // إنشاء حاوية خارجية تتكيف مع شريط التنقل النظامي
    return Container(
      // استخدام حاوية خارجية بدون هوامش
      margin: EdgeInsets.zero,
      decoration: BoxDecoration(
        color: Colors.black, // لون أسود ثابت
      ),
      child: SafeArea(
        top: false, // عدم تطبيق المساحة الآمنة من الأعلى
        child: Container(
          height: _navBarHeight, // ارتفاع ثابت
          color: Colors.black, // لون أسود ثابت
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(widget.items.length, (index) {
              final item = widget.items[index];
              final isActive = index == widget.currentIndex;

              return Expanded(
                child: InkWell(
                  onTap: () => widget.onTap(index),
                  child: Container(
                    color: Colors.black, // لون أسود ثابت
                    height: _navBarHeight, // ارتفاع ثابت
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // أيقونة العنصر
                        Icon(
                          isActive ? (item.activeIcon ?? item.icon) : item.icon,
                          color: isActive ? activeColor : AppColors.textMuted,
                          size: isActive ? _activeIconSize : _iconSize,
                        ),
                        const SizedBox(height: 2), // مسافة أقل
                        // نص العنصر
                        Text(
                          item.title,
                          style: TextStyle(
                            fontSize: _fontSize,
                            color: isActive ? activeColor : AppColors.textMuted,
                            fontWeight:
                                isActive ? FontWeight.w600 : FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }
}

/// عنصر شريط التنقل
class NavItem {
  final IconData icon;
  final IconData? activeIcon;
  final String title;

  NavItem({required this.icon, this.activeIcon, required this.title});
}
