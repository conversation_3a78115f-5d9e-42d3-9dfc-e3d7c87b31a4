import 'package:equatable/equatable.dart';

/// نموذج إعدادات التطبيق
class AppSettings extends Equatable {
  final bool darkMode;
  final String language;
  final bool notifications;
  final String downloadQuality;
  final int dailyDownloadLimit;
  final bool isPro;
  final String? subscriptionId;
  final DateTime? subscriptionStartDate;
  final DateTime? subscriptionEndDate;

  const AppSettings({
    this.darkMode = true,
    this.language = 'ar',
    this.notifications = true,
    this.downloadQuality = 'high',
    this.dailyDownloadLimit = 5,
    this.isPro = false,
    this.subscriptionId,
    this.subscriptionStartDate,
    this.subscriptionEndDate,
  });

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      darkMode: json['darkMode'] ?? true,
      language: json['language'] ?? 'ar',
      notifications: json['notifications'] ?? true,
      downloadQuality: json['downloadQuality'] ?? 'high',
      dailyDownloadLimit: json['dailyDownloadLimit'] ?? 5,
      isPro: json['isPro'] ?? false,
      subscriptionId: json['subscriptionId'],
      subscriptionStartDate: json['subscriptionStartDate'] != null
          ? DateTime.parse(json['subscriptionStartDate'])
          : null,
      subscriptionEndDate: json['subscriptionEndDate'] != null
          ? DateTime.parse(json['subscriptionEndDate'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'darkMode': darkMode,
      'language': language,
      'notifications': notifications,
      'downloadQuality': downloadQuality,
      'dailyDownloadLimit': dailyDownloadLimit,
      'isPro': isPro,
      'subscriptionId': subscriptionId,
      'subscriptionStartDate': subscriptionStartDate?.toIso8601String(),
      'subscriptionEndDate': subscriptionEndDate?.toIso8601String(),
    };
  }

  AppSettings copyWith({
    bool? darkMode,
    String? language,
    bool? notifications,
    String? downloadQuality,
    int? dailyDownloadLimit,
    bool? isPro,
    String? subscriptionId,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionEndDate,
  }) {
    return AppSettings(
      darkMode: darkMode ?? this.darkMode,
      language: language ?? this.language,
      notifications: notifications ?? this.notifications,
      downloadQuality: downloadQuality ?? this.downloadQuality,
      dailyDownloadLimit: dailyDownloadLimit ?? this.dailyDownloadLimit,
      isPro: isPro ?? this.isPro,
      subscriptionId: subscriptionId ?? this.subscriptionId,
      subscriptionStartDate: subscriptionStartDate ?? this.subscriptionStartDate,
      subscriptionEndDate: subscriptionEndDate ?? this.subscriptionEndDate,
    );
  }

  @override
  List<Object?> get props => [
        darkMode,
        language,
        notifications,
        downloadQuality,
        dailyDownloadLimit,
        isPro,
        subscriptionId,
        subscriptionStartDate,
        subscriptionEndDate,
      ];
}
