import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../models/wallpaper.dart';

class WallpaperService {
  // رابط API الخاص بـ Google Apps Script - محرر السكريبت (للمرجع فقط)
  // static const String _scriptEditorUrl =
  //     'https://script.google.com/home/<USER>/10M9bkMeFLE3_ym7GSY-Gtqk9b1clpKAVnmoNxOgfYCadpezinZLDB-DA/edit';

  // رابط API الخاص بـ Google Apps Script - نقطة نهاية البيانات
  static const String _apiUrl =
      'https://script.googleusercontent.com/macros/echo?user_content_key=AehSKLhdMc6pN6rpVOWo25390F_8S_eWe5hxytXB0QprFJCYCPIjDFI4b5B8b__jwZHR09bErRj-doc8Nb34-maRX-SJmnNmPSg63YSx5mWX7haWPKJm8EcA8rk9be9L4_Vc3YFBauABKz8XbOH-sw-moAGSVBRoLODtbG7OHnQeNWs0leNie-P7UjCLkizB4nrYsGRkDUTwczOfRvc8hzQRz6exyAWm3kLPNY0Vpk42R6mQCOj7HBdPp4tsccVN25q_ZPk3mJoLjMi1hjn9TOMu4jxCRRSPqOu6BIGZT91c&lib=MzdFOt3cKdQGAF0pIbkiTYz1WwPc2B2M8';

  // رابط الخلفيات الاحتياطي (نفس الرابط الجديد)
  static const String _backupUrl = _apiUrl;

  // جلب جميع الخلفيات
  Future<List<Wallpaper>> fetchWallpapers() async {
    try {
      try {
        final response = await http.get(Uri.parse(_apiUrl));

        if (response.statusCode == 200) {
          final List<dynamic> jsonData = json.decode(response.body);
          // عكس ترتيب البيانات لعرض الخلفيات الأحدث أولاً (الأسطر الأخيرة في الملف هي الأحدث)
          final reversedData = jsonData.reversed.toList();
          final wallpapers =
              reversedData.map((data) => Wallpaper.fromJson(data)).toList();

          // تعيين بعض الخلفيات كمدفوعة (50% من الخلفيات)
          _markPremiumWallpapers(wallpapers);

          return wallpapers;
        } else {
          // إذا فشل الاتصال بـ API، نستخدم الرابط الاحتياطي
          return _fetchFromBackup();
        }
      } catch (e) {
        debugPrint('خطأ في جلب الخلفيات من API: $e');
        // إذا فشل الاتصال بـ API، نستخدم الرابط الاحتياطي
        return _fetchFromBackup();
      }
    } catch (e) {
      debugPrint('خطأ في جلب الخلفيات: $e');
      // في حالة حدوث خطأ، نستخدم الرابط الاحتياطي
      return _fetchFromBackup();
    }
  }

  // تعيين الخلفيات المدفوعة
  void _markPremiumWallpapers(List<Wallpaper> wallpapers) {
    // تعيين 50% من الخلفيات كمدفوعة
    for (int i = 0; i < wallpapers.length; i++) {
      // جعل كل خلفية ثانية مدفوعة
      if (i % 2 == 0) {
        final wallpaper = wallpapers[i];
        // استخدام copyWith لإنشاء نسخة جديدة مع تعيين isPremium إلى true
        wallpapers[i] = wallpaper.copyWith(isPremium: true);
      }
    }
  }

  // تم إزالة الدوال غير المستخدمة

  // جلب الخلفيات من الرابط الاحتياطي
  Future<List<Wallpaper>> _fetchFromBackup() async {
    try {
      final response = await http.get(Uri.parse(_backupUrl));

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);

        // تحويل البيانات إلى قائمة من الخلفيات
        List<Wallpaper> wallpapers = [];

        for (var item in data) {
          if (item is Map<String, dynamic>) {
            // التحقق من وجود رابط الصورة (دعم كلا التنسيقين)
            final String imageUrl =
                item['Image_link'] ?? item['Image link'] ?? '';

            if (imageUrl.isNotEmpty) {
              // إنشاء كائن الخلفية باستخدام البيانات من API الجديد
              wallpapers.add(Wallpaper.fromJson(item));
            }
          }
        }

        // عكس ترتيب الخلفيات لعرض الأحدث أولاً
        final reversedWallpapers = wallpapers.reversed.toList();

        // تعيين بعض الخلفيات كمدفوعة (50% من الخلفيات)
        _markPremiumWallpapers(reversedWallpapers);

        return reversedWallpapers;
      } else {
        // إذا فشل الاتصال بالرابط الاحتياطي، نعيد قائمة افتراضية
        final defaultWallpapers = _getDefaultWallpapers();
        _markPremiumWallpapers(defaultWallpapers);
        return defaultWallpapers;
      }
    } catch (e) {
      debugPrint('خطأ في جلب الخلفيات من الرابط الاحتياطي: $e');
      // في حالة حدوث خطأ، نعيد قائمة افتراضية
      final defaultWallpapers = _getDefaultWallpapers();
      _markPremiumWallpapers(defaultWallpapers);
      return defaultWallpapers;
    }
  }

  // الحصول على خلفيات افتراضية
  List<Wallpaper> _getDefaultWallpapers() {
    return [
      Wallpaper(
        id: '1',
        imageUrl:
            'https://images.unsplash.com/photo-1584810359583-96fc3448beaa',
        thumbnailUrl:
            'https://images.unsplash.com/photo-1584810359583-96fc3448beaa?w=200',
        category: 'مساجد',
        isFeatured: true,
      ),
      Wallpaper(
        id: '2',
        imageUrl:
            'https://images.unsplash.com/photo-1609599006353-e629aaabfeae',
        thumbnailUrl:
            'https://images.unsplash.com/photo-1609599006353-e629aaabfeae?w=200',
        category: 'قرآن',
      ),
      Wallpaper(
        id: '3',
        imageUrl:
            'https://images.unsplash.com/photo-1519817650390-64a93db51149',
        thumbnailUrl:
            'https://images.unsplash.com/photo-1519817650390-64a93db51149?w=200',
        category: 'مكة',
        isFeatured: true,
      ),
    ];
  }

  // جلب الخلفيات حسب الفئة
  Future<List<Wallpaper>> fetchWallpapersByCategory(String categoryId) async {
    try {
      final List<Wallpaper> allWallpapers = await fetchWallpapers();

      // تصفية الخلفيات حسب الفئة
      // تحديث: استخدام مقارنة مرنة للفئات للتوافق مع التنسيق الجديد
      return allWallpapers.where((wallpaper) {
        // تحويل النصوص إلى حروف صغيرة للمقارنة
        final categoryLower = wallpaper.category.toLowerCase();
        final categoryIdLower = categoryId.toLowerCase();

        // التحقق من تطابق الفئة أو وجودها في العلامات
        return categoryLower == categoryIdLower ||
            categoryLower.contains(categoryIdLower) ||
            categoryIdLower.contains(categoryLower) ||
            wallpaper.tags.any(
              (tag) =>
                  tag.toLowerCase() == categoryIdLower ||
                  tag.toLowerCase().contains(categoryIdLower) ||
                  categoryIdLower.contains(tag.toLowerCase()),
            );
      }).toList();
    } catch (e) {
      debugPrint('خطأ في جلب الخلفيات حسب الفئة: $e');
      return [];
    }
  }

  // جلب الخلفيات المميزة
  Future<List<Wallpaper>> fetchFeaturedWallpapers() async {
    try {
      final List<Wallpaper> allWallpapers = await fetchWallpapers();

      // تصفية الخلفيات المميزة
      return allWallpapers.where((wallpaper) => wallpaper.isFeatured).toList();
    } catch (e) {
      debugPrint('خطأ في جلب الخلفيات المميزة: $e');
      return [];
    }
  }

  // البحث عن خلفيات
  Future<List<Wallpaper>> searchWallpapers(String query) async {
    try {
      final List<Wallpaper> allWallpapers = await fetchWallpapers();

      // تصفية النتائج حسب الاستعلام
      // تحديث: جعل البحث أكثر مرونة للتوافق مع التنسيق الجديد
      return allWallpapers.where((wallpaper) {
        final String searchQuery = query.toLowerCase();

        // البحث في الفئة والعنوان والعلامات
        return wallpaper.category.toLowerCase().contains(searchQuery) ||
            searchQuery.contains(wallpaper.category.toLowerCase()) ||
            wallpaper.title.toLowerCase().contains(searchQuery) ||
            searchQuery.contains(wallpaper.title.toLowerCase()) ||
            wallpaper.tags.any(
              (tag) =>
                  tag.toLowerCase().contains(searchQuery) ||
                  searchQuery.contains(tag.toLowerCase()),
            );
      }).toList();
    } catch (e) {
      debugPrint('خطأ في البحث عن الخلفيات: $e');
      return [];
    }
  }

  // زيادة عدد التنزيلات
  Future<bool> incrementDownloads(String id) async {
    try {
      // في الإنتاج، يجب استبدال هذا بطلب API حقيقي
      // هنا نستخدم محاكاة بسيطة
      return true;
    } catch (e) {
      debugPrint('خطأ في زيادة عدد التنزيلات: $e');
      return false;
    }
  }

  // زيادة عدد الإعجابات
  Future<bool> incrementLikes(String id) async {
    try {
      // في الإنتاج، يجب استبدال هذا بطلب API حقيقي
      // هنا نستخدم محاكاة بسيطة
      return true;
    } catch (e) {
      debugPrint('خطأ في زيادة عدد الإعجابات: $e');
      return false;
    }
  }

  // جلب الخلفيات مع دعم الصفحات
  static Future<List<Wallpaper>> getWallpapers({
    int page = 1,
    int limit = 10,
  }) async {
    try {
      // إنشاء كائن من الخدمة
      final service = WallpaperService();

      // جلب جميع الخلفيات
      final allWallpapers = await service.fetchWallpapers();

      // حساب الفهارس
      final startIndex = (page - 1) * limit;
      final endIndex = startIndex + limit;

      // التحقق من صحة الفهارس
      if (startIndex >= allWallpapers.length) {
        return [];
      }

      // إرجاع الخلفيات المطلوبة
      return allWallpapers.sublist(
        startIndex,
        endIndex > allWallpapers.length ? allWallpapers.length : endIndex,
      );
    } catch (e) {
      debugPrint('خطأ في جلب الخلفيات مع دعم الصفحات: $e');
      return [];
    }
  }

  // جلب المزيد من الخلفيات بناءً على الإزاحة
  Future<List<Wallpaper>> fetchMoreWallpapers({
    required int offset,
    int limit = 10,
  }) async {
    try {
      // جلب جميع الخلفيات
      final allWallpapers = await fetchWallpapers();

      // التحقق من صحة الإزاحة
      if (offset >= allWallpapers.length) {
        return [];
      }

      // حساب الفهرس النهائي
      final endIndex = offset + limit;

      // إرجاع الخلفيات المطلوبة
      return allWallpapers.sublist(
        offset,
        endIndex > allWallpapers.length ? allWallpapers.length : endIndex,
      );
    } catch (e) {
      debugPrint('خطأ في جلب المزيد من الخلفيات: $e');
      return [];
    }
  }
}
