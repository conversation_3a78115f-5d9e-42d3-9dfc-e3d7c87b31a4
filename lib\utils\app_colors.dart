import 'package:flutter/material.dart';

/// ألوان التطبيق - مستوردة من design_system/colors.dart
/// هذا الملف موجود للحفاظ على التوافق مع الكود القديم
/// يجب استخدام design_system/colors.dart في الكود الجديد

class AppColors {
  // ألوان الهوية الأساسية
  static const Color primary = Color(0xFF1E88E5); // أزرق إسلامي
  static const Color primaryLight = Color(0xFF6AB7FF); // أزرق فاتح
  static const Color primaryDark = Color(0xFF005CB2); // أزرق داكن

  static const Color secondary = Color(0xFF26A69A); // أخضر فيروزي
  static const Color secondaryLight = Color(0xFF64D8CB); // أخضر فاتح
  static const Color secondaryDark = Color(0xFF00766C); // أخضر داكن

  static const Color accent = Color(0xFFD4AF37); // ذهبي إسلامي
  static const Color accentLight = Color(0xFFFFE082); // ذهبي فاتح
  static const Color accentDark = Color(0xFFC9A227); // ذهبي داكن

  // ألوان الخلفية - تدرج أسود داكن
  static const Color background = Color(0xFF050505); // أسود داكن جدًا (خلفية التطبيق)
  static const Color surface = Color(0xFF101010); // أسود داكن (خلفية العناصر)
  static const Color surfaceLight = Color(0xFF151515); // أسود داكن فاتح (بطاقات)
  static const Color card = Color(0xFF1A1A1A); // أسود متوسط (بطاقات مميزة)
  static const Color divider = Color(0xFF252525); // لون الفواصل

  // ألوان النص
  static const Color textPrimary = Color(0xFFFFFFFF); // أبيض
  static const Color textSecondary = Color(0xFFE0E0E0); // رمادي فاتح
  static const Color textMuted = Color(0xFFABBBCF); // رمادي أزرق
  static const Color textDisabled = Color(0xFF78909C); // رمادي معطل

  // ألوان الحالة
  static const Color success = Color(0xFF66BB6A); // أخضر
  static const Color error = Color(0xFFEF5350); // أحمر
  static const Color warning = Color(0xFFFFCA28); // أصفر
  static const Color info = Color(0xFF42A5F5); // أزرق معلومات
}
