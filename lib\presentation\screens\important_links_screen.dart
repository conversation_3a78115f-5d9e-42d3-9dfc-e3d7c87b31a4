import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../design_system/colors.dart';
import '../components/effects/glass_container.dart';
import '../components/navigation/modern_app_header.dart';

class ImportantLinksScreen extends StatelessWidget {
  const ImportantLinksScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // رأس الصفحة الثابت
            SliverPersistentHeader(
              delegate: ModernAppHeader(
                title: 'روابط مهمة',
                subtitle: 'تواصل معنا وتابعنا',
                showBackButton: true,
              ),
              pinned: true,
            ),

            // محتوى الروابط المهمة
            SliverPadding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 120),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  _buildLinkCard(
                    title: 'تقييم التطبيق',
                    description:
                        'ساعدنا على التحسين بتقييم التطبيق على متجر جوجل بلاي',
                    icon: Icons.star,
                    iconColor: Colors.amber,
                    onTap:
                        () => _launchUrl(
                          'https://play.google.com/store/apps/details?id=com.islamiclandporta.islam.allahwallpaper.ahmad.np',
                        ),
                  ),

                  _buildLinkCard(
                    title: 'المزيد من التطبيقات',
                    description:
                        'اكتشف المزيد من تطبيقاتنا المميزة على متجر جوجل بلاي',
                    icon: Icons.apps,
                    iconColor: Colors.greenAccent,
                    onTap:
                        () => _launchUrl(
                          'https://play.google.com/store/apps/dev?id=5869763471235650152',
                        ),
                  ),

                  _buildLinkCard(
                    title: 'صفحتنا على فيسبوك',
                    description:
                        'تابعنا على فيسبوك للحصول على آخر الأخبار والتحديثات',
                    icon: Icons.facebook,
                    iconColor: Colors.blue,
                    onTap:
                        () => _launchUrl(
                          'https://www.facebook.com/share/16XbtVRXxG/',
                        ),
                  ),

                  _buildLinkCard(
                    title: 'قناتنا على تليجرام',
                    description:
                        'انضم إلى قناتنا على تليجرام للحصول على آخر التحديثات والخلفيات الحصرية',
                    icon: Icons.send,
                    iconColor: Colors.lightBlueAccent,
                    onTap: () => _launchUrl('https://t.me/AA_00_77'),
                  ),

                  _buildLinkCard(
                    title: 'التواصل مع الدعم',
                    description:
                        'هل تواجه مشكلة؟ تواصل معنا عبر البريد الإلكتروني',
                    icon: Icons.email,
                    iconColor: Colors.redAccent,
                    onTap: () => _launchEmail('<EMAIL>'),
                  ),

                  const SizedBox(height: 40),
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLinkCard({
    required String title,
    required String description,
    required IconData icon,
    required Color iconColor,
    required VoidCallback onTap,
  }) {
    return Container(
          margin: const EdgeInsets.only(bottom: 16),
          child: GlassContainer(
            animate: true,
            opacity: 0.15,
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: iconColor.withAlpha(50),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(icon, color: iconColor, size: 28),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            description,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Icon(
                      Icons.arrow_forward_ios,
                      color: Colors.white54,
                      size: 16,
                    ),
                  ],
                ),
              ),
            ),
          ),
        )
        .animate()
        .fadeIn(duration: 400.ms)
        .slideY(
          begin: 0.1,
          end: 0,
          duration: 400.ms,
          curve: Curves.easeOutQuad,
        );
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse(url);
    try {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } catch (e) {
      debugPrint('لا يمكن فتح الرابط: $e');
    }
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=استفسار من تطبيق خلفيات إسلامية',
    );

    try {
      await launchUrl(emailUri);
    } catch (e) {
      debugPrint('لا يمكن فتح تطبيق البريد الإلكتروني: $e');
    }
  }
}
