import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import '../../design_system/colors.dart';
import '../../data/models/wallpaper.dart';
import '../../data/services/favorites_service.dart';
import '../../data/services/subscription_service.dart';
import '../../data/services/download_service.dart';
import '../../data/services/ad_service.dart';
import '../../services/wallpaper_service.dart';
import '../../services/remote_config_service.dart';
import '../../services/ads_manager.dart';
import '../../utils/ui_utils.dart';
import '../components/dialogs/wallpaper_options_dialog.dart';
import '../components/ads/rewarded_ad_button.dart';
import '../components/premium/premium_badge.dart';
import '../components/loaders/download_progress_screen.dart';

// تعداد أنواع الأزرار الاحترافية
enum ButtonType {
  favorite,
  download,
  wallpaper,
}

class WallpaperDetailsScreen extends StatefulWidget {
  final Wallpaper wallpaper;

  const WallpaperDetailsScreen({super.key, required this.wallpaper});

  @override
  State<WallpaperDetailsScreen> createState() => _WallpaperDetailsScreenState();
}

class _WallpaperDetailsScreenState extends State<WallpaperDetailsScreen> {
  final FavoritesService _favoritesService = FavoritesService();
  final SubscriptionService _subscriptionService = SubscriptionService();
  final WallpaperService _wallpaperService = WallpaperService();
  final DownloadService _downloadService = DownloadService();
  final AdService _adService = AdService();
  bool _isFavorite = false;
  bool _isLoading = false;
  bool _isPremiumUser = false;
  bool _isSettingWallpaper = false; // متغير لتتبع حالة تعيين الخلفية
  double _downloadProgress = 0.0; // متغير لتتبع تقدم التنزيل

  @override
  void initState() {
    super.initState();
    _checkFavoriteStatus();
    _checkSubscriptionStatus();
    _preloadAds();

    // إخفاء شريط التنقل الخاص بالهاتف
    UiUtils.hideSystemNavigationBar();
  }

  /// تحميل الإعلانات مسبقًا
  Future<void> _preloadAds() async {
    try {
      await _adService.loadRewardedAd();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإعلانات: $e');
    }
  }

  Future<void> _checkFavoriteStatus() async {
    final isFavorite = await _favoritesService.isFavorite(widget.wallpaper.id);
    setState(() {
      _isFavorite = isFavorite;
    });
  }

  Future<void> _checkSubscriptionStatus() async {
    final isSubscribed = await _subscriptionService.isSubscribed();
    setState(() {
      _isPremiumUser = isSubscribed;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBody: true,
      // تصميم بسيط بدون AppBar
      // إخفاء شريط أزرار الهاتف
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: true,
      // إخفاء شريط التنقل السفلي
      bottomNavigationBar: null,
      body: Stack(
        fit: StackFit.expand,
        children: [
          // صورة الخلفية
          Hero(
            tag: 'wallpaper_${widget.wallpaper.id}',
            child: CachedNetworkImage(
              imageUrl: widget.wallpaper.imageUrl,
              fit: BoxFit.cover,
              placeholder:
                  (context, url) => Container(
                    color: AppColors.background,
                    child: const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColors.primary,
                        ),
                      ),
                    ),
                  ),
              errorWidget:
                  (context, url, error) => Container(
                    color: AppColors.background,
                    child: const Center(
                      child: Icon(
                        Icons.error_outline,
                        color: AppColors.error,
                        size: 48,
                      ),
                    ),
                  ),
            ),
          ),

          // تم نقل زر الرجوع إلى الأسفل مع باقي الأزرار

          // تم إزالة شريط التذييل (الإعلانات)

          // تأثير التدرج في الأسفل
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 200,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withAlpha(204),
                  ], // 0.8 opacity
                ),
              ),
            ),
          ),

          // شاشة تحميل الخلفيات المحسنة
          if (_isLoading && !_isSettingWallpaper)
            DownloadProgressScreen(
              progress: _downloadProgress,
              onCancel: () {
                setState(() {
                  _isLoading = false;
                  _downloadProgress = 0.0;
                });
              },
            ),

          // أزرار احترافية متقدمة مع أشرطة ملونة
          Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // زر المفضلة مع شريط أحمر
                  _buildProfessionalButton(
                    icon: _isFavorite ? Icons.favorite : Icons.favorite_border,
                    label: 'المفضلة',
                    onTap: _toggleFavorite,
                    color: Colors.white,
                    backgroundColor: AppColors.secondary,
                    isActive: _isFavorite,
                    stripColor: const Color(0xFF667EEA), // بنفسجي أنيق
                    buttonType: ButtonType.favorite,
                  ),

                  const SizedBox(width: 16),

                  // زر التحميل مع شريط أزرق وأيقونة اشتراك متطورة
                  _buildProfessionalButton(
                    icon: Icons.download_rounded,
                    label: 'تحميل',
                    onTap: _downloadWallpaper,
                    isLoading: _isLoading && !_isSettingWallpaper,
                    showPremiumBadge: !_isPremiumUser,
                    color: Colors.white,
                    backgroundColor: AppColors.primary,
                    stripColor: const Color(0xFF4FD1C7), // تركوازي أنيق
                    buttonType: ButtonType.download,
                    isPrimary: true,
                  ),

                  const SizedBox(width: 16),

                  // زر تعيين الخلفية مع شريط ذهبي
                  _buildProfessionalButton(
                    icon: Icons.wallpaper_rounded,
                    label: 'تعيين',
                    onTap: _setWallpaper,
                    isLoading: _isLoading && _isSettingWallpaper,
                    color: Colors.white,
                    backgroundColor: AppColors.accent,
                    stripColor: const Color(0xFFF093FB), // وردي أنيق
                    buttonType: ButtonType.wallpaper,
                    isSpecial: true,
                  ),
                ],
              ),
            ),
          ),

          // شارة المحتوى المميز
          if (widget.wallpaper.isFeatured)
            Positioned(
              top: 100,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: AppColors.accent,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(76),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.star, color: Colors.black, size: 16),
                    const SizedBox(width: 4),
                    const Text(
                      'مميز',
                      style: TextStyle(
                        color: Colors.black,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ).animate().fadeIn(
                delay: const Duration(milliseconds: 600),
                duration: const Duration(milliseconds: 500),
              ),
            ),

          // تم إزالة شارة المحتوى المدفوع لأن جميع الخلفيات متاحة للتعيين مجاناً
        ],
      ),
    );
  }

  Future<void> _toggleFavorite() async {
    final newState = !_isFavorite;

    setState(() {
      _isFavorite = newState;
    });

    // حفظ حالة المفضلة
    if (newState) {
      // إضافة إلى المفضلة
      await _favoritesService.addToFavorites(widget.wallpaper);
    } else {
      // إزالة من المفضلة
      await _favoritesService.removeFromFavorites(widget.wallpaper.id);
    }

    if (!mounted) return;
  }

  Future<void> _downloadWallpaper() async {
    // التحقق من إمكانية تنزيل الخلفية (متاح فقط للمشتركين أو بعد مشاهدة إعلان)
    final canDownload = await _subscriptionService.canDownloadWallpapers();
    final isSubscribed = await _subscriptionService.isSubscribed();

    // التحقق من حالة إعلانات المكافأة في نظام التحكم عن بعد
    final remoteConfigService = RemoteConfigService();
    final bool rewardedAdsEnabled =
        remoteConfigService.isInitialized &&
        remoteConfigService.config.showRewardedAds;

    if (!mounted) return;

    // تحديث حالة الاشتراك في الواجهة
    setState(() {
      _isPremiumUser = isSubscribed;
    });

    // إذا كان المستخدم غير مشترك وإعلانات المكافأة معطلة، نوجه المستخدم مباشرة إلى صفحة الاشتراك
    if (!isSubscribed && !rewardedAdsEnabled) {
      // عرض نافذة اشتراك جذابة
      showDialog(
        context: context,
        barrierDismissible: true,
        barrierColor: Colors.black.withAlpha(204), // 0.8 opacity
        builder:
            (context) => Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: EdgeInsets.symmetric(
                horizontal: MediaQuery.of(context).size.width * 0.05,
                vertical: MediaQuery.of(context).size.height * 0.03,
              ),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.9,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [const Color(0xFF101010), const Color(0xFF0A0A0A)],
                  ),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: AppColors.accent.withOpacity(0.5),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.5),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // أيقونة الاشتراك المميزة
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        // الدائرة الخارجية المتوهجة
                        Container(
                              width: 100,
                              height: 100,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: RadialGradient(
                                  colors: [
                                    AppColors.accent.withOpacity(0.7),
                                    AppColors.accent.withOpacity(0.0),
                                  ],
                                  stops: const [0.3, 1.0],
                                ),
                              ),
                            )
                            .animate(
                              onPlay: (controller) => controller.repeat(),
                            )
                            .scale(
                              begin: const Offset(0.8, 0.8),
                              end: const Offset(1.2, 1.2),
                              duration: const Duration(seconds: 2),
                              curve: Curves.easeInOut,
                            ),

                        // الدائرة الداخلية
                        Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: AppColors.goldGradient,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.accent.withOpacity(0.5),
                                    blurRadius: 15,
                                    spreadRadius: 2,
                                  ),
                                ],
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.8),
                                  width: 2,
                                ),
                              ),
                              child: const Center(
                                child: Icon(
                                  Icons.workspace_premium,
                                  color: Colors.white,
                                  size: 45,
                                ),
                              ),
                            )
                            .animate(
                              onPlay:
                                  (controller) =>
                                      controller.repeat(reverse: true),
                            )
                            .scale(
                              begin: const Offset(1.0, 1.0),
                              end: const Offset(1.1, 1.1),
                              duration: const Duration(seconds: 1),
                              curve: Curves.easeInOut,
                            ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // عنوان النافذة
                    const Text(
                      'ميزة حصرية للمشتركين',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ).animate().fadeIn(
                      duration: const Duration(milliseconds: 500),
                      delay: const Duration(milliseconds: 200),
                    ),
                    const SizedBox(height: 12),

                    // وصف النافذة
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: const Text(
                        'تنزيل الخلفيات متاح فقط للمشتركين. اشترك الآن للوصول غير المحدود لجميع الخلفيات!',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 15,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ).animate().fadeIn(
                      duration: const Duration(milliseconds: 500),
                      delay: const Duration(milliseconds: 300),
                    ),
                    const SizedBox(height: 32),

                    // زر الاشتراك
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: AppColors.goldGradient,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.accent.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            Navigator.pop(context);
                            context.push('/subscription');
                          },
                          borderRadius: BorderRadius.circular(12),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: const [
                                Icon(
                                  Icons.workspace_premium,
                                  color: Colors.black,
                                  size: 20,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'اشترك الآن',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ).animate().fadeIn(
                      duration: const Duration(milliseconds: 500),
                      delay: const Duration(milliseconds: 400),
                    ),

                    const SizedBox(height: 16),

                    // زر الإلغاء
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(color: Colors.white60, fontSize: 14),
                      ),
                    ).animate().fadeIn(
                      duration: const Duration(milliseconds: 500),
                      delay: const Duration(milliseconds: 500),
                    ),
                  ],
                ),
              ),
            ),
      );
      return;
    }

    // إذا كان المستخدم غير مشترك وإعلانات المكافأة مفعلة، نعرض خيارات التنزيل
    if (!isSubscribed) {
      // عرض خيار مشاهدة إعلان للتنزيل أو الاشتراك - تصميم محسن
      showDialog(
        context: context,
        barrierDismissible: true,
        barrierColor: Colors.black.withAlpha(204), // 0.8 opacity
        builder:
            (context) => Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: EdgeInsets.symmetric(
                horizontal: MediaQuery.of(context).size.width * 0.05,
                vertical: MediaQuery.of(context).size.height * 0.03,
              ),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.9,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [const Color(0xFF101010), const Color(0xFF0A0A0A)],
                  ),
                  borderRadius: BorderRadius.circular(24),
                  border: Border.all(
                    color: const Color(0xFF1E88E5).withOpacity(0.3),
                    width: 1.5,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.5),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // أيقونة الفيديو المتحركة
                    Stack(
                      alignment: Alignment.center,
                      children: [
                        // الدائرة الخارجية المتوهجة
                        Container(
                              width: 100,
                              height: 100,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: RadialGradient(
                                  colors: [
                                    AppColors.primary.withOpacity(0.7),
                                    AppColors.primary.withOpacity(0.0),
                                  ],
                                  stops: const [0.3, 1.0],
                                ),
                              ),
                            )
                            .animate(
                              onPlay: (controller) => controller.repeat(),
                            )
                            .scale(
                              begin: const Offset(0.8, 0.8),
                              end: const Offset(1.2, 1.2),
                              duration: const Duration(seconds: 2),
                              curve: Curves.easeInOut,
                            ),

                        // الدائرة الداخلية
                        Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: const Color(0xFF0A0A0A),
                                border: Border.all(
                                  color: AppColors.primary.withOpacity(0.5),
                                  width: 2,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.primary.withOpacity(0.3),
                                    blurRadius: 15,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.play_circle_fill,
                                  color: AppColors.primary,
                                  size: 45,
                                ),
                              ),
                            )
                            .animate(
                              onPlay:
                                  (controller) =>
                                      controller.repeat(reverse: true),
                            )
                            .scale(
                              begin: const Offset(1.0, 1.0),
                              end: const Offset(1.1, 1.1),
                              duration: const Duration(seconds: 1),
                              curve: Curves.easeInOut,
                            ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // عنوان النافذة
                    const Text(
                      'تنزيل الخلفية',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ).animate().fadeIn(
                      duration: const Duration(milliseconds: 500),
                      delay: const Duration(milliseconds: 200),
                    ),
                    const SizedBox(height: 12),

                    // وصف النافذة
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: const Text(
                        'يمكنك تنزيل هذه الخلفية مجانًا بعد مشاهدة إعلان قصير أو الاشتراك للوصول غير المحدود',
                        style: TextStyle(
                          color: Colors.white70,
                          fontSize: 15,
                          height: 1.4,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ).animate().fadeIn(
                      duration: const Duration(milliseconds: 500),
                      delay: const Duration(milliseconds: 300),
                    ),
                    const SizedBox(height: 32),

                    // زر مشاهدة الإعلان
                    RewardedAdButton(
                      text: 'مشاهدة إعلان للتنزيل',
                      icon: Icons.movie_outlined,
                      isPremium: false,
                      onRewarded: () {
                        Navigator.pop(context);
                        _downloadWallpaperAfterAd();
                      },
                      onFailed: () {
                        Navigator.pop(context);
                        // عرض نافذة خطأ تحميل الإعلان
                        _showAdLoadErrorDialog();
                      },
                    ).animate().fadeIn(
                      duration: const Duration(milliseconds: 500),
                      delay: const Duration(milliseconds: 400),
                    ),
                    const SizedBox(height: 16),

                    // زر الاشتراك
                    Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.accent.withOpacity(0.8),
                            AppColors.accent,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppColors.accent.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            Navigator.pop(context);
                            context.push('/subscription');
                          },
                          borderRadius: BorderRadius.circular(12),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: const [
                                Icon(
                                  Icons.workspace_premium,
                                  color: Colors.black,
                                  size: 20,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'اشترك للوصول غير المحدود',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 15,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ).animate().fadeIn(
                      duration: const Duration(milliseconds: 500),
                      delay: const Duration(milliseconds: 500),
                    ),

                    const SizedBox(height: 16),

                    // زر الإلغاء
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(color: Colors.white60, fontSize: 14),
                      ),
                    ).animate().fadeIn(
                      duration: const Duration(milliseconds: 500),
                      delay: const Duration(milliseconds: 600),
                    ),
                  ],
                ),
              ),
            ),
      );
      return;
    }

    // إذا كان المستخدم مشترك، نسمح له بالتنزيل مباشرة
    if (canDownload) {
      setState(() {
        _isLoading = true;
        _downloadProgress = 0.0;
      });

      if (!mounted) return;

      // تنزيل الخلفية باستخدام خدمة التنزيل
      final filePath = await _downloadService.downloadWallpaper(
        widget.wallpaper,
        onProgress: (progress) {
          setState(() {
            _downloadProgress = progress;
          });
        },
      );

      setState(() {
        _isLoading = false;
      });

      if (!mounted) return;

      if (filePath != null) {
        // تم التنزيل بنجاح - يمكن فتح الصورة في المعرض
        _wallpaperService.openImageInGallery(filePath);
      } else {
        // فشل التنزيل - عرض نافذة خطأ
        _showDownloadErrorDialog();
      }
    }
  }

  Future<void> _setWallpaper() async {
    // تعيين الخلفية متاح للجميع مجاناً دائماً
    // لا نحتاج للتحقق من الاشتراك هنا
    if (!mounted) return;

    // عرض نافذة خيارات تعيين الخلفية
    showDialog(
      context: context,
      builder:
          (context) => WallpaperOptionsDialog(
            onApply: _applyWallpaper,
            onCancel: () => Navigator.pop(context),
          ),
    );
  }

  /// تنزيل الخلفية بعد مشاهدة الإعلان
  /// هذه الدالة تُستدعى فقط بعد مشاهدة الإعلان بنجاح
  Future<void> _downloadWallpaperAfterAd() async {
    if (!mounted) return;

    // بدء تنزيل الخلفية بعد مشاهدة الإعلان

    // تأخير قصير لإظهار الرسالة
    await Future.delayed(const Duration(milliseconds: 500));

    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _downloadProgress = 0.0;
    });

    // تنزيل الخلفية باستخدام خدمة التنزيل
    final filePath = await _downloadService.downloadWallpaper(
      widget.wallpaper,
      onProgress: (progress) {
        if (mounted) {
          setState(() {
            _downloadProgress = progress;
          });
        }
      },
    );

    if (!mounted) return;

    setState(() {
      _isLoading = false;
    });

    if (filePath != null) {
      // تم التنزيل بنجاح - عرض نافذة نجاح
      showDialog(
        context: context,
        barrierDismissible: true,
        builder:
            (context) => Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.success.withOpacity(0.3),
                    width: 1.5,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // أيقونة النجاح
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.success.withOpacity(0.2),
                      ),
                      child: const Icon(
                        Icons.check_circle_outline,
                        color: AppColors.success,
                        size: 50,
                      ),
                    ).animate().scale(
                      duration: const Duration(milliseconds: 400),
                      curve: Curves.elasticOut,
                    ),
                    const SizedBox(height: 20),

                    // رسالة النجاح
                    const Text(
                      'تم تنزيل الخلفية بنجاح!',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),

                    const Text(
                      'تم حفظ الخلفية في معرض الصور',
                      style: TextStyle(color: Colors.white70, fontSize: 14),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),

                    // أزرار الإجراءات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // زر الإغلاق
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: Colors.white,
                              side: const BorderSide(color: Colors.white30),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('إغلاق'),
                          ),
                        ),
                        const SizedBox(width: 16),

                        // زر عرض الصورة
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              _wallpaperService.openImageInGallery(filePath);
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.success,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('عرض الصورة'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
      );

      // تحميل إعلان مكافأة جديد للمرة القادمة
      _adService.loadRewardedAd();
    } else {
      // فشل التنزيل - عرض نافذة خطأ
      showDialog(
        context: context,
        barrierDismissible: true,
        builder:
            (context) => Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.black.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.error.withOpacity(0.3),
                    width: 1.5,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // أيقونة الخطأ
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: AppColors.error.withOpacity(0.2),
                      ),
                      child: const Icon(
                        Icons.error_outline,
                        color: AppColors.error,
                        size: 50,
                      ),
                    ),
                    const SizedBox(height: 20),

                    // رسالة الخطأ
                    const Text(
                      'فشل تنزيل الخلفية',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),

                    const Text(
                      'يرجى التحقق من أذونات التخزين والاتصال بالإنترنت والمحاولة مرة أخرى',
                      style: TextStyle(color: Colors.white70, fontSize: 14),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),

                    // زر المحاولة مرة أخرى
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _showDownloadDialog();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        minimumSize: const Size(double.infinity, 45),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('المحاولة مرة أخرى'),
                    ),
                  ],
                ),
              ),
            ),
      );
    }
  }

  // عرض نافذة خيارات التنزيل
  void _showDownloadDialog() {
    if (!mounted) return;

    _downloadWallpaper();
  }

  Future<void> _applyWallpaper(String type) async {
    setState(() {
      _isLoading = true;
      _isSettingWallpaper = true; // تحديث حالة تعيين الخلفية
    });

    // تحويل النوع إلى WallpaperType
    WallpaperType wallpaperType;
    switch (type) {
      case 'home':
        wallpaperType = WallpaperType.home;
        break;
      case 'lock':
        wallpaperType = WallpaperType.lock;
        break;
      case 'both':
        wallpaperType = WallpaperType.both;
        break;
      default:
        wallpaperType = WallpaperType.both;
    }

    // تعيين الخلفية باستخدام WallpaperService
    final success = await _wallpaperService.setWallpaper(
      widget.wallpaper.imageUrl,
      wallpaperType,
    );

    setState(() {
      _isLoading = false;
      _isSettingWallpaper = false; // إعادة تعيين حالة تعيين الخلفية
    });

    // عرض إعلان بيني بعد تعيين الخلفية بنجاح
    if (success && mounted) {
      // تأخير قصير قبل عرض الإعلان
      await Future.delayed(const Duration(milliseconds: 500));
      _adService.showInterstitialAd();
    }

    if (!mounted) return;

    // عرض رسالة نجاح أو فشل
    if (success) {
      // في حالة النجاح، نعرض رسالة توضيحية مع أيقونة
      showDialog(
        context: context,
        barrierDismissible: true,
        builder:
            (context) => AlertDialog(
              backgroundColor: Colors.black.withAlpha(230),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
                side: BorderSide(color: Colors.white.withAlpha(30), width: 1),
              ),
              contentPadding: const EdgeInsets.all(24),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة النجاح
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.success.withAlpha(50),
                    ),
                    child: const Icon(
                      Icons.check_circle,
                      color: AppColors.success,
                      size: 50,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // رسالة النجاح
                  Text(
                    _getSuccessMessage(type),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),

                  // وصف إضافي
                  const Text(
                    'تم تطبيق الخلفية بنجاح على جهازك',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),

                  // زر الإغلاق
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.success,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('حسناً', style: TextStyle(fontSize: 16)),
                  ),
                ],
              ),
            ),
      );
    } else {
      // في حالة الفشل، نعرض رسالة خطأ
      showDialog(
        context: context,
        barrierDismissible: true,
        builder:
            (context) => AlertDialog(
              backgroundColor: Colors.black.withAlpha(230),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
                side: BorderSide(color: Colors.white.withAlpha(30), width: 1),
              ),
              contentPadding: const EdgeInsets.all(24),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة الخطأ
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.error.withAlpha(50),
                    ),
                    child: const Icon(
                      Icons.error_outline,
                      color: AppColors.error,
                      size: 50,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // رسالة الخطأ
                  const Text(
                    'حدث خطأ أثناء تعيين الخلفية',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),

                  // وصف إضافي
                  const Text(
                    'يرجى التحقق من أذونات التطبيق والمحاولة مرة أخرى',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),

                  // أزرار الإجراءات
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // زر الإلغاء
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.pop(context),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.white,
                            side: const BorderSide(color: Colors.white30),
                            minimumSize: const Size(0, 45),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('إلغاء'),
                        ),
                      ),
                      const SizedBox(width: 16),

                      // زر المحاولة مرة أخرى
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.pop(context);
                            _setWallpaper();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            minimumSize: const Size(0, 45),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Text('المحاولة مرة أخرى'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
      );
    }
  }

  // بناء زر احترافي محسن مع تصميم عصري
  Widget _buildEnhancedButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isLoading = false,
    bool showSubscriptionBadge = false,
    bool isActive = false,
    bool isPrimary = false,
    bool isSpecial = false,
    Color color = Colors.white,
    Color? backgroundColor,
  }) {
    // لون الخلفية الافتراضي إذا لم يتم تحديده
    final bgColor = backgroundColor ?? AppColors.secondary;

    // تحديد ألوان التدرج بناءً على لون الخلفية
    List<Color> gradientColors;
    if (bgColor == AppColors.primary) {
      gradientColors = AppColors.primaryGradient;
    } else if (bgColor == AppColors.secondary) {
      gradientColors = AppColors.secondaryGradient;
    } else if (bgColor == AppColors.accent) {
      gradientColors = AppColors.goldGradient;
    } else {
      gradientColors = [bgColor.withAlpha(255), bgColor.withAlpha(200)];
    }

    // التحقق من حالة إعلانات المكافأة في نظام التحكم عن بعد
    final remoteConfigService = RemoteConfigService();
    final bool rewardedAdsEnabled =
        remoteConfigService.isInitialized &&
        remoteConfigService.config.showRewardedAds;

    // تحديد ما إذا كان يجب عرض أيقونة الاشتراك (تظهر فقط عندما تكون الإعلانات معطلة)
    final bool shouldShowSubscriptionBadge = !rewardedAdsEnabled && showSubscriptionBadge;

    return Stack(
      alignment: Alignment.center,
      children: [
        // تأثير متحرك خلف الزر
        Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [bgColor.withAlpha(180), bgColor.withAlpha(0)],
                  stops: const [0.3, 1.0],
                ),
              ),
            )
            .animate(onPlay: (controller) => controller.repeat())
            .scale(
              begin: const Offset(0.8, 0.8),
              end: const Offset(1.2, 1.2),
              duration: const Duration(seconds: 2),
              curve: Curves.easeInOut,
            ),

        // أيقونة الاشتراك المحسنة فوق الزر (تظهر فقط إذا كان التحكم عن بعد مغلق)
        if (shouldShowSubscriptionBadge)
          Positioned(
            top: -12, // تحسين الموضع للأعلى
            right: -8, // تحسين الموضع للجانب
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: const LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFFFFD700), // ذهبي مشرق
                    Color(0xFFD4AF37), // ذهبي متوسط
                    Color(0xFFC9A227), // ذهبي داكن
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.accent.withOpacity(0.6),
                    blurRadius: 12,
                    spreadRadius: 2,
                    offset: const Offset(0, 3),
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.4),
                    blurRadius: 6,
                    spreadRadius: 0,
                    offset: const Offset(0, 1),
                  ),
                ],
                border: Border.all(color: Colors.white, width: 2.5),
              ),
              child: const Center(
                child: Icon(
                  Icons.workspace_premium,
                  color: Colors.black,
                  size: 18,
                  shadows: [
                    Shadow(
                      color: Color(0x66FFFFFF),
                      blurRadius: 1,
                      offset: Offset(0, 0.5),
                    ),
                  ],
                ),
              ),
            )
                .animate()
                .scale(
                  begin: const Offset(0, 0),
                  end: const Offset(1, 1),
                  duration: const Duration(milliseconds: 400),
                  delay: const Duration(milliseconds: 600),
                  curve: Curves.elasticOut,
                )
                .then()
                .shimmer(
                  duration: const Duration(milliseconds: 1500),
                )
                .then()
                .custom(
                  duration: const Duration(milliseconds: 2000),
                  curve: Curves.easeInOut,
                  builder: (context, value, child) => Transform.scale(
                    scale: 1.0 + (0.1 * value),
                    child: child,
                  ),
                ),
          ),

        // الزر الرئيسي المحسن
        AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          width: isPrimary ? 75 : 65,
          height: isPrimary ? 75 : 65,
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: isLoading ? null : onTap,
              borderRadius: BorderRadius.circular(50),
              splashColor: Colors.white.withOpacity(0.2),
              highlightColor: Colors.white.withOpacity(0.1),
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: isActive
                        ? [
                            gradientColors.first,
                            gradientColors.last,
                            Colors.white.withOpacity(0.1)
                          ]
                        : gradientColors,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: bgColor.withOpacity(isPrimary ? 0.5 : 0.3),
                      blurRadius: isPrimary ? 16 : 12,
                      spreadRadius: isPrimary ? 3 : 2,
                      offset: Offset(0, isPrimary ? 4 : 3),
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.4),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 2),
                    ),
                    if (isSpecial)
                      BoxShadow(
                        color: AppColors.accent.withOpacity(0.6),
                        blurRadius: 20,
                        spreadRadius: 0,
                        offset: const Offset(0, 0),
                      ),
                  ],
                  border: Border.all(
                    color: isActive
                        ? Colors.white.withOpacity(0.4)
                        : Colors.white.withOpacity(0.2),
                    width: isActive ? 2 : 1.5,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // الأيقونة أو مؤشر التحميل
                    isLoading
                        ? SizedBox(
                            width: isPrimary ? 28 : 24,
                            height: isPrimary ? 28 : 24,
                            child: CircularProgressIndicator(
                              color: color,
                              strokeWidth: 2.5,
                              backgroundColor: color.withOpacity(0.2),
                            ),
                          )
                        : Icon(
                            icon,
                            color: color,
                            size: isPrimary ? 30 : 26,
                          ),

                    // النص (يظهر فقط إذا لم يكن في حالة تحميل)
                    if (!isLoading && label.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 2),
                        child: Text(
                          label,
                          style: TextStyle(
                            color: color.withOpacity(0.9),
                            fontSize: isPrimary ? 10 : 9,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    )
        .animate()
        .fadeIn(
          duration: const Duration(milliseconds: 500),
          delay: Duration(milliseconds: isPrimary ? 300 : 200),
        )
        .scale(
          begin: const Offset(0.8, 0.8),
          end: const Offset(1, 1),
          duration: const Duration(milliseconds: 500),
          delay: Duration(milliseconds: isPrimary ? 300 : 200),
          curve: Curves.elasticOut,
        );
  }

  // بناء زر احترافي متقدم مع أشرطة ملونة وتصميم أنيق ومدمج
  Widget _buildProfessionalButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required ButtonType buttonType,
    required Color stripColor,
    bool isLoading = false,
    bool showPremiumBadge = false,
    Color color = Colors.white,
    Color? backgroundColor,
    bool isActive = false,
    bool isPrimary = false,
    bool isSpecial = false,
  }) {
    // أحجام دائرية أصغر وأكثر أناقة
    final size = isPrimary ? 60.0 : 55.0;
    final iconSize = isPrimary ? 22.0 : 20.0;
    final fontSize = isPrimary ? 11.0 : 10.0;
    final stripHeight = 3.5;

    // ألوان احترافية ومتناسقة
    Color finalBackgroundColor = backgroundColor ?? const Color(0xFF2A2D3A);
    if (isActive) {
      finalBackgroundColor = const Color(0xFF4A5568);
    } else if (isPrimary) {
      finalBackgroundColor = const Color(0xFF4A5568);
    } else if (isSpecial) {
      finalBackgroundColor = const Color(0xFF553C9A);
    }

    // تحديد لون الأيقونة والنص
    Color finalColor = color;
    if (isActive) {
      finalColor = Colors.white;
    }

    // التحقق من حالة إعلانات المكافأة في نظام التحكم عن بعد
    final remoteConfigService = RemoteConfigService();
    final bool rewardedAdsEnabled =
        remoteConfigService.isInitialized &&
        remoteConfigService.config.showRewardedAds;

    // تحديد ما إذا كان يجب عرض أيقونة الاشتراك (تظهر فقط عندما تكون الإعلانات معطلة)
    final bool shouldShowPremiumBadge = !rewardedAdsEnabled && showPremiumBadge;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // الزر الرئيسي مع Stack للشارة والشريط الملون
          Stack(
            clipBehavior: Clip.none,
            children: [
              // الزر الأساسي
              Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      finalBackgroundColor,
                      finalBackgroundColor.withOpacity(0.85),
                      finalBackgroundColor.withOpacity(0.7),
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isActive
                        ? Colors.white.withOpacity(0.3)
                        : Colors.white.withOpacity(0.08),
                    width: isActive ? 1.5 : 1,
                  ),
                  boxShadow: [
                    // ظل رئيسي أنيق
                    BoxShadow(
                      color: finalBackgroundColor.withOpacity(0.25),
                      blurRadius: isPrimary ? 10 : 8,
                      offset: const Offset(0, 4),
                      spreadRadius: isPrimary ? 1 : 0,
                    ),
                    // ظل عمق مخفف
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: isPrimary ? 15 : 12,
                      offset: const Offset(0, 6),
                    ),
                    // ظل داخلي للبريق
                    BoxShadow(
                      color: Colors.white.withOpacity(0.05),
                      blurRadius: 1,
                      offset: const Offset(0, -1),
                      spreadRadius: -1,
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: isLoading ? null : onTap,
                    borderRadius: BorderRadius.circular(size / 2),
                    splashColor: Colors.white.withOpacity(0.3),
                    highlightColor: Colors.white.withOpacity(0.15),
                    child: Container(
                      width: size,
                      height: size,
                      child: isLoading
                          ? Center(
                              child: SizedBox(
                                width: iconSize,
                                height: iconSize,
                                child: CircularProgressIndicator(
                                  strokeWidth: 3.0,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    finalColor,
                                  ),
                                ),
                              ),
                            )
                          : Icon(
                              icon,
                              color: finalColor,
                              size: iconSize,
                            ),
                    ),
                  ),
                ),
              ),

              // الشريط الملون العلوي (الشارة الاحترافية الدائرية)
              Positioned(
                top: 2,
                left: size * 0.2,
                right: size * 0.2,
                child: Container(
                  height: stripHeight,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        stripColor.withOpacity(0.8),
                        stripColor,
                        stripColor.withOpacity(0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(stripHeight / 2),
                    boxShadow: [
                      BoxShadow(
                        color: stripColor.withOpacity(0.4),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                )
                    .animate(
                      onPlay: (controller) => controller.repeat(),
                    )
                    .shimmer(
                      duration: const Duration(seconds: 3),
                      color: Colors.white.withOpacity(0.6),
                    ),
              ),

              // شارة الاشتراك الأنيقة الدائرية
              if (shouldShowPremiumBadge)
                Positioned(
                  top: -10,
                  right: -6,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        center: Alignment.topLeft,
                        radius: 1.2,
                        colors: [
                          const Color(0xFFFFD700), // ذهبي مشرق
                          const Color(0xFFFFB347), // ذهبي متوسط
                          const Color(0xFFFF8C00), // ذهبي داكن
                        ],
                        stops: const [0.0, 0.6, 1.0],
                      ),
                      border: Border.all(
                        color: Colors.white,
                        width: 2.0,
                      ),
                      boxShadow: [
                        // ظل ذهبي أنيق
                        BoxShadow(
                          color: const Color(0xFFFFD700).withOpacity(0.4),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                          spreadRadius: 1,
                        ),
                        // ظل أسود للعمق
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 6,
                          offset: const Offset(0, 2),
                        ),
                        // ظل داخلي للبريق
                        BoxShadow(
                          color: Colors.white.withOpacity(0.6),
                          blurRadius: 1,
                          offset: const Offset(-1, -1),
                          spreadRadius: -1,
                        ),
                      ],
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.workspace_premium,
                        color: Colors.white,
                        size: 14,
                      ),
                    ),
                  )
                      .animate(
                        onPlay: (controller) => controller.repeat(),
                      )
                      .scale(
                        begin: const Offset(0.0, 0.0),
                        end: const Offset(1.0, 1.0),
                        duration: const Duration(milliseconds: 900),
                        delay: const Duration(milliseconds: 700),
                        curve: Curves.elasticOut,
                      )
                      .then()
                      .shimmer(
                        duration: const Duration(seconds: 2),
                        color: Colors.white.withOpacity(0.7),
                      )
                      .then()
                      .scale(
                        begin: const Offset(1.0, 1.0),
                        end: const Offset(1.15, 1.15),
                        duration: const Duration(milliseconds: 1200),
                        curve: Curves.easeInOut,
                      )
                      .then()
                      .scale(
                        begin: const Offset(1.15, 1.15),
                        end: const Offset(1.0, 1.0),
                        duration: const Duration(milliseconds: 1200),
                        curve: Curves.easeInOut,
                      ),
                ),
            ],
          ),

          // النص التوضيحي الأنيق المتناسق مع الشكل الدائري
          if (label.isNotEmpty) ...[
            const SizedBox(height: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.25),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.white.withOpacity(0.15),
                  width: 0.5,
                ),
              ),
              child: Text(
                label,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: fontSize,
                  fontWeight: FontWeight.w600,
                  letterSpacing: 0.3,
                  shadows: [
                    Shadow(
                      color: Colors.black.withOpacity(0.6),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ],
      ),
    )
        .animate()
        .fadeIn(
          duration: const Duration(milliseconds: 700),
          delay: Duration(
            milliseconds: buttonType == ButtonType.download
                ? 200
                : buttonType == ButtonType.wallpaper
                    ? 400
                    : 0,
          ),
        )
        .scale(
          begin: const Offset(0.7, 0.7),
          end: const Offset(1.0, 1.0),
          duration: const Duration(milliseconds: 700),
          delay: Duration(
            milliseconds: buttonType == ButtonType.download
                ? 200
                : buttonType == ButtonType.wallpaper
                    ? 400
                    : 0,
          ),
          curve: Curves.elasticOut,
        );
  }

  // دالة مساعدة للحفاظ على التوافق مع الكود الموجود
  Widget _buildSimpleButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isLoading = false,
    bool showAdBadge = false,
    Color color = Colors.white,
    Color? backgroundColor,
  }) {
    return _buildEnhancedButton(
      icon: icon,
      label: '',
      onTap: onTap,
      isLoading: isLoading,
      showSubscriptionBadge: showAdBadge,
      color: color,
      backgroundColor: backgroundColor,
    );
  }

  // الحصول على رسالة النجاح المناسبة
  String _getSuccessMessage(String type) {
    switch (type) {
      case 'home':
        return 'تم تعيين الخلفية للشاشة الرئيسية بنجاح';
      case 'lock':
        return 'تم تعيين الخلفية لشاشة القفل بنجاح';
      case 'both':
        return 'تم تعيين الخلفية للشاشتين بنجاح';
      default:
        return 'تم تعيين الخلفية بنجاح';
    }
  }

  // عرض نافذة خطأ تحميل الإعلان
  void _showAdLoadErrorDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(230),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppColors.error.withAlpha(76),
                  width: 1.5,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة الخطأ
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.error.withAlpha(51),
                    ),
                    child: const Icon(
                      Icons.error_outline,
                      color: AppColors.error,
                      size: 40,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // رسالة الخطأ
                  const Text(
                    'فشل تحميل الإعلان',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),

                  const Text(
                    'حاول مرة أخرى لاحقًا أو اشترك للوصول غير المحدود',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),

                  // زر الإغلاق
                  ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.error,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('حسناً', style: TextStyle(fontSize: 16)),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  // عرض نافذة خطأ التنزيل
  void _showDownloadErrorDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: true,
      builder:
          (context) => Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(230),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: AppColors.error.withAlpha(76),
                  width: 1.5,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة الخطأ
                  Container(
                    width: 70,
                    height: 70,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppColors.error.withAlpha(51),
                    ),
                    child: const Icon(
                      Icons.file_download_off,
                      color: AppColors.error,
                      size: 40,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // رسالة الخطأ
                  const Text(
                    'فشل تنزيل الخلفية',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),

                  const Text(
                    'يرجى التحقق من أذونات التخزين والاتصال بالإنترنت والمحاولة مرة أخرى',
                    style: TextStyle(color: Colors.white70, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),

                  // زر المحاولة مرة أخرى
                  ElevatedButton(
                    onPressed: () {
                      Navigator.pop(context);
                      _downloadWallpaper();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      minimumSize: const Size(double.infinity, 45),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'المحاولة مرة أخرى',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }
}
