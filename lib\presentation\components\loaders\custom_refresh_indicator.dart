import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';
import 'enhanced_loading_screen.dart';

/// مؤشر سحب مخصص
class CustomRefreshIndicator extends StatefulWidget {
  final Widget child;
  final Future<void> Function() onRefresh;
  final String refreshText;
  final String refreshingText;
  final String refreshedText;
  final Color color;

  const CustomRefreshIndicator({
    super.key,
    required this.child,
    required this.onRefresh,
    this.refreshText = 'اسحب للتحديث',
    this.refreshingText = 'جاري تحديث الخلفيات...',
    this.refreshedText = 'تم تحديث الخلفيات',
    this.color = AppColors.primary,
  });

  @override
  State<CustomRefreshIndicator> createState() => _CustomRefreshIndicatorState();
}

class _CustomRefreshIndicatorState extends State<CustomRefreshIndicator> {
  bool _isRefreshing = false;
  double _progress = 0.0;
  bool _disposed = false;

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }

  // تحديث الحالة بأمان
  void _safeSetState(VoidCallback fn) {
    if (!_disposed && mounted) {
      setState(fn);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // مؤشر السحب للتحديث
        RefreshIndicator(
          onRefresh: () async {
            // عرض شاشة التحميل المحسنة
            _safeSetState(() {
              _isRefreshing = true;
              _progress = 0.2;
            });

            try {
              // تحديث التقدم بشكل تدريجي
              _simulateProgress();

              // تنفيذ عملية التحديث
              await widget.onRefresh();

              // إكمال التقدم
              _safeSetState(() {
                _progress = 1.0;
              });

              // إخفاء شاشة التحميل بعد فترة قصيرة
              await Future.delayed(const Duration(milliseconds: 500));
            } catch (e) {
              debugPrint('خطأ في التحديث: $e');
            } finally {
              // إخفاء شاشة التحميل
              _safeSetState(() {
                _isRefreshing = false;
              });
            }
          },
          color: widget.color,
          backgroundColor: AppColors.surface,
          strokeWidth: 2.5,
          displacement: 20,
          edgeOffset: 20,
          child: widget.child,
        ),

        // شاشة التحميل المحسنة
        if (_isRefreshing)
          EnhancedLoadingScreen(
            loadingText: widget.refreshingText,
            progress: _progress,
            onRefresh: () async {
              // إعادة تنفيذ عملية التحديث
              _safeSetState(() {
                _progress = 0.2;
              });

              _simulateProgress();
              await widget.onRefresh();

              _safeSetState(() {
                _progress = 1.0;
              });

              await Future.delayed(const Duration(milliseconds: 500));

              _safeSetState(() {
                _isRefreshing = false;
              });
            },
          ),
      ],
    );
  }

  // محاكاة تقدم التحميل
  void _simulateProgress() {
    if (_disposed) return;

    Future.delayed(const Duration(milliseconds: 300), () {
      if (_disposed) return;

      if (_isRefreshing && _progress < 0.7) {
        _safeSetState(() {
          _progress += 0.1;
        });
        _simulateProgress();
      }
    });
  }
}
