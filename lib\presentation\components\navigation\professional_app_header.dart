import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/shadows.dart';
import '../effects/glass_container.dart';
import '../buttons/premium_button.dart';

/// شريط علوي احترافي
class ProfessionalAppHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool showBackButton;
  final bool showPremiumButton;
  final VoidCallback? onPremiumPressed;
  final List<Widget>? actions;
  final double height;
  final bool isTransparent;
  final Color? backgroundColor;
  final Gradient? backgroundGradient;

  const ProfessionalAppHeader({
    Key? key,
    required this.title,
    this.subtitle,
    this.showBackButton = false,
    this.showPremiumButton = true,
    this.onPremiumPressed,
    this.actions,
    this.height = 80,
    this.isTransparent = false,
    this.backgroundColor,
    this.backgroundGradient,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // استخدام MediaQuery للحصول على أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;
    final topPadding = MediaQuery.of(context).padding.top;

    return Container(
          height: height + topPadding,
          margin: const EdgeInsets.fromLTRB(
            AppDimensions.marginMedium,
            AppDimensions.marginMedium,
            AppDimensions.marginMedium,
            0,
          ),
          child: GlassContainer(
            borderRadius: AppDimensions.radiusLarge,
            blur: 10,
            opacity: isTransparent ? 0.5 : 0.8,
            border: 1.5,
            borderColor: Colors.white.withAlpha(51),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.15),
                blurRadius: 15,
                spreadRadius: 1,
                offset: const Offset(0, 4),
              ),
            ],
            gradient:
                backgroundGradient ??
                LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    (backgroundColor ?? AppColors.surface).withOpacity(0.7),
                    (backgroundColor ?? AppColors.surface).withOpacity(0.5),
                  ],
                ),
            child: Padding(
              padding: EdgeInsets.only(
                left: AppDimensions.paddingMedium,
                right: AppDimensions.paddingMedium,
                top: topPadding,
              ),
              child: _buildHeaderContent(context, screenSize),
            ),
          ),
        )
        .animate()
        .fadeIn(
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOut,
        )
        .slideY(
          begin: -0.2,
          end: 0,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOut,
        );
  }

  Widget _buildHeaderContent(BuildContext context, Size screenSize) {
    return Row(
      textDirection: TextDirection.rtl, // RTL layout for Arabic
      children: [
        // زر الاشتراك المميز
        if (showPremiumButton)
          GestureDetector(
            onTap: onPremiumPressed ?? () => context.push('/subscription'),
            child: _buildPremiumButton(),
          ).animate().fadeIn(
            delay: const Duration(milliseconds: 200),
            duration: const Duration(milliseconds: 400),
          ),

        // أزرار إضافية
        if (actions != null) ...actions!,

        const SizedBox(width: AppDimensions.marginMedium),

        // عنوان الصفحة
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title,
                style: AppTypography.headingMedium.copyWith(
                  color: Colors.white,
                  shadows: AppShadows.textMedium,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ).animate().fadeIn(
                delay: const Duration(milliseconds: 100),
                duration: const Duration(milliseconds: 400),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 2),
                Text(
                  subtitle!,
                  style: AppTypography.bodySmall.copyWith(
                    color: Colors.white.withOpacity(0.8),
                    shadows: AppShadows.textSmall,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ).animate().fadeIn(
                  delay: const Duration(milliseconds: 200),
                  duration: const Duration(milliseconds: 400),
                ),
              ],
            ],
          ),
        ),

        // زر الرجوع
        if (showBackButton)
          GestureDetector(
            onTap: () => Navigator.of(context).pop(),
            child: Container(
              width: 40,
              height: 40,
              margin: const EdgeInsets.only(left: AppDimensions.marginSmall),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                border: Border.all(
                  color: Colors.white.withOpacity(0.1),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.arrow_back_ios_new_rounded,
                color: Colors.white,
                size: 20,
              ),
            ),
          ).animate().fadeIn(
            delay: const Duration(milliseconds: 300),
            duration: const Duration(milliseconds: 400),
          ),
      ],
    );
  }

  Widget _buildPremiumButton() {
    return Container(
      height: 40,
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
        vertical: AppDimensions.paddingXSmall,
      ),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFFFFD700).withOpacity(0.3),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const Icon(
            Icons.workspace_premium_rounded,
            color: Colors.black87,
            size: 20,
          ),
          const SizedBox(width: 4),
          Text(
            'مميز',
            style: AppTypography.labelMedium.copyWith(
              color: Colors.black87,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// شريط علوي ثابت عند التمرير
class ProfessionalStickyHeader extends SliverPersistentHeaderDelegate {
  final String title;
  final String subtitle;
  final Color accentColor;
  final VoidCallback? onPremiumPressed;
  final double collapsedHeight;
  final double expandedHeight;
  final bool showBackButton;
  final String? backRoute;
  final Widget? leadingIcon;
  final List<Widget>? actions;

  ProfessionalStickyHeader({
    required this.title,
    required this.subtitle,
    this.accentColor = AppColors.accent,
    this.onPremiumPressed,
    this.collapsedHeight = 70,
    this.expandedHeight = 120,
    this.showBackButton = false,
    this.backRoute,
    this.leadingIcon,
    this.actions,
  });

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    final double percentage = shrinkOffset / maxExtent;
    final double opacity = 1 - percentage;
    final double topPadding = MediaQuery.of(context).padding.top;

    return Container(
      height: maxExtent,
      padding: EdgeInsets.only(top: topPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.background,
            AppColors.background.withOpacity(0.9),
            AppColors.background.withOpacity(0.8),
            AppColors.background.withOpacity(0.0),
          ],
          stops: const [0.0, 0.3, 0.7, 1.0],
        ),
      ),
      child: Stack(
        fit: StackFit.expand,
        children: [
          // عنوان متحرك
          Positioned(
            top: topPadding + lerpDouble(expandedHeight * 0.2, 15, percentage)!,
            right: lerpDouble(20, 60, percentage)!,
            left: 80,
            child: Opacity(
              opacity: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان الرئيسي
                  Text(
                    title,
                    style: AppTypography.headingMedium.copyWith(
                      fontSize: lerpDouble(24, 20, percentage),
                      color: Colors.white,
                      shadows: AppShadows.textMedium,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  // العنوان الفرعي
                  if (opacity > 0.3)
                    Opacity(
                      opacity: opacity,
                      child: Text(
                        subtitle,
                        style: AppTypography.bodySmall.copyWith(
                          fontSize: lerpDouble(14, 12, percentage),
                          color: Colors.white.withOpacity(0.8),
                          shadows: AppShadows.textSmall,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                ],
              ),
            ),
          ),

          // زر الاشتراك المميز
          Positioned(
            top: topPadding + lerpDouble(expandedHeight * 0.2, 15, percentage)!,
            left: 20,
            child: _buildPremiumButton(context, percentage),
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumButton(BuildContext context, double percentage) {
    final double scale = lerpDouble(1.0, 0.8, percentage)!;

    return Transform.scale(
      scale: scale,
      child: GestureDetector(
        onTap: onPremiumPressed ?? () => context.push('/subscription'),
        child: Container(
          height: 40,
          padding: const EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
            vertical: AppDimensions.paddingXSmall,
          ),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFFFD700).withOpacity(0.3),
                blurRadius: 8,
                spreadRadius: 1,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(
                Icons.workspace_premium_rounded,
                color: Colors.black87,
                size: 20,
              ),
              const SizedBox(width: 4),
              Text(
                'مميز',
                style: AppTypography.labelMedium.copyWith(
                  color: Colors.black87,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  double get maxExtent => expandedHeight;

  @override
  double get minExtent => collapsedHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }

  // Helper function for linear interpolation
  double? lerpDouble(double? a, double? b, double t) {
    if (a == null && b == null) return null;
    a ??= 0.0;
    b ??= 0.0;
    return a + (b - a) * t;
  }
}
