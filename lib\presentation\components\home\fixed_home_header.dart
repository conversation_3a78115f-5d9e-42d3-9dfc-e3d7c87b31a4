import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../../data/models/wallpaper.dart';
import '../home/<USER>';
import '../navigation/browse_navigation_bar.dart';

/// مكون ثابت يجمع رأس الصفحة الرئيسية والفئات وشريط التصفح
class FixedHomeHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback? onSubscribePressed;
  final VoidCallback onViewAllPressed;
  final VoidCallback onShowAllWallpapersPressed;
  final List<Category> categories;
  final bool isLoading;

  const FixedHomeHeader({
    super.key,
    required this.title,
    required this.subtitle,
    required this.onSubscribePressed,
    required this.onViewAllPressed,
    required this.onShowAllWallpapersPressed,
    required this.categories,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    // استخدام MediaQuery للحصول على أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;
    final categoryWidth = screenSize.width * 0.28; // حجم نسبي للفئة
    final categoryHeight = categoryWidth * 0.8; // ارتفاع نسبي للفئة

    return Container(
      color: Colors.black, // خلفية سوداء ثابتة
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // رأس الصفحة
          PremiumHomeHeader(
            title: title,
            subtitle: subtitle,
            onSubscribePressed: onSubscribePressed,
          ),

          // قسم الفئات
          Container(
            height: categoryHeight * 1.2, // تقليل ارتفاع قسم الفئات
            padding: const EdgeInsets.symmetric(vertical: 12),
            child:
                isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : LayoutBuilder(
                      builder: (context, constraints) {
                        // حساب العرض المتاح للقائمة
                        final availableWidth = constraints.maxWidth;
                        // حساب عرض العنصر بناءً على العرض المتاح - استخدام دوائر
                        final itemSize = availableWidth / 3.5;

                        return ListView.builder(
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          itemCount: categories.length,
                          itemBuilder: (context, index) {
                            final category = categories[index];
                            return GestureDetector(
                              onTap: () {
                                context.push(
                                  '/category/${category.id}',
                                  extra: category,
                                );
                              },
                              child: Container(
                                width: itemSize * 0.8, // تصغير حجم الفئات
                                height: itemSize * 0.8, // تصغير حجم الفئات
                                margin: const EdgeInsets.only(right: 16),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withAlpha(60),
                                      blurRadius: 12,
                                      spreadRadius: 1,
                                      offset: const Offset(0, 4),
                                    ),
                                    BoxShadow(
                                      color: Colors.black.withAlpha(40),
                                      blurRadius: 6,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                  border: Border.all(
                                    color: Colors.white.withAlpha(40),
                                    width: 2,
                                  ),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(itemSize),
                                  child: Stack(
                                    fit: StackFit.expand,
                                    children: [
                                      // صورة الفئة
                                      Positioned.fill(
                                        child: _getCategoryImage(category),
                                      ),

                                      // تراكب لوني متدرج لتحسين وضوح النص
                                      Positioned.fill(
                                        child: Container(
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            gradient: RadialGradient(
                                              center: Alignment.center,
                                              radius: 0.8,
                                              colors: [
                                                Colors.black.withAlpha(40),
                                                Colors.black.withAlpha(120),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),

                                      // محتوى الفئة (العنوان داخل الصورة)
                                      Center(
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: Colors.black.withAlpha(120),
                                            borderRadius: BorderRadius.circular(
                                              8,
                                            ),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withAlpha(
                                                  80,
                                                ),
                                                blurRadius: 3,
                                                spreadRadius: 0,
                                                offset: const Offset(0, 1),
                                              ),
                                            ],
                                          ),
                                          child: Text(
                                            category.name,
                                            style: AppTypography.titleSmall
                                                .copyWith(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.bold,
                                                  fontSize:
                                                      12, // تصغير حجم الخط
                                                  shadows: [
                                                    Shadow(
                                                      color: Colors.black
                                                          .withAlpha(200),
                                                      blurRadius: 3,
                                                      offset: const Offset(
                                                        0,
                                                        1,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                            textAlign: TextAlign.center,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ),
                                      ),

                                      // تأثير لامع على الحواف
                                      Positioned.fill(
                                        child: Container(
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: Colors.white.withAlpha(60),
                                              width: 1.5,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        );
                      },
                    ),
          ),

          // شريط التصفح
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: BrowseNavigationBar(
              onViewAllPressed: onViewAllPressed,
              onShowAllWallpapersPressed: onShowAllWallpapersPressed,
              isGlass: false,
              isFixed: true,
            ),
          ),
        ],
      ),
    );
  }

  // دالة للحصول على صورة الفئة المناسبة
  Widget _getCategoryImage(Category category) {
    // تحديد مسار الصورة بناءً على معرف الفئة
    String imagePath;

    switch (category.id) {
      case 'مسجد':
        imagePath = 'assets/images/categories/mosque.jpg';
        break;
      case 'دعاء':
        imagePath = 'assets/images/categories/dua.jpg';
        break;
      case 'القران':
        imagePath = 'assets/images/categories/quran.jpg';
        break;
      case 'ذكر الله':
        imagePath = 'assets/images/categories/dhikr.jpg';
        break;
      case 'اسم الله الحسنى':
        imagePath = 'assets/images/categories/names.webp';
        break;
      case 'متنوعه':
        imagePath = 'assets/images/categories/misc.jpg';
        break;
      default:
        // استخدام صورة الفئة إذا كانت متوفرة، وإلا استخدام صورة افتراضية
        if (category.imageUrl.isNotEmpty) {
          return CachedNetworkImage(
            imageUrl: category.imageUrl,
            fit: BoxFit.cover,
            placeholder:
                (context, url) => Container(
                  color: (category.color ?? AppColors.primary).withAlpha(100),
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      strokeWidth: 2,
                    ),
                  ),
                ),
            errorWidget:
                (context, url, error) => Image.asset(
                  'assets/images/categories/misc.jpg',
                  fit: BoxFit.cover,
                ),
          );
        }
        imagePath = 'assets/images/categories/misc.jpg';
    }

    // عرض الصورة المحلية
    return Image.asset(
      imagePath,
      fit: BoxFit.cover,
      errorBuilder: (context, error, stackTrace) {
        // في حالة حدوث خطأ في تحميل الصورة المحلية
        return Container(
          color: (category.color ?? AppColors.primary),
          child: const Center(
            child: Icon(
              Icons.image_not_supported_outlined,
              color: Colors.white,
              size: 24,
            ),
          ),
        );
      },
    );
  }
}
