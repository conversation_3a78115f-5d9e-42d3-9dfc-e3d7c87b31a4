package com.islamiclandporta.islam.allahwallpaper.ahmad.np;

import android.content.Context;
import android.media.MediaScannerConnection;
import android.util.Log;

import androidx.annotation.NonNull;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class MediaScannerMethodChannel implements MethodChannel.MethodCallHandler {
    private static final String TAG = "MediaScannerChannel";
    private static final String CHANNEL = "com.islamiclandporta.islam.allahwallpaper.ahmad.np/media_scanner";
    private final Context context;

    public MediaScannerMethodChannel(Context context) {
        this.context = context;
    }

    public static void registerWith(FlutterEngine flutterEngine, Context context) {
        MethodChannel channel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL);
        MediaScannerMethodChannel instance = new MediaScannerMethodChannel(context);
        channel.setMethodCallHandler(instance);
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        // التحقق من صحة المعاملات أولاً لتجنب IllegalArgumentException
        if (call == null || result == null) {
            Log.e(TAG, "MethodCall أو Result null");
            return;
        }

        // تنفيذ العملية في خيط منفصل لتجنب ANR
        new Thread(() -> {
            android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());

            try {
                if ("scanFile".equals(call.method)) {
                    // التحقق من صحة المعاملات
                    Object filePathObj = call.argument("filePath");
                    if (filePathObj == null) {
                        mainHandler.post(() -> {
                            try {
                                result.error("INVALID_ARGUMENT", "مسار الملف مفقود", null);
                            } catch (Exception e) {
                                Log.e(TAG, "خطأ في إرجاع نتيجة الخطأ: " + e.getMessage());
                            }
                        });
                        return;
                    }

                    String filePath = filePathObj.toString();
                    if (filePath.isEmpty()) {
                        mainHandler.post(() -> {
                            try {
                                result.error("INVALID_ARGUMENT", "مسار الملف فارغ", null);
                            } catch (Exception e) {
                                Log.e(TAG, "خطأ في إرجاع نتيجة الخطأ: " + e.getMessage());
                            }
                        });
                        return;
                    }

                    // تنفيذ عملية المسح
                    boolean success = scanFile(filePath);

                    // إرجاع النتيجة في الخيط الرئيسي مع معالجة الأخطاء
                    mainHandler.post(() -> {
                        try {
                            result.success(success);
                        } catch (Exception e) {
                            Log.e(TAG, "خطأ في إرجاع نتيجة النجاح: " + e.getMessage());
                        }
                    });

                } else {
                    // طريقة غير مدعومة
                    mainHandler.post(() -> {
                        try {
                            result.notImplemented();
                        } catch (Exception e) {
                            Log.e(TAG, "خطأ في إرجاع notImplemented: " + e.getMessage());
                        }
                    });
                }

            } catch (Exception e) {
                Log.e(TAG, "خطأ في معالجة استدعاء الطريقة: " + e.getMessage(), e);
                mainHandler.post(() -> {
                    try {
                        result.error("EXECUTION_ERROR", "خطأ في تنفيذ العملية: " + e.getMessage(), null);
                    } catch (Exception resultError) {
                        Log.e(TAG, "خطأ في إرجاع نتيجة الخطأ: " + resultError.getMessage());
                    }
                });
            }
        }).start();
    }

    private boolean scanFile(String filePath) {
        try {
            // التحقق من وجود الملف
            java.io.File file = new java.io.File(filePath);
            if (!file.exists()) {
                Log.e(TAG, "الملف غير موجود: " + filePath);
                return false;
            }

            // استخدام CountDownLatch للانتظار حتى انتهاء المسح
            final java.util.concurrent.CountDownLatch latch = new java.util.concurrent.CountDownLatch(1);
            final boolean[] scanResult = {false};

            MediaScannerConnection.scanFile(
                    context,
                    new String[]{filePath},
                    new String[]{"image/jpeg", "image/png", "image/webp"},
                    (path, uri) -> {
                        try {
                            Log.d(TAG, "تم مسح الملف بنجاح: " + path);
                            Log.d(TAG, "URI: " + uri);
                            scanResult[0] = (uri != null);
                        } catch (Exception e) {
                            Log.e(TAG, "خطأ في callback المسح: " + e.getMessage());
                        } finally {
                            latch.countDown();
                        }
                    }
            );

            // انتظار انتهاء المسح لمدة أقصاها 5 ثوان
            boolean completed = latch.await(5, java.util.concurrent.TimeUnit.SECONDS);
            if (!completed) {
                Log.w(TAG, "انتهت مهلة انتظار مسح الملف");
                return false;
            }

            return scanResult[0];
        } catch (Exception e) {
            Log.e(TAG, "خطأ في مسح الملف: " + e.getMessage());
            return false;
        }
    }
}
