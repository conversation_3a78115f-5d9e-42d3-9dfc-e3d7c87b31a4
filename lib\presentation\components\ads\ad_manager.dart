import 'package:flutter/material.dart';
import '../../../data/services/subscription_service.dart';

/// مدير الإعلانات للتطبيق
/// يتحكم في عرض الإعلانات بناءً على حالة اشتراك المستخدم
class AdManager {
  static final AdManager _instance = AdManager._internal();
  final SubscriptionService _subscriptionService = SubscriptionService();
  
  // متغيرات لتخزين حالة الإعلانات
  bool _isInitialized = false;
  bool _shouldHideAds = false;
  
  // الحصول على نسخة واحدة من المدير (Singleton)
  factory AdManager() {
    return _instance;
  }
  
  AdManager._internal();
  
  /// تهيئة مدير الإعلانات
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // تهيئة خدمة الاشتراك
      await _subscriptionService.initialize();
      
      // التحقق من حالة الاشتراك
      _shouldHideAds = await _subscriptionService.shouldHideAds();
      
      // هنا يمكن إضافة تهيئة مكتبة AdMob أو أي مكتبة إعلانات أخرى
      
      _isInitialized = true;
      debugPrint('✅ تم تهيئة مدير الإعلانات بنجاح');
      debugPrint('ℹ️ حالة إخفاء الإعلانات: $_shouldHideAds');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مدير الإعلانات: $e');
    }
  }
  
  /// التحقق من حالة إخفاء الإعلانات
  Future<bool> shouldHideAds() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    // تحديث حالة إخفاء الإعلانات
    _shouldHideAds = await _subscriptionService.shouldHideAds();
    return _shouldHideAds;
  }
  
  /// تحديث حالة الإعلانات
  Future<void> updateAdStatus() async {
    _shouldHideAds = await _subscriptionService.shouldHideAds();
    debugPrint('ℹ️ تم تحديث حالة إخفاء الإعلانات: $_shouldHideAds');
  }
}
