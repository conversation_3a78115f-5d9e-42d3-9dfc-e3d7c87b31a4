import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:http/http.dart' as http;
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../data/models/remote_config.dart';
import '../utils/logger.dart';

// نوع دالة الاستماع للتغييرات
typedef RemoteConfigListener = void Function();

/// خدمة للتحكم عن بعد في التطبيق
class RemoteConfigService {
  static final RemoteConfigService _instance = RemoteConfigService._internal();
  factory RemoteConfigService() => _instance;
  RemoteConfigService._internal();

  // الإعدادات الافتراضية
  RemoteConfig? _config;
  String _currentVersion = '';
  bool _isInitialized = false;
  final String _configKey = 'remote_config_data';
  final String _configTimestampKey = 'remote_config_timestamp';

  // مدة صلاحية التكوين المخزن (6 ساعات)
  final Duration _cacheDuration = const Duration(hours: 6);

  // مدة التحقق الدوري من التغييرات (10 ثوانٍ)
  final Duration _refreshInterval = const Duration(seconds: 10);

  // عنوان URL للتكوين
  String _configUrl =
      "https://drive.google.com/uc?id=1mut1fYcZMmVFFBuFKjmTEwb-ds6KUnZM";

  // مؤقت للتحقق الدوري
  Timer? _refreshTimer;

  // حالة التحقق من التغييرات
  bool _isRefreshing = false;

  // قائمة المستمعين للتغييرات
  final List<RemoteConfigListener> _listeners = [];

  // آخر وقت تم فيه تحديث التكوين
  DateTime _lastUpdateTime = DateTime.now();

  // الحصول على التكوين الحالي
  RemoteConfig get config => _config ?? RemoteConfig.defaultConfig();
  bool get isInitialized => _isInitialized;
  String get currentVersion => _currentVersion;
  bool get needsUpdate => _isUpdateRequired();
  DateTime get lastUpdateTime => _lastUpdateTime;
  String get configUrl => _configUrl;

  /// تعيين عنوان URL للتكوين
  void setConfigUrl(String url) {
    if (url.isEmpty) {
      Logger.log('⚠️ عنوان URL للتكوين فارغ، لم يتم تغييره');
      return;
    }

    _configUrl = url;
    Logger.log('✅ تم تعيين عنوان URL للتكوين: $_configUrl');
  }

  /// الحصول على حالة التكوين
  Map<String, dynamic> getConfigStatus() {
    return {
      'isInitialized': _isInitialized,
      'lastUpdateTime': _lastUpdateTime.toIso8601String(),
      'configUrl': _configUrl,
      'isRefreshing': _isRefreshing,
      'currentVersion': _currentVersion,
      'remoteVersion': _config?.appVersion ?? 'غير متوفر',
      'needsUpdate': needsUpdate,
      'showBannerAds': _config?.showBannerAds ?? true,
      'showInterstitialAds': _config?.showInterstitialAds ?? true,
      'showRewardedAds': _config?.showRewardedAds ?? true,
    };
  }

  /// طباعة حالة التكوين للتشخيص
  void logConfigStatus() {
    Logger.log('📊 حالة التكوين عن بعد:');
    Logger.log('📊 تم التهيئة: ${_isInitialized ? "نعم" : "لا"}');
    Logger.log('📊 آخر تحديث: $_lastUpdateTime');
    Logger.log('📊 عنوان URL للتكوين: $_configUrl');
    Logger.log('📊 جاري التحديث: ${_isRefreshing ? "نعم" : "لا"}');
    Logger.log('📊 إصدار التطبيق الحالي: $_currentVersion');
    Logger.log(
      '📊 إصدار التطبيق عن بعد: ${_config?.appVersion ?? "غير متوفر"}',
    );
    Logger.log('📊 يحتاج إلى تحديث: ${needsUpdate ? "نعم" : "لا"}');
    Logger.log('📊 معرفات الإعلانات: تستخدم المعرفات الثابتة في الكود');
    Logger.log(
      '📊 عرض إعلانات البانر: ${_config?.showBannerAds ?? true ? "مفعل" : "معطل"}',
    );
    Logger.log(
      '📊 عرض الإعلانات البينية: ${_config?.showInterstitialAds ?? true ? "مفعل" : "معطل"}',
    );
    Logger.log(
      '📊 عرض إعلانات المكافأة: ${_config?.showRewardedAds ?? true ? "مفعل" : "معطل"}',
    );

    // طباعة الإعدادات الإضافية
    if (_config?.additionalSettings != null &&
        _config!.additionalSettings.isNotEmpty) {
      Logger.log('📊 الإعدادات الإضافية:');
      _config!.additionalSettings.forEach((key, value) {
        Logger.log('📊   $key: $value');
      });
    }
  }

  /// إضافة مستمع للتغييرات
  void addListener(RemoteConfigListener listener) {
    if (!_listeners.contains(listener)) {
      _listeners.add(listener);
    }
  }

  /// إزالة مستمع للتغييرات
  void removeListener(RemoteConfigListener listener) {
    _listeners.remove(listener);
  }

  /// إشعار جميع المستمعين بالتغييرات
  void _notifyListeners() {
    for (final listener in _listeners) {
      listener();
    }
  }

  // لا نجلب معرفات الإعلانات من التحكم عن بعد
  // نستخدم المعرفات الثابتة في ad_service.dart

  /// تهيئة الخدمة
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // الحصول على إصدار التطبيق الحالي
      final packageInfo = await PackageInfo.fromPlatform();
      _currentVersion = packageInfo.version;

      // محاولة تحميل التكوين المخزن محليًا
      await _loadCachedConfig();

      // بدء التحقق الدوري من التغييرات
      _startPeriodicRefresh();

      _isInitialized = true;
      Logger.log('✅ تم تهيئة خدمة التحكم عن بعد بنجاح');

      // طباعة حالة التكوين للتشخيص
      logConfigStatus();
    } catch (e) {
      Logger.log('❌ خطأ في تهيئة خدمة التحكم عن بعد: $e');
    }
  }

  /// بدء التحقق الدوري من التغييرات
  void _startPeriodicRefresh() {
    // إلغاء المؤقت السابق إذا كان موجودًا
    _refreshTimer?.cancel();

    // بدء مؤقت جديد للتحقق الدوري
    _refreshTimer = Timer.periodic(_refreshInterval, (_) {
      _refreshConfig();
    });

    Logger.log(
      '🔄 تم بدء التحقق الدوري من التغييرات كل ${_refreshInterval.inSeconds} ثانية',
    );
  }

  /// تحديث التكوين بشكل دوري (داخلي)
  Future<void> _refreshConfig() async {
    // تجنب التحقق المتزامن
    if (_isRefreshing) return;

    _isRefreshing = true;

    try {
      await fetchConfig(_configUrl);
    } catch (e) {
      Logger.log('❌ خطأ في التحقق الدوري من التغييرات: $e');
    } finally {
      _isRefreshing = false;
    }
  }

  /// تحديث التكوين يدويًا (عام)
  Future<void> refreshConfig() async {
    Logger.log('🔄 جاري تحديث التكوين يدويًا...');

    // تجنب التحقق المتزامن
    if (_isRefreshing) {
      Logger.log('⚠️ جاري بالفعل تحديث التكوين، يرجى الانتظار...');
      return;
    }

    _isRefreshing = true;

    try {
      await fetchConfig(_configUrl);
      Logger.log('✅ تم تحديث التكوين يدويًا بنجاح');

      // طباعة حالة التكوين للتشخيص
      logConfigStatus();
    } catch (e) {
      Logger.log('❌ خطأ في تحديث التكوين يدويًا: $e');
    } finally {
      _isRefreshing = false;
    }
  }

  /// التحقق مما إذا كانت إعدادات الإعلانات قد تغيرت
  bool _checkIfAdsIdsChanged(RemoteConfig? oldConfig) {
    if (oldConfig == null || _config == null) return true;

    // نتحقق فقط من تغيير حالة تفعيل/إيقاف الإعلانات
    return oldConfig.showBannerAds != _config!.showBannerAds ||
        oldConfig.showInterstitialAds != _config!.showInterstitialAds ||
        oldConfig.showRewardedAds != _config!.showRewardedAds;
  }

  /// طباعة معلومات تفصيلية عن التغييرات
  void _logDetailedChanges(RemoteConfig? oldConfig) {
    if (oldConfig == null || _config == null) return;

    // لا نتحقق من تغييرات معرفات الإعلانات لأنها ثابتة في الكود

    // التحقق من تغييرات حالة تفعيل الإعلانات
    if (oldConfig.showBannerAds != _config!.showBannerAds) {
      Logger.log('📊 تغيير حالة إعلانات البانر:');
      Logger.log('   - القديم: ${oldConfig.showBannerAds ? "مفعل" : "معطل"}');
      Logger.log('   - الجديد: ${_config!.showBannerAds ? "مفعل" : "معطل"}');
    }

    if (oldConfig.showInterstitialAds != _config!.showInterstitialAds) {
      Logger.log('📊 تغيير حالة الإعلانات البينية:');
      Logger.log(
        '   - القديم: ${oldConfig.showInterstitialAds ? "مفعل" : "معطل"}',
      );
      Logger.log(
        '   - الجديد: ${_config!.showInterstitialAds ? "مفعل" : "معطل"}',
      );
    }

    if (oldConfig.showRewardedAds != _config!.showRewardedAds) {
      Logger.log('📊 تغيير حالة إعلانات المكافأة:');
      Logger.log('   - القديم: ${oldConfig.showRewardedAds ? "مفعل" : "معطل"}');
      Logger.log('   - الجديد: ${_config!.showRewardedAds ? "مفعل" : "معطل"}');
    }
  }

  /// تحديث التكوين من الخادم
  Future<void> fetchConfig(String url) async {
    try {
      Logger.log('🔄 جاري تحميل إعدادات التحكم عن بعد...');

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final List<dynamic> jsonData = json.decode(response.body);

        if (jsonData.isNotEmpty && jsonData[0] is Map<String, dynamic>) {
          // تحويل البيانات إلى الصيغة المطلوبة
          final Map<String, dynamic> configData = _convertToConfigFormat(
            jsonData[0],
          );

          // تحديث التكوين
          final oldConfig = _config;
          _config = RemoteConfig.fromJson(configData);

          // تحديث وقت آخر تحديث
          _lastUpdateTime = DateTime.now();

          // حفظ التكوين محليًا (باستثناء معرفات الإعلانات)
          await _cacheConfig();

          Logger.log('✅ تم تحميل إعدادات التحكم عن بعد بنجاح');
          Logger.log('⏱️ وقت آخر تحديث: ${_lastUpdateTime.toIso8601String()}');

          // التحقق من التحديثات
          _checkForUpdates();

          // التحقق مما إذا كانت معرفات الإعلانات قد تغيرت
          bool adsIdsChanged = _checkIfAdsIdsChanged(oldConfig);

          // إشعار المستمعين بالتغييرات إذا تغيرت معرفات الإعلانات
          if (adsIdsChanged) {
            Logger.log(
              '🔔 تم تحديث معرفات الإعلانات - إشعار المستمعين بالتغييرات',
            );
            _notifyListeners();
          }

          // تحديث عنوان URL للتكوين
          _configUrl = url;

          // طباعة معلومات تفصيلية عن التغييرات
          _logDetailedChanges(oldConfig);

          // طباعة حالة التكوين للتشخيص
          logConfigStatus();

          // طباعة معلومات معرفات الإعلانات للتشخيص
          if (_config != null) {
            // طباعة حالة تفعيل الإعلانات
            if (!_config!.showBannerAds) {
              Logger.log('ℹ️ تم تعطيل إعلانات البانر من التحكم عن بعد');
            }
            if (!_config!.showInterstitialAds) {
              Logger.log('ℹ️ تم تعطيل الإعلانات البينية من التحكم عن بعد');
            }
            if (!_config!.showRewardedAds) {
              Logger.log('ℹ️ تم تعطيل إعلانات المكافأة من التحكم عن بعد');
            }

            // معرفات الإعلانات ثابتة في الكود ولا تأتي من الخادم
            Logger.log('📋 معرفات الإعلانات: تستخدم المعرفات الثابتة في الكود');

            // لا نطبع معرفات الإعلانات لأنها لا تُستخدم من الخادم
          }
        }
      } else {
        Logger.log(
          '❌ فشل في تحميل إعدادات التحكم عن بعد: ${response.statusCode}',
        );
      }
    } catch (e) {
      Logger.log('❌ خطأ في تحميل إعدادات التحكم عن بعد: $e');
    }
  }

  /// تحويل البيانات من الصيغة القديمة إلى الصيغة الجديدة
  Map<String, dynamic> _convertToConfigFormat(Map<String, dynamic> oldFormat) {
    // طباعة البيانات الأصلية للتشخيص
    Logger.log('📋 البيانات الأصلية من التحكم عن بعد:');
    oldFormat.forEach((key, value) {
      Logger.log('📋 $key: $value');
    });

    return {
      'app_version': oldFormat['Application_release'] ?? '1.0.0',
      'show_banner_ads': oldFormat['Alan_Albanar'] ?? 'ON',
      'show_interstitial_ads': oldFormat['Alan_Bini'] ?? 'ON',
      'show_rewarded_ads': oldFormat['Announce_reward'] ?? 'ON',
      'message': oldFormat['message'] ?? '',
      'force_update': oldFormat['force_update'] ?? 'OFF',
      'update_url':
          oldFormat['update_url'] ??
          'https://play.google.com/store/apps/details?id=com.islamiclandporta.islam.allahwallpaper.ahmad.np',
      'additional_settings': oldFormat,
    };
  }

  /// حفظ التكوين محليًا
  Future<void> _cacheConfig() async {
    if (_config == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_configKey, jsonEncode(_config!.toJson()));
      await prefs.setInt(
        _configTimestampKey,
        DateTime.now().millisecondsSinceEpoch,
      );

      Logger.log('✅ تم حفظ إعدادات التحكم عن بعد محليًا');
    } catch (e) {
      Logger.log('❌ خطأ في حفظ إعدادات التحكم عن بعد محليًا: $e');
    }
  }

  /// تحميل التكوين المخزن محليًا
  Future<void> _loadCachedConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // التحقق من وجود تكوين مخزن
      if (!prefs.containsKey(_configKey) ||
          !prefs.containsKey(_configTimestampKey)) {
        Logger.log('ℹ️ لا يوجد إعدادات تحكم عن بعد مخزنة محليًا');
        _config = RemoteConfig.defaultConfig();
        return;
      }

      // التحقق من صلاحية التكوين المخزن
      final timestamp = prefs.getInt(_configTimestampKey)!;
      final now = DateTime.now().millisecondsSinceEpoch;

      if (now - timestamp > _cacheDuration.inMilliseconds) {
        Logger.log('ℹ️ إعدادات التحكم عن بعد المخزنة منتهية الصلاحية');
        _config = RemoteConfig.defaultConfig();
        return;
      }

      // تحميل التكوين المخزن
      final configJson = prefs.getString(_configKey)!;
      _config = RemoteConfig.fromJson(jsonDecode(configJson));

      Logger.log('✅ تم تحميل إعدادات التحكم عن بعد من التخزين المحلي');
    } catch (e) {
      Logger.log('❌ خطأ في تحميل إعدادات التحكم عن بعد من التخزين المحلي: $e');
      _config = RemoteConfig.defaultConfig();
    }
  }

  /// التحقق من التحديثات
  void _checkForUpdates() {
    if (_config == null || _currentVersion.isEmpty) return;

    try {
      final currentVersionParts = _currentVersion.split('.');
      final remoteVersionParts = _config!.appVersion.split('.');

      // مقارنة الإصدارات
      bool needsUpdate = false;

      for (
        int i = 0;
        i < math.min(currentVersionParts.length, remoteVersionParts.length);
        i++
      ) {
        final currentPart = int.tryParse(currentVersionParts[i]) ?? 0;
        final remotePart = int.tryParse(remoteVersionParts[i]) ?? 0;

        if (remotePart > currentPart) {
          needsUpdate = true;
          break;
        } else if (remotePart < currentPart) {
          break;
        }
      }

      if (needsUpdate) {
        Logger.log('🔄 يوجد تحديث جديد متاح: ${_config!.appVersion}');
      } else {
        Logger.log('✅ التطبيق محدث: $_currentVersion');
      }
    } catch (e) {
      Logger.log('❌ خطأ في التحقق من التحديثات: $e');
    }
  }

  /// التحقق مما إذا كان التحديث مطلوبًا
  bool _isUpdateRequired() {
    if (_config == null || _currentVersion.isEmpty) return false;

    try {
      final currentVersionParts = _currentVersion.split('.');
      final remoteVersionParts = _config!.appVersion.split('.');

      // مقارنة الإصدارات
      for (
        int i = 0;
        i < math.min(currentVersionParts.length, remoteVersionParts.length);
        i++
      ) {
        final currentPart = int.tryParse(currentVersionParts[i]) ?? 0;
        final remotePart = int.tryParse(remoteVersionParts[i]) ?? 0;

        if (remotePart > currentPart) {
          return true;
        } else if (remotePart < currentPart) {
          return false;
        }
      }

      return false;
    } catch (e) {
      Logger.log('❌ خطأ في التحقق من التحديثات: $e');
      return false;
    }
  }

  /// إيقاف خدمة التحكم عن بعد
  void dispose() {
    // إيقاف المؤقت
    if (_refreshTimer != null) {
      _refreshTimer!.cancel();
      _refreshTimer = null;
      Logger.log('✅ تم إيقاف التحقق الدوري من التغييرات');
    }

    // إزالة جميع المستمعين
    _listeners.clear();
  }
}
