import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// خدمة الترجمة للتطبيق
class AppLocalizations {
  final Locale locale;
  Map<String, String> _localizedStrings = {};
  static final Map<String, Map<String, String>> _cachedStrings = {};

  AppLocalizations(this.locale);

  // مساعد للحصول على مثيل من الخدمة
  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  // مساعد للتحقق من وجود ترجمة للغة معينة
  static bool isSupported(Locale locale) {
    return ['ar'].contains(locale.languageCode);
  }

  // تحميل ملفات الترجمة
  Future<bool> load() async {
    try {
      // التحقق من وجود الترجمات في الذاكرة المؤقتة
      if (_cachedStrings.containsKey(locale.languageCode)) {
        _localizedStrings = _cachedStrings[locale.languageCode]!;
        debugPrint(
          '✅ تم تحميل ترجمات ${locale.languageCode} من الذاكرة المؤقتة',
        );
        return true;
      }

      // تحميل ملف JSON للغة المحددة
      String jsonString = await rootBundle.loadString(
        'assets/localization/${locale.languageCode}.json',
      );
      Map<String, dynamic> jsonMap = json.decode(jsonString);

      _localizedStrings = jsonMap.map((key, value) {
        return MapEntry(key, value.toString());
      });

      // حفظ الترجمات في الذاكرة المؤقتة
      _cachedStrings[locale.languageCode] = _localizedStrings;
      debugPrint('✅ تم تحميل ترجمات ${locale.languageCode} من الملف');

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تحميل ملف اللغة: $e');
      // استخدام قيم افتراضية في حالة عدم وجود ملف اللغة
      _localizedStrings = {
        'app_name': 'خلفيات إسلامية',
        'settings': 'الإعدادات',
        'language': 'اللغة',
        'dark_mode': 'الوضع الداكن',
      };
      return false;
    }
  }

  // الحصول على النص المترجم
  String translate(String key) {
    return _localizedStrings[key] ?? key;
  }

  // اختصار للحصول على النص المترجم
  String get(String key) => translate(key);
}

/// مندوب الترجمة
class AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return AppLocalizations.isSupported(locale);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(AppLocalizationsDelegate old) => false;
}
