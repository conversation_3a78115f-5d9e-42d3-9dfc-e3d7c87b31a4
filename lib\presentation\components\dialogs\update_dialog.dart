import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../../services/remote_config_service.dart';
import '../../../utils/logger.dart';

/// مربع حوار التحديث
class UpdateDialog extends StatelessWidget {
  final String currentVersion;
  final String newVersion;
  final String updateUrl;
  final bool forceUpdate;
  final VoidCallback? onLater;

  const UpdateDialog({
    Key? key,
    required this.currentVersion,
    required this.newVersion,
    required this.updateUrl,
    this.forceUpdate = false,
    this.onLater,
  }) : super(key: key);

  /// عرض مربع حوار التحديث
  static Future<void> show(
    BuildContext context, {
    bool checkRemoteConfig = true,
  }) async {
    if (checkRemoteConfig) {
      final remoteConfig = RemoteConfigService();
      if (!remoteConfig.isInitialized || !remoteConfig.needsUpdate) {
        return;
      }

      return showDialog(
        context: context,
        barrierDismissible: !remoteConfig.config.forceUpdate,
        builder:
            (context) => UpdateDialog(
              currentVersion: remoteConfig.currentVersion,
              newVersion: remoteConfig.config.appVersion,
              updateUrl: remoteConfig.config.updateUrl,
              forceUpdate: remoteConfig.config.forceUpdate,
            ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !forceUpdate,
      child: Dialog(
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppDimensions.paddingLarge),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(100),
                blurRadius: 16,
                spreadRadius: 2,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة التحديث
              Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: AppColors.primary.withAlpha(30),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.system_update_outlined,
                      color: AppColors.primary,
                      size: 40,
                    ),
                  )
                  .animate()
                  .scale(
                    duration: const Duration(milliseconds: 500),
                    curve: Curves.easeOutBack,
                  )
                  .then()
                  .shimmer(
                    duration: const Duration(seconds: 2),
                    color: AppColors.primary.withAlpha(100),
                  ),

              const SizedBox(height: AppDimensions.marginLarge),

              // عنوان التحديث
              Text(
                'تحديث جديد متاح',
                style: AppTypography.titleLarge.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppDimensions.marginMedium),

              // تفاصيل التحديث
              Text(
                'هناك إصدار جديد من التطبيق متاح الآن. يرجى تحديث التطبيق للاستمتاع بأحدث الميزات والتحسينات.',
                style: AppTypography.bodyMedium,
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: AppDimensions.marginMedium),

              // معلومات الإصدار
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingMedium),
                decoration: BoxDecoration(
                  color: AppColors.card,
                  borderRadius: BorderRadius.circular(
                    AppDimensions.radiusMedium,
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildVersionInfo('الإصدار الحالي', currentVersion),
                    Container(height: 40, width: 1, color: AppColors.divider),
                    _buildVersionInfo('الإصدار الجديد', newVersion),
                  ],
                ),
              ),

              const SizedBox(height: AppDimensions.marginLarge),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // زر التحديث الآن
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _launchUpdateUrl,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          vertical: AppDimensions.paddingMedium,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            AppDimensions.radiusMedium,
                          ),
                        ),
                      ),
                      child: const Text('تحديث الآن'),
                    ),
                  ),

                  // زر لاحقًا (إذا لم يكن التحديث إجباريًا)
                  if (!forceUpdate) ...[
                    const SizedBox(width: AppDimensions.marginMedium),
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          if (onLater != null) {
                            onLater!();
                          }
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: AppColors.textSecondary,
                          side: BorderSide(color: AppColors.divider),
                          padding: const EdgeInsets.symmetric(
                            vertical: AppDimensions.paddingMedium,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(
                              AppDimensions.radiusMedium,
                            ),
                          ),
                        ),
                        child: const Text('لاحقًا'),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء معلومات الإصدار
  Widget _buildVersionInfo(String title, String version) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          title,
          style: AppTypography.labelMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          version,
          style: AppTypography.titleSmall.copyWith(fontWeight: FontWeight.bold),
        ),
      ],
    );
  }

  /// فتح رابط التحديث
  Future<void> _launchUpdateUrl() async {
    try {
      final Uri url = Uri.parse(updateUrl);
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } catch (e) {
      Logger.log('❌ خطأ في فتح رابط التحديث: $e');
    }
  }
}
