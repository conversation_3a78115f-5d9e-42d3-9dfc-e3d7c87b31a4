import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'dart:math' as math;
import '../../design_system/colors.dart';
import '../../design_system/typography.dart';
import '../../design_system/dimensions.dart';
import '../../data/models/wallpaper.dart';
import '../../data/services/wallpaper_service.dart';
import '../../services/remote_config_service.dart';
import '../../services/ads_manager.dart';
import '../components/navigation/dark_app_header.dart';
import '../components/cards/wallpaper_card.dart';
import '../components/loaders/shimmer_loading.dart';
import '../components/ads/ad_banner.dart';

/// شاشة عرض جميع الخلفيات
class AllWallpapersScreen extends StatefulWidget {
  const AllWallpapersScreen({super.key});

  @override
  State<AllWallpapersScreen> createState() => _AllWallpapersScreenState();
}

class _AllWallpapersScreenState extends State<AllWallpapersScreen> {
  final WallpaperService _wallpaperService = WallpaperService();
  final ScrollController _scrollController = ScrollController();
  final RemoteConfigService _remoteConfigService = RemoteConfigService();
  final AdsManager _adsManager = AdsManager();

  List<Wallpaper> _wallpapers = [];
  bool _isLoading = true;
  bool _isLoadingMore = false;
  bool _hasMoreWallpapers = true;
  String _error = '';
  int _totalWallpapers = 0;

  @override
  void initState() {
    super.initState();
    _initServices();
    _loadWallpapers();
    _scrollController.addListener(_onScroll);
  }

  /// تهيئة الخدمات
  Future<void> _initServices() async {
    try {
      // تهيئة خدمة التحكم عن بعد
      await _remoteConfigService.initialize();

      // تحديث التكوين من الخادم
      await _remoteConfigService.fetchConfig(
        "https://drive.google.com/uc?id=1mut1fYcZMmVFFBuFKjmTEwb-ds6KUnZM",
      );

      // تهيئة مدير الإعلانات
      await _adsManager.initialize();

      // تحديث الإعلانات من التحكم عن بعد
      await _adsManager.updateFromRemoteConfig(
        "https://drive.google.com/uc?id=1mut1fYcZMmVFFBuFKjmTEwb-ds6KUnZM",
      );
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمات: $e');
    }
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    // Verificar si el controlador de desplazamiento está adjunto
    if (!_scrollController.hasClients) return;

    // Calcular la posición de desplazamiento
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.position.pixels;
    final threshold =
        maxScroll * 0.8; // Cargar más cuando llegue al 80% del desplazamiento

    // Verificar si debemos cargar más elementos
    if (currentScroll >= threshold &&
        !_isLoading &&
        !_isLoadingMore &&
        _hasMoreWallpapers) {
      // Evitar cargas múltiples
      if (_wallpapers.isEmpty) return;

      // Cargar más elementos
      _loadMoreWallpapers();
    }
  }

  Future<void> _loadWallpapers() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      final wallpapers = await _wallpaperService.fetchWallpapers();

      if (!mounted) return;

      // Eliminar duplicados antes de actualizar el estado
      final uniqueWallpapers = <Wallpaper>[];
      final seenIds = <String>{};

      for (final wallpaper in wallpapers) {
        if (!seenIds.contains(wallpaper.id)) {
          uniqueWallpapers.add(wallpaper);
          seenIds.add(wallpaper.id);
        }
      }

      setState(() {
        _wallpapers = uniqueWallpapers;
        _totalWallpapers = uniqueWallpapers.length;
        _isLoading = false;
        _hasMoreWallpapers = true; // Reiniciar el estado de carga
      });

      // Precargar las primeras imágenes para mejorar el rendimiento
      for (int i = 0; i < math.min(6, uniqueWallpapers.length); i++) {
        final wallpaper = uniqueWallpapers[i];
        if (wallpaper.thumbnailUrl.isNotEmpty && mounted) {
          precacheImage(
            CachedNetworkImageProvider(wallpaper.thumbnailUrl),
            context,
          );
        }
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _error = 'حدث خطأ أثناء تحميل الخلفيات';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreWallpapers() async {
    // Evitar cargas múltiples o innecesarias
    if (_isLoadingMore || !_hasMoreWallpapers) return;

    // Actualizar el estado para mostrar el indicador de carga
    setState(() {
      _isLoadingMore = true;
    });

    try {
      // Calcular la página actual basada en el número de elementos ya cargados
      final currentPage = (_wallpapers.length ~/ 10) + 1;

      // Obtener los IDs de los fondos de pantalla actuales para evitar duplicados
      final existingIds = _wallpapers.map((w) => w.id).toSet();

      // Cargar más elementos con un límite fijo
      final moreWallpapers = await WallpaperService.getWallpapers(
        page: currentPage,
        limit: 10,
      );

      // Verificar si hemos llegado al final de la lista
      if (moreWallpapers.isEmpty) {
        setState(() {
          _hasMoreWallpapers = false;
          _isLoadingMore = false;
        });

        // Mostrar mensaje al usuario
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('لقد وصلت إلى نهاية القائمة'),
              duration: const Duration(seconds: 2),
              backgroundColor: AppColors.success,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          );
        }
      } else {
        // Filtrar para eliminar duplicados
        final uniqueWallpapers =
            moreWallpapers
                .where((wallpaper) => !existingIds.contains(wallpaper.id))
                .toList();

        if (uniqueWallpapers.isEmpty) {
          // Si todos son duplicados, marcar que no hay más fondos
          setState(() {
            _hasMoreWallpapers = false;
            _isLoadingMore = false;
          });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('لقد وصلت إلى نهاية القائمة'),
                duration: const Duration(seconds: 2),
                backgroundColor: AppColors.success,
                behavior: SnackBarBehavior.floating,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            );
          }
          return;
        }

        // Agregar los nuevos elementos únicos a la lista
        if (mounted) {
          setState(() {
            _wallpapers.addAll(uniqueWallpapers);
            _totalWallpapers = _wallpapers.length;
            _isLoadingMore = false;
          });

          // Precargar imágenes para mejorar el rendimiento
          for (final wallpaper in uniqueWallpapers) {
            if (wallpaper.thumbnailUrl.isNotEmpty) {
              precacheImage(
                CachedNetworkImageProvider(wallpaper.thumbnailUrl),
                context,
              );
            }
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحميل المزيد من الخلفيات: $e');

      // Actualizar el estado en caso de error
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });

        // Mostrar mensaje de error al usuario
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحميل المزيد من الخلفيات: $e'),
            duration: const Duration(seconds: 3),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: Column(
          children: [
            // رأس الصفحة الثابت - خارج CustomScrollView لجعله ثابتًا تمامًا
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [AppColors.surface, AppColors.surfaceLight],
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(80),
                    blurRadius: 8,
                    spreadRadius: 0.5,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: SafeArea(
                bottom: false,
                child: Column(
                  children: [
                    // عنوان الصفحة
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 12, 16, 8),
                      child: Row(
                        children: [
                          // زر الرجوع
                          GestureDetector(
                            onTap: () => context.pop(),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    AppColors.accent.withAlpha(100),
                                    AppColors.accentLight.withAlpha(100),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(12),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(30),
                                    blurRadius: 4,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: const Icon(
                                Icons.arrow_back_ios_new,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          // عنوان ووصف
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'جميع الخلفيات',
                                  style: AppTypography.titleLarge.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'استكشف مجموعتنا الكاملة من الخلفيات',
                                  style: AppTypography.bodyMedium.copyWith(
                                    color: Colors.white.withAlpha(
                                      230,
                                    ), // 0.9 * 255 = 230
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // معلومات الخلفيات
                    Padding(
                      padding: const EdgeInsets.fromLTRB(16, 8, 16, 12),
                      child: Row(
                        children: [
                          // عدد الخلفيات
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.accentDark,
                                  AppColors.accent,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.accent.withAlpha(40),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.photo_library_rounded,
                                  size: 18,
                                  color: Colors.black87,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  '$_totalWallpapers خلفية',
                                  style: AppTypography.titleMedium.copyWith(
                                    color: Colors.black87,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Spacer(),
                          // ترتيب الخلفيات
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  AppColors.secondaryDark.withAlpha(180),
                                  AppColors.secondary.withAlpha(180),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withAlpha(40),
                                  blurRadius: 6,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.sort,
                                  size: 18,
                                  color: Colors.white,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  'الأحدث أولاً',
                                  style: AppTypography.labelMedium.copyWith(
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // محتوى الصفحة القابل للتمرير
            Expanded(
              child: RefreshIndicator(
                onRefresh: _loadWallpapers,
                color: AppColors.accent,
                child:
                    _error.isNotEmpty
                        ? _buildErrorState()
                        : CustomScrollView(
                          controller: _scrollController,
                          physics: const BouncingScrollPhysics(
                            parent: AlwaysScrollableScrollPhysics(),
                          ),
                          slivers: [
                            // شبكة الخلفيات
                            _isLoading
                                ? _buildLoadingGrid()
                                : _buildWallpapersGrid(),

                            // مؤشر تحميل المزيد
                            if (_isLoadingMore)
                              const SliverToBoxAdapter(
                                child: Padding(
                                  padding: EdgeInsets.only(bottom: 32, top: 16),
                                  child: Center(
                                    child: CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        AppColors.accent,
                                      ),
                                    ),
                                  ),
                                ),
                              ),

                            // مساحة إضافية في الأسفل لتجنب تداخل شريط التنقل
                            const SliverToBoxAdapter(
                              child: SizedBox(height: 80),
                            ),
                          ],
                        ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return CustomScrollView(
      physics: const NeverScrollableScrollPhysics(),
      slivers: [
        // رأس الصفحة الثابت
        SliverPersistentHeader(
          delegate: DarkStickyHeader(
            title: 'جميع الخلفيات',
            subtitle: 'استكشف مجموعتنا الكاملة من الخلفيات',
            accentColor: AppColors.accent,
            showBackButton: true,
            onBackPressed: () => context.pop(),
          ),
          pinned: true,
        ),

        // رسالة الخطأ
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: AppColors.error,
                  size: 64,
                ),
                const SizedBox(height: 16),
                Text(
                  _error,
                  style: AppTypography.titleMedium.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: _loadWallpapers,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.accent,
                    foregroundColor: Colors.black,
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingLarge,
                      vertical: AppDimensions.paddingMedium,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusMedium,
                      ),
                    ),
                  ),
                  child: const Text('إعادة المحاولة'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  SliverToBoxAdapter _buildLoadingGrid() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // إعلان بانر - يتم التحكم فيه من خلال التحكم عن بعد
            if (_remoteConfigService.isInitialized &&
                _remoteConfigService.config.showBannerAds)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: SizedBox(
                  height: 60,
                  child:
                      _adsManager.getBannerAdWidget() ?? AdBanner(height: 60),
                ),
              ),

            // شبكة تحميل
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                childAspectRatio: 0.75,
              ),
              itemCount: 6,
              itemBuilder: (context, index) {
                return const WallpaperCardShimmer(
                  height: 250,
                  borderRadius: 16,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWallpapersGrid() {
    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverToBoxAdapter(
        child: Column(
          children: [
            // إعلان بانر - يتم التحكم فيه من خلال التحكم عن بعد
            if (_remoteConfigService.isInitialized &&
                _remoteConfigService.config.showBannerAds)
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: SizedBox(
                  height: 60,
                  child:
                      _adsManager.getBannerAdWidget() ?? AdBanner(height: 60),
                ),
              ),

            // شبكة الخلفيات - تم تحسينها لتجنب مشاكل التمرير والتكرار
            Builder(
              builder: (context) {
                // إزالة الخلفيات المكررة
                final uniqueWallpapers = <Wallpaper>[];
                final seenIds = <String>{};

                for (final wallpaper in _wallpapers) {
                  if (!seenIds.contains(wallpaper.id)) {
                    uniqueWallpapers.add(wallpaper);
                    seenIds.add(wallpaper.id);
                  }
                }

                return MasonryGridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                  itemCount: uniqueWallpapers.length,
                  cacheExtent: 1000, // تخزين مؤقت أكبر لتحسين الأداء
                  itemBuilder: (context, index) {
                    // التحقق من صحة الفهرس لتجنب الأخطاء
                    if (index >= uniqueWallpapers.length) {
                      return const SizedBox.shrink();
                    }

                    final wallpaper = uniqueWallpapers[index];

                    // تحميل مسبق للصورة لتحسين الأداء
                    if (wallpaper.thumbnailUrl.isNotEmpty) {
                      precacheImage(
                        CachedNetworkImageProvider(wallpaper.thumbnailUrl),
                        context,
                      );
                    }

                    return WallpaperCard(
                      wallpaper: wallpaper,
                      borderRadius: 16,
                      showInfo: false,
                      onTap: () {
                        context.push(
                          '/wallpaper/${wallpaper.id}',
                          extra: wallpaper,
                        );
                      },
                    );
                  },
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
