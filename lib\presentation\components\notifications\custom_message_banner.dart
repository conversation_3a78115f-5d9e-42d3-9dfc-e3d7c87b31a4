import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../../services/remote_config_service.dart';

/// شريط الرسائل المخصصة
class CustomMessageBanner extends StatelessWidget {
  final String message;
  final VoidCallback? onDismiss;
  final VoidCallback? onTap;
  final Color backgroundColor;
  final Color textColor;
  final IconData icon;

  const CustomMessageBanner({
    Key? key,
    required this.message,
    this.onDismiss,
    this.onTap,
    this.backgroundColor = AppColors.accent,
    this.textColor = Colors.black,
    this.icon = Icons.info_outline,
  }) : super(key: key);

  /// إنشاء شريط رسائل من التحكم عن بعد
  static Widget? fromRemoteConfig({
    VoidCallback? onDismiss,
    VoidCallback? onTap,
  }) {
    final remoteConfig = RemoteConfigService();
    if (!remoteConfig.isInitialized || remoteConfig.config.message.isEmpty) {
      return null;
    }

    return CustomMessageBanner(
      message: remoteConfig.config.message,
      onDismiss: onDismiss,
      onTap: onTap,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingMedium,
          vertical: AppDimensions.paddingSmall,
        ),
        decoration: BoxDecoration(
          color: backgroundColor,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(50),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: textColor,
              size: 20,
            ),
            const SizedBox(width: AppDimensions.marginSmall),
            Expanded(
              child: Text(
                message,
                style: AppTypography.bodySmall.copyWith(
                  color: textColor,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (onDismiss != null)
              IconButton(
                icon: Icon(
                  Icons.close,
                  color: textColor,
                  size: 16,
                ),
                onPressed: onDismiss,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(
                  minWidth: 24,
                  minHeight: 24,
                ),
              ),
          ],
        ),
      )
          .animate()
          .fadeIn(duration: const Duration(milliseconds: 500))
          .slideY(
            begin: -1,
            end: 0,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeOutBack,
          ),
    );
  }
}
