import 'dart:async';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../utils/logger.dart';
import 'remote_config_service.dart';

/// مدير الإعلانات المتكامل مع التحكم عن بعد
class AdsManager {
  static final AdsManager _instance = AdsManager._internal();
  factory AdsManager() => _instance;
  AdsManager._internal();

  // الخدمات
  final RemoteConfigService _remoteConfigService = RemoteConfigService();

  // معرفات الإعلانات الثابتة - لا تتغير من الخادم
  static const String _bannerAdUnitId = 'ca-app-pub-9278102953647270/3778249828';
  static const String _interstitialAdUnitId = 'ca-app-pub-9278102953647270/3151563865';
  static const String _rewardedAdUnitId = 'ca-app-pub-9278102953647270/3416924224';

  // حالة الإعلانات
  BannerAd? _bannerAd;
  InterstitialAd? _interstitialAd;
  RewardedAd? _rewardedAd;
  bool _isInterstitialAdReady = false;
  bool _isRewardedAdReady = false;
  bool _isInitialized = false;

  // الحصول على حالة الإعلانات
  bool get isInitialized => _isInitialized;
  bool get isBannerAdReady =>
      _bannerAd != null && _remoteConfigService.config.showBannerAds;
  bool get isInterstitialAdReady =>
      _isInterstitialAdReady && _remoteConfigService.config.showInterstitialAds;
  bool get isRewardedAdReady =>
      _isRewardedAdReady && _remoteConfigService.config.showRewardedAds;

  /// الحصول على حالة الإعلانات
  Map<String, dynamic> getAdsStatus() {
    return {
      'isInitialized': _isInitialized,
      'isBannerAdReady': isBannerAdReady,
      'isInterstitialAdReady': isInterstitialAdReady,
      'isRewardedAdReady': isRewardedAdReady,
      'bannerAdId': _bannerAdUnitId,
      'interstitialAdId': _interstitialAdUnitId,
      'rewardedAdId': _rewardedAdUnitId,
      'appId': 'ca-app-pub-9278102953647270~6028576475', // معرف ثابت
      'showBannerAds': _remoteConfigService.config.showBannerAds,
      'showInterstitialAds': _remoteConfigService.config.showInterstitialAds,
      'showRewardedAds': _remoteConfigService.config.showRewardedAds,
    };
  }

  /// الحصول على حالة التكوين عن بعد
  Map<String, dynamic> getRemoteConfigStatus() {
    return _remoteConfigService.getConfigStatus();
  }

  /// طباعة حالة الإعلانات للتشخيص
  void logAdStatus() {
    Logger.log('📊 حالة الإعلانات:');
    Logger.log('📊 تم التهيئة: ${isInitialized ? "نعم" : "لا"}');
    Logger.log('📊 إعلان البانر جاهز: ${isBannerAdReady ? "نعم" : "لا"}');
    Logger.log(
      '📊 الإعلان البيني جاهز: ${isInterstitialAdReady ? "نعم" : "لا"}',
    );
    Logger.log('📊 إعلان المكافأة جاهز: ${isRewardedAdReady ? "نعم" : "لا"}');
    Logger.log('📊 معرف تطبيق AdMob: ca-app-pub-9278102953647270~6028576475');
    Logger.log('📊 معرف إعلان البانر: $_bannerAdUnitId');
    Logger.log('📊 معرف الإعلان البيني: $_interstitialAdUnitId');
    Logger.log('📊 معرف إعلان المكافأة: $_rewardedAdUnitId');
    Logger.log(
      '📊 عرض إعلانات البانر: ${_remoteConfigService.config.showBannerAds ? "مفعل" : "معطل"}',
    );
    Logger.log(
      '📊 عرض الإعلانات البينية: ${_remoteConfigService.config.showInterstitialAds ? "مفعل" : "معطل"}',
    );
    Logger.log(
      '📊 عرض إعلانات المكافأة: ${_remoteConfigService.config.showRewardedAds ? "مفعل" : "معطل"}',
    );
    Logger.log(
      '📊 آخر تحديث للتكوين: ${_remoteConfigService.lastUpdateTime.toIso8601String()}',
    );
  }

  /// تهيئة مدير الإعلانات
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة خدمة التحكم عن بعد
      await _remoteConfigService.initialize();

      // محاولة تحديث التكوين من الخادم
      try {
        await _remoteConfigService.fetchConfig(
          "https://drive.google.com/uc?id=1mut1fYcZMmVFFBuFKjmTEwb-ds6KUnZM",
        );
      } catch (e) {
        Logger.log('⚠️ تعذر تحديث التكوين من الخادم: $e');
        // نستمر باستخدام القيم الافتراضية
      }

      // طباعة معرفات الإعلانات الثابتة المستخدمة
      Logger.log('📢 معرف إعلان البانر الثابت: $_bannerAdUnitId');
      Logger.log('📢 معرف الإعلان البيني الثابت: $_interstitialAdUnitId');
      Logger.log('📢 معرف إعلان المكافأة الثابت: $_rewardedAdUnitId');
      Logger.log('📢 معرف تطبيق AdMob الثابت: ca-app-pub-9278102953647270~6028576475');

      // إضافة مستمع للتغييرات في التحكم عن بعد
      _remoteConfigService.addListener(_onRemoteConfigChanged);

      // تحميل الإعلانات
      _loadBannerAd();
      _loadInterstitialAd();
      _loadRewardedAd();

      _isInitialized = true;
      Logger.log('✅ تم تهيئة مدير الإعلانات بنجاح');

      // طباعة حالة الإعلانات للتشخيص
      logAdStatus();

      // طباعة حالة التكوين للتشخيص
      _remoteConfigService.logConfigStatus();
    } catch (e) {
      Logger.log('❌ خطأ في تهيئة مدير الإعلانات: $e');
    }
  }

  /// معالجة التغييرات في التحكم عن بعد
  void _onRemoteConfigChanged() async {
    try {
      Logger.log('🔔 تم تغيير إعدادات التحكم عن بعد، جاري تحديث الإعلانات...');

      // طباعة وقت التغيير
      Logger.log('⏱️ وقت التغيير: ${DateTime.now().toIso8601String()}');

      // طباعة معرفات الإعلانات الثابتة المستخدمة
      Logger.log('📋 معرفات الإعلانات الثابتة المستخدمة:');
      Logger.log('📋 معرف تطبيق AdMob: ca-app-pub-9278102953647270~6028576475');
      Logger.log('📋 معرف إعلان البانر: $_bannerAdUnitId');
      Logger.log('📋 معرف الإعلان البيني: $_interstitialAdUnitId');
      Logger.log('📋 معرف إعلان المكافأة: $_rewardedAdUnitId');

      // إعادة تحميل الإعلانات من التحكم عن بعد
      await updateFromRemoteConfig(
        "https://drive.google.com/uc?id=1mut1fYcZMmVFFBuFKjmTEwb-ds6KUnZM",
      );

      Logger.log('✅ تم تحديث الإعلانات بنجاح بعد تغيير إعدادات التحكم عن بعد');

      // طباعة حالة الإعلانات للتشخيص
      logAdStatus();

      // طباعة حالة التكوين للتشخيص
      _remoteConfigService.logConfigStatus();
    } catch (e) {
      Logger.log('❌ خطأ في معالجة تغييرات التحكم عن بعد: $e');
    }
  }

  /// تحميل إعلان البانر
  void _loadBannerAd() {
    // التحقق من تفعيل الإعلانات من التحكم عن بعد
    if (!_remoteConfigService.config.showBannerAds) {
      Logger.log('ℹ️ تم تعطيل إعلانات البانر من التحكم عن بعد');
      return;
    }

    // التحقق من وجود معرف صالح
    final String adUnitId = _bannerAdUnitId;
    if (adUnitId.isEmpty) {
      Logger.log('⛔ معرف إعلان البانر فارغ، تم تعطيل إعلانات البانر');
      return;
    }

    _bannerAd = BannerAd(
      adUnitId: adUnitId,
      size: AdSize.banner,
      request: const AdRequest(),
      listener: BannerAdListener(
        onAdLoaded: (ad) {
          Logger.log('✅ تم تحميل إعلان البانر بنجاح');
        },
        onAdFailedToLoad: (ad, error) {
          Logger.log('❌ فشل في تحميل إعلان البانر: ${error.message}');
          ad.dispose();
          _bannerAd = null;

          // إذا كان الخطأ متعلق بمعرف الإعلان، نعطل الإعلانات
          if (error.code == 1 || // Invalid request
              error.code == 3) {
            // No fill
            Logger.log('⚠️ معرف إعلان البانر غير صالح أو لا يوجد إعلان متاح');
          } else {
            // إعادة المحاولة بعد فترة
            Future.delayed(const Duration(minutes: 1), _loadBannerAd);
          }
        },
      ),
    );

    _bannerAd?.load();
  }

  /// تحميل إعلان بيني
  void _loadInterstitialAd() {
    // التحقق من تفعيل الإعلانات من التحكم عن بعد
    if (!_remoteConfigService.config.showInterstitialAds) {
      Logger.log('ℹ️ تم تعطيل الإعلانات البينية من التحكم عن بعد');
      return;
    }

    // التحقق من وجود معرف صالح
    final String adUnitId = _interstitialAdUnitId;
    if (adUnitId.isEmpty) {
      Logger.log('⛔ معرف الإعلان البيني فارغ، تم تعطيل الإعلانات البينية');
      return;
    }

    InterstitialAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      adLoadCallback: InterstitialAdLoadCallback(
        onAdLoaded: (ad) {
          _interstitialAd = ad;
          _isInterstitialAdReady = true;
          Logger.log('✅ تم تحميل الإعلان البيني بنجاح');

          // إعداد مستمع للإغلاق
          _interstitialAd
              ?.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (ad) {
              Logger.log('ℹ️ تم إغلاق الإعلان البيني');
              ad.dispose();
              _isInterstitialAdReady = false;
              _loadInterstitialAd(); // إعادة تحميل الإعلان
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              Logger.log('❌ فشل في عرض الإعلان البيني: ${error.message}');
              ad.dispose();
              _isInterstitialAdReady = false;
              _loadInterstitialAd(); // إعادة تحميل الإعلان
            },
          );
        },
        onAdFailedToLoad: (error) {
          Logger.log('❌ فشل في تحميل الإعلان البيني: ${error.message}');
          _isInterstitialAdReady = false;

          // إذا كان الخطأ متعلق بمعرف الإعلان، نعطل الإعلانات
          if (error.code == 1 || // Invalid request
              error.code == 3) {
            // No fill
            Logger.log('⚠️ معرف الإعلان البيني غير صالح أو لا يوجد إعلان متاح');
          } else {
            // إعادة المحاولة بعد فترة
            Future.delayed(const Duration(minutes: 1), _loadInterstitialAd);
          }
        },
      ),
    );
  }

  /// تحميل إعلان المكافأة
  void _loadRewardedAd() {
    // التحقق من تفعيل الإعلانات من التحكم عن بعد
    if (!_remoteConfigService.config.showRewardedAds) {
      Logger.log('ℹ️ تم تعطيل إعلانات المكافأة من التحكم عن بعد');
      return;
    }

    // التحقق من وجود معرف صالح
    final String adUnitId = _rewardedAdUnitId;
    if (adUnitId.isEmpty) {
      Logger.log('⛔ معرف إعلان المكافأة فارغ، تم تعطيل إعلانات المكافأة');
      return;
    }

    RewardedAd.load(
      adUnitId: adUnitId,
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (ad) {
          _rewardedAd = ad;
          _isRewardedAdReady = true;
          Logger.log('✅ تم تحميل إعلان المكافأة بنجاح');

          // إعداد مستمع للإغلاق
          _rewardedAd?.fullScreenContentCallback = FullScreenContentCallback(
            onAdDismissedFullScreenContent: (ad) {
              Logger.log('ℹ️ تم إغلاق إعلان المكافأة');
              ad.dispose();
              _isRewardedAdReady = false;
              _loadRewardedAd(); // إعادة تحميل الإعلان
            },
            onAdFailedToShowFullScreenContent: (ad, error) {
              Logger.log('❌ فشل في عرض إعلان المكافأة: ${error.message}');
              ad.dispose();
              _isRewardedAdReady = false;
              _loadRewardedAd(); // إعادة تحميل الإعلان
            },
          );
        },
        onAdFailedToLoad: (error) {
          Logger.log('❌ فشل في تحميل إعلان المكافأة: ${error.message}');
          _isRewardedAdReady = false;

          // إذا كان الخطأ متعلق بمعرف الإعلان، نعطل الإعلانات
          if (error.code == 1 || // Invalid request
              error.code == 3) {
            // No fill
            Logger.log('⚠️ معرف إعلان المكافأة غير صالح أو لا يوجد إعلان متاح');
          } else {
            // إعادة المحاولة بعد فترة
            Future.delayed(const Duration(minutes: 1), _loadRewardedAd);
          }
        },
      ),
    );
  }

  /// عرض إعلان البانر
  Widget? getBannerAdWidget() {
    if (!isBannerAdReady) return null;
    return AdWidget(ad: _bannerAd!);
  }

  /// عرض إعلان بيني
  Future<bool> showInterstitialAd() async {
    if (!isInterstitialAdReady) {
      Logger.log('ℹ️ الإعلان البيني غير جاهز للعرض');
      return false;
    }

    try {
      await _interstitialAd?.show();
      return true;
    } catch (e) {
      Logger.log('❌ خطأ في عرض الإعلان البيني: $e');
      return false;
    }
  }

  /// عرض إعلان المكافأة
  Future<bool> showRewardedAd({Function(RewardItem)? onRewarded}) async {
    if (!isRewardedAdReady) {
      Logger.log('ℹ️ إعلان المكافأة غير جاهز للعرض');
      return false;
    }

    final completer = Completer<bool>();

    try {
      await _rewardedAd?.show(
        onUserEarnedReward: (ad, reward) {
          Logger.log('✅ تم منح المكافأة: ${reward.amount} ${reward.type}');
          if (onRewarded != null) {
            onRewarded(reward);
          }
          completer.complete(true);
        },
      );

      // إذا لم يتم استدعاء onUserEarnedReward، فسيتم إكمال المستقبل بعد فترة
      Future.delayed(const Duration(seconds: 30), () {
        if (!completer.isCompleted) {
          completer.complete(false);
        }
      });

      return await completer.future;
    } catch (e) {
      Logger.log('❌ خطأ في عرض إعلان المكافأة: $e');
      if (!completer.isCompleted) {
        completer.complete(false);
      }
      return false;
    }
  }

  /// تحديث الإعدادات من التحكم عن بعد
  Future<void> updateFromRemoteConfig(String url) async {
    try {
      Logger.log('🔄 جاري تحديث الإعلانات من التحكم عن بعد...');

      // تحديث التكوين من الخادم - هذا سيحدث معرفات الإعلانات تلقائيًا
      await _remoteConfigService.fetchConfig(url);

      // طباعة معرفات الإعلانات الثابتة المستخدمة
      Logger.log('📋 معرفات الإعلانات الثابتة المستخدمة:');
      Logger.log('📋 معرف تطبيق AdMob: ca-app-pub-9278102953647270~6028576475');
      Logger.log('📋 معرف إعلان البانر: $_bannerAdUnitId');
      Logger.log('📋 معرف الإعلان البيني: $_interstitialAdUnitId');
      Logger.log('📋 معرف إعلان المكافأة: $_rewardedAdUnitId');

      // التخلص من الإعلانات الحالية
      _bannerAd?.dispose();
      _interstitialAd?.dispose();
      _rewardedAd?.dispose();
      _bannerAd = null;
      _interstitialAd = null;
      _rewardedAd = null;
      _isInterstitialAdReady = false;
      _isRewardedAdReady = false;

      // إعادة تحميل الإعلانات بمعرفات الإعلانات الجديدة
      if (_remoteConfigService.config.showBannerAds) {
        _loadBannerAd();
      }

      if (_remoteConfigService.config.showInterstitialAds) {
        _loadInterstitialAd();
      }

      if (_remoteConfigService.config.showRewardedAds) {
        _loadRewardedAd();
      }

      Logger.log('✅ تم تحديث إعدادات الإعلانات من التحكم عن بعد بنجاح');

      // طباعة حالة الإعلانات للتشخيص
      logAdStatus();
    } catch (e) {
      Logger.log('❌ خطأ في تحديث إعدادات الإعلانات من التحكم عن بعد: $e');
    }
  }

  /// تعيين عنوان URL للتكوين
  void setConfigUrl(String url) {
    if (url.isEmpty) {
      Logger.log('⚠️ عنوان URL للتكوين فارغ، لم يتم تغييره');
      return;
    }

    // تعيين عنوان URL في خدمة التحكم عن بعد
    _remoteConfigService.setConfigUrl(url);

    Logger.log('✅ تم تعيين عنوان URL للتكوين في مدير الإعلانات: $url');
  }

  /// تحديث الإعلانات بشكل فوري
  Future<void> forceRefresh() async {
    try {
      Logger.log('🔄 جاري تحديث الإعلانات بشكل فوري...');

      // تحديث التكوين من التحكم عن بعد
      await _remoteConfigService.refreshConfig();

      // تحديث معرفات الإعلانات
      await updateFromRemoteConfig(_remoteConfigService.configUrl);

      Logger.log('✅ تم تحديث الإعلانات بشكل فوري بنجاح');

      // طباعة حالة الإعلانات للتشخيص
      logAdStatus();

      // طباعة حالة التكوين للتشخيص
      _remoteConfigService.logConfigStatus();
    } catch (e) {
      Logger.log('❌ خطأ في تحديث الإعلانات بشكل فوري: $e');
    }
  }

  /// التخلص من الموارد
  void dispose() {
    _bannerAd?.dispose();
    _interstitialAd?.dispose();
    _rewardedAd?.dispose();
    _bannerAd = null;
    _interstitialAd = null;
    _rewardedAd = null;
    _isInterstitialAdReady = false;
    _isRewardedAdReady = false;
  }
}
