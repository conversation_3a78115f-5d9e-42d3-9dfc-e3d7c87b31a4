import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة إدارة اللغة في التطبيق
class LanguageService extends ChangeNotifier {
  static const String _languageKey = 'language';
  static const String _defaultLanguage = 'ar';

  Locale _currentLocale = const Locale(_defaultLanguage);

  // الحصول على اللغة الحالية
  Locale get currentLocale => _currentLocale;

  // الحصول على اتجاه النص (دائمًا من اليمين إلى اليسار)
  TextDirection get textDirection => TextDirection.rtl;

  // تهيئة الخدمة
  Future<void> init() async {
    await loadSavedLanguage();
  }

  // تحميل اللغة المحفوظة - دائمًا تعيد اللغة العربية
  Future<void> loadSavedLanguage() async {
    try {
      // تجاهل اللغة المحفوظة واستخدام اللغة العربية دائمًا
      _currentLocale = const Locale(_defaultLanguage);
      debugPrint('✅ تم تعيين اللغة الافتراضية: $_defaultLanguage');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ خطأ في تحميل اللغة: $e');
      _currentLocale = const Locale(_defaultLanguage);
    }
  }

  // تغيير اللغة - تحفظ اللغة ولكن تبقي العربية كلغة عرض
  Future<bool> changeLanguage(String languageCode) async {
    try {
      // حفظ اللغة المختارة في الإعدادات
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_languageKey, languageCode);

      // إظهار رسالة نجاح للمستخدم
      debugPrint('✅ تم حفظ اللغة المختارة: $languageCode');

      // لكن نبقي على اللغة العربية كلغة عرض
      if (_currentLocale.languageCode != _defaultLanguage) {
        _currentLocale = const Locale(_defaultLanguage);
        notifyListeners();
      }

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في تغيير اللغة: $e');
      return false;
    }
  }

  // التحقق من اللغة الحالية
  bool isCurrentLanguage(String languageCode) {
    return _currentLocale.languageCode == languageCode;
  }
}
