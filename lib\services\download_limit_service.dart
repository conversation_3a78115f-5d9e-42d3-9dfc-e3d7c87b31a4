import 'package:shared_preferences/shared_preferences.dart';
import '../data/models/app_settings.dart';

class DownloadLimitService {
  static const String _keyDailyDownloads = 'daily_downloads';
  static const String _keyLastDownloadDate = 'last_download_date';

  // الحصول على عدد التنزيلات اليومية المتبقية
  Future<int> getRemainingDownloads(AppSettings settings) async {
    final prefs = await SharedPreferences.getInstance();
    final String today = _getTodayDateString();
    final String lastDownloadDate = prefs.getString(_keyLastDownloadDate) ?? '';

    // إذا كان اليوم مختلفًا عن آخر يوم تنزيل، نعيد تعيين العداد
    if (today != lastDownloadDate) {
      await prefs.setInt(_keyDailyDownloads, 0);
      await prefs.setString(_keyLastDownloadDate, today);
      return settings.dailyDownloadLimit;
    }

    // الحصول على عدد التنزيلات اليوم
    final int dailyDownloads = prefs.getInt(_keyDailyDownloads) ?? 0;
    return settings.dailyDownloadLimit - dailyDownloads;
  }

  // تسجيل تنزيل جديد
  Future<bool> recordDownload(AppSettings settings) async {
    // إذا كان المستخدم يملك النسخة المدفوعة، لا نقوم بتتبع التنزيلات
    if (settings.isPro) {
      return true;
    }

    final prefs = await SharedPreferences.getInstance();
    final String today = _getTodayDateString();
    final String lastDownloadDate = prefs.getString(_keyLastDownloadDate) ?? '';

    // إذا كان اليوم مختلفًا عن آخر يوم تنزيل، نعيد تعيين العداد
    if (today != lastDownloadDate) {
      await prefs.setInt(_keyDailyDownloads, 1);
      await prefs.setString(_keyLastDownloadDate, today);
      return true;
    }

    // الحصول على عدد التنزيلات اليوم
    final int dailyDownloads = prefs.getInt(_keyDailyDownloads) ?? 0;

    // التحقق من عدم تجاوز الحد اليومي
    if (dailyDownloads >= settings.dailyDownloadLimit) {
      return false;
    }

    // زيادة عدد التنزيلات
    await prefs.setInt(_keyDailyDownloads, dailyDownloads + 1);
    return true;
  }

  // الحصول على تاريخ اليوم كسلسلة نصية
  String _getTodayDateString() {
    final now = DateTime.now();
    return '${now.year}-${now.month}-${now.day}';
  }

  // إعادة تعيين عداد التنزيلات اليومية
  Future<void> resetDailyDownloads() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_keyDailyDownloads, 0);
    await prefs.setString(_keyLastDownloadDate, _getTodayDateString());
  }

  // الحصول على عدد التنزيلات اليوم
  Future<int> getDailyDownloads() async {
    final prefs = await SharedPreferences.getInstance();
    final String today = _getTodayDateString();
    final String lastDownloadDate = prefs.getString(_keyLastDownloadDate) ?? '';

    // إذا كان اليوم مختلفًا عن آخر يوم تنزيل، نعيد تعيين العداد
    if (today != lastDownloadDate) {
      await prefs.setInt(_keyDailyDownloads, 0);
      await prefs.setString(_keyLastDownloadDate, today);
      return 0;
    }

    return prefs.getInt(_keyDailyDownloads) ?? 0;
  }
}
