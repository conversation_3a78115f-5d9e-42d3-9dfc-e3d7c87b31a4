import 'package:flutter/material.dart';
import 'platform_utils.dart';

/// مزود معلومات إصدار أندرويد
class AndroidVersionProvider extends ChangeNotifier {
  bool _isAndroid12OrHigher = false;
  bool _isLoading = true;
  String _androidVersion = '';
  int _sdkInt = 0;

  bool get isAndroid12OrHigher => _isAndroid12OrHigher;
  bool get isLoading => _isLoading;
  String get androidVersion => _androidVersion;
  int get sdkInt => _sdkInt;

  /// تهيئة معلومات إصدار أندرويد
  Future<void> init() async {
    _isAndroid12OrHigher = await PlatformUtils.isAndroid12OrHigher();
    _androidVersion = await PlatformUtils.getAndroidVersionString();
    _sdkInt = await PlatformUtils.getAndroidSdkInt();
    _isLoading = false;
    notifyListeners();
  }
}
