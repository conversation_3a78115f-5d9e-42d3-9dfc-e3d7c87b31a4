/// نظام الأبعاد الموحد للتطبيق
class AppDimensions {
  // نظام الشبكة الأساسي (8dp)
  static const double grid1 = 4.0;
  static const double grid2 = 8.0;
  static const double grid3 = 12.0;
  static const double grid4 = 16.0;
  static const double grid5 = 20.0;
  static const double grid6 = 24.0;
  static const double grid8 = 32.0;
  static const double grid10 = 40.0;
  static const double grid12 = 48.0;
  static const double grid16 = 64.0;
  
  // الهوامش
  static const double marginXSmall = 4.0;
  static const double marginSmall = 8.0;
  static const double marginMedium = 16.0;
  static const double marginLarge = 24.0;
  static const double marginXLarge = 32.0;
  static const double marginXXLarge = 48.0;
  
  // التباعد الداخلي
  static const double paddingXSmall = 4.0;
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  
  // أنصاف أقطار الزوايا
  static const double radiusXSmall = 4.0;
  static const double radiusSmall = 8.0;
  static const double radiusMedium = 12.0;
  static const double radiusLarge = 16.0;
  static const double radiusXLarge = 24.0;
  static const double radiusCircular = 100.0;
  
  // أحجام الأيقونات
  static const double iconSmall = 16.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
  
  // ارتفاعات العناصر
  static const double buttonHeight = 48.0;
  static const double inputHeight = 56.0;
  static const double appBarHeight = 64.0;
  static const double bottomNavHeight = 64.0;
  static const double cardMinHeight = 80.0;
  
  // عرض العناصر
  static const double buttonMinWidth = 120.0;
  static const double dialogWidth = 320.0;
  static const double maxContentWidth = 600.0;
  
  // سماكة الحدود
  static const double borderThin = 1.0;
  static const double borderMedium = 2.0;
  static const double borderThick = 3.0;
  
  // الظلال
  static const double elevationSmall = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;
  static const double elevationXLarge = 16.0;
  
  // أحجام الصور
  static const double avatarSmall = 32.0;
  static const double avatarMedium = 48.0;
  static const double avatarLarge = 64.0;
  static const double thumbnailSmall = 80.0;
  static const double thumbnailMedium = 120.0;
  static const double thumbnailLarge = 160.0;
  
  // أحجام الشاشة
  static const double screenSmall = 360.0;
  static const double screenMedium = 600.0;
  static const double screenLarge = 900.0;
  static const double screenXLarge = 1200.0;
}
