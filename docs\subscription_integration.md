# دليل تكامل الاشتراكات في تطبيق خلفيات إسلامية

هذا الدليل يشرح كيفية تكامل خدمات الاشتراك في تطبيق خلفيات إسلامية باستخدام Google Play Billing API.

## نظرة عامة

تم تنفيذ نظام الاشتراكات باستخدام المكونات التالية:

1. **نموذج الاشتراك (Subscription Model)**: يمثل بيانات الاشتراك.
2. **خدمة Google Play Billing**: تتعامل مع عمليات الشراء والاشتراك من Google Play.
3. **خدمة الاشتراك (Subscription Service)**: توفر واجهة موحدة للتعامل مع الاشتراكات.

## الإعداد

### 1. إعداد Google Play Console

قبل استخدام خدمات الاشتراك، يجب إعداد المنتجات في Google Play Console:

1. قم بتسجيل الدخول إلى [Google Play Console](https://play.google.com/console/).
2. انتقل إلى تطبيقك > الربح > المنتجات > الاشتراكات.
3. أنشئ اشتراكات جديدة بالمعرفات التالية:
   - `com.islamiclandporta.islam.allahwallpaper.ahmad.np.monthly`: اشتراك شهري
   - `com.islamiclandporta.islam.allahwallpaper.ahmad.np.yearly`: اشتراك سنوي
   - `com.islamiclandporta.islam.allahwallpaper.ahmad.np.lifetime`: اشتراك مدى الحياة
4. حدد الأسعار وفترات التجديد لكل اشتراك.
5. انشر الاشتراكات.

### 2. إعداد Cloud Pub/Sub

لتلقي إشعارات حول حالة الاشتراكات:

1. قم بإنشاء مشروع في [Google Cloud Platform](https://console.cloud.google.com/).
2. قم بتمكين Cloud Pub/Sub API.
3. أنشئ موضوع (Topic) جديد.
4. قم بربط موضوع Cloud Pub/Sub بتطبيقك في Google Play Console.

## استخدام خدمات الاشتراك

### تهيئة خدمة الاشتراك

يتم تهيئة خدمة الاشتراك تلقائياً عند بدء التطبيق:

```dart
final subscriptionService = SubscriptionService();
await subscriptionService.initialize();
```

### الحصول على الاشتراكات المتاحة

```dart
final availableSubscriptions = SubscriptionService.getAvailableSubscriptions();
```

### الحصول على الاشتراك الحالي

```dart
final currentSubscription = await subscriptionService.getCurrentSubscription();
if (currentSubscription != null && currentSubscription.isActive) {
  // المستخدم لديه اشتراك نشط
} else {
  // المستخدم ليس لديه اشتراك نشط
}
```

### شراء اشتراك

```dart
final success = await subscriptionService.purchaseSubscription('monthly');
if (success) {
  // تم الشراء بنجاح
} else {
  // فشل الشراء
}
```

### استعادة الاشتراكات

```dart
final success = await subscriptionService.restoreSubscription();
if (success) {
  // تم استعادة الاشتراك بنجاح
} else {
  // لم يتم العثور على اشتراكات سابقة
}
```

### إلغاء الاشتراك

```dart
// ملاحظة: لا يمكن إلغاء الاشتراكات برمجياً من التطبيق
// يجب على المستخدم الذهاب إلى Google Play Store لإلغاء الاشتراك
// هذه الطريقة تقوم فقط بتعديل حالة الاشتراك محلياً
final success = await subscriptionService.cancelSubscription();
```

## اختبار الاشتراكات

### اختبار في بيئة التطوير

1. أضف حساب اختبار في Google Play Console.
2. استخدم حساب الاختبار للشراء.
3. استخدم أدوات Google Play Billing للتحقق من عمليات الشراء.

### اختبار في بيئة الإنتاج

1. أنشئ نسخة ألفا أو بيتا من التطبيق.
2. أضف مختبرين للنسخة.
3. اختبر عمليات الشراء الحقيقية (سيتم رد المبالغ تلقائياً للمختبرين).

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة

1. **عدم ظهور المنتجات**: تأكد من نشر المنتجات في Google Play Console.
2. **فشل عمليات الشراء**: تحقق من تكوين حساب التاجر في Google Play.
3. **عدم تلقي الإشعارات**: تحقق من إعدادات Cloud Pub/Sub.

### سجلات التصحيح

استخدم `debugPrint` لتتبع عمليات الشراء والاشتراك:

```dart
debugPrint('✅ تم تهيئة خدمة الاشتراك بنجاح');
debugPrint('❌ خطأ في شراء الاشتراك: $e');
```

## الموارد

- [توثيق Google Play Billing](https://developer.android.com/google/play/billing)
- [توثيق حزمة in_app_purchase](https://pub.dev/packages/in_app_purchase)
- [توثيق Cloud Pub/Sub](https://cloud.google.com/pubsub/docs)
