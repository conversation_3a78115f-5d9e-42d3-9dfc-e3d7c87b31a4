import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/typography.dart';

/// شريط تذييل موحد للتطبيق
class AppFooter extends StatelessWidget {
  final bool isTransparent;

  const AppFooter({super.key, this.isTransparent = false});

  @override
  Widget build(BuildContext context) {
    // استخدام MediaQuery للحصول على معلومات حول حجم الشاشة
    final mediaQuery = MediaQuery.of(context);

    // حساب ارتفاع مناسب بناءً على حجم الشاشة
    final dynamicHeight = mediaQuery.size.height * 0.05; // 5% من ارتفاع الشاشة
    final minHeight = 32.0; // الحد الأدنى للارتفاع
    final maxHeight = 48.0; // الحد الأقصى للارتفاع

    // تطبيق الحدود على الارتفاع
    final height = dynamicHeight.clamp(minHeight, maxHeight);

    // حساب حجم الخط المناسب
    final fontSize = (height * 0.4).clamp(12.0, 14.0);

    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        vertical: height * 0.2, // حشوة عمودية نسبية
      ),
      decoration: BoxDecoration(
        color: isTransparent ? Colors.transparent : AppColors.navBackground,
        boxShadow:
            isTransparent
                ? null
                : [
                  BoxShadow(
                    color: Colors.black.withAlpha(38), // 0.15 * 255 = 38
                    blurRadius: 4,
                    offset: const Offset(0, -1),
                  ),
                ],
      ),
      child: Center(
        child: Text(
          'خلفيات إسلامية',
          style: AppTypography.bodySmall.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
            fontSize: fontSize,
          ),
        ),
      ),
    );
  }
}
