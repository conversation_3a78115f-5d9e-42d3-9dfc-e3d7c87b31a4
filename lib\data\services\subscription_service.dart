import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';
import 'package:package_info/package_info.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/subscription.dart';
import 'google_play_billing_service.dart';

class SubscriptionService {
  // تطبيق نمط Singleton للتأكد من وجود نسخة واحدة فقط من الخدمة
  static final SubscriptionService _instance = SubscriptionService._internal();
  factory SubscriptionService() => _instance;
  SubscriptionService._internal();

  static const String _subscriptionKey = 'user_subscription';
  static const String _purchaseHistoryKey = 'purchase_history';
  static const String _deviceFingerprintKey = 'device_fingerprint';
  static const String _securityTokenKey = 'security_token';

  // خدمة Google Play Billing
  final GooglePlayBillingService _billingService = GooglePlayBillingService();
  bool _isInitialized = false;

  // متغير للتحكم في عدد مرات الاستدعاء
  int _initializationAttempts = 0;
  static const int _maxInitializationAttempts = 3;

  // متغيرات الأمان
  String? _deviceFingerprint;
  String? _securityToken;
  DateTime? _lastSecurityCheck;

  // تحسين الأداء
  static const int _cacheTimeout = 300000; // 5 دقائق
  Subscription? _cachedSubscription;
  DateTime? _lastCacheTime;

  // قائمة الاشتراكات المتاحة - محسنة وتحديثها لتتوافق مع منتجات Google Play
  static List<Subscription> getAvailableSubscriptions() {
    return [
      Subscription(
        id: 'monthly',
        name: 'اشتراك شهري',
        description: 'اشترك في النسخة المميزة من تطبيق خلفيات إسلامية واستمتع بمزايا حصرية لمدة شهر كامل',
        price: 3.55,
        currency: 'JOD',
        durationDays: 30,
        isRecommended: true,
        productId: 'com.islamiclandporta.islam.allahwallpaper.ahmad.np.monthly',
        features: [
          'تنزيل جميع الخلفيات بجودة عالية',
          'إزالة جميع الإعلانات',
          'الوصول المبكر للخلفيات الجديدة',
          'دعم تطوير المحتوى الإسلامي',
          'دعم فريق التطوير لمواصلة العمل',
          'تحديثات مجانية مستمرة',
        ],
        allowsDownloads: true,
        removesAds: true,
      ),
      Subscription(
        id: 'yearly',
        name: 'اشتراك سنوي',
        description: 'اشترك في النسخة المميزة من تطبيق خلفيات إسلامية واستمتع بمزايا حصرية لمدة عام كامل بسعر مخفض',
        price: 28.35,
        currency: 'JOD',
        durationDays: 365,
        productId: 'com.islamiclandporta.islam.allahwallpaper.ahmad.np.yearly',
        features: [
          'تنزيل جميع الخلفيات بجودة عالية',
          'إزالة جميع الإعلانات',
          'الوصول المبكر للخلفيات الجديدة',
          'دعم تطوير المحتوى الإسلامي',
          'توفير أكثر من 50% مقارنة بالاشتراك الشهري',
          'ميزات حصرية إضافية للمدفوعين',
          'دعم فريق التطوير لمواصلة العمل',
          'تحديثات مجانية مستمرة',
        ],
        allowsDownloads: true,
        removesAds: true,
      ),
      Subscription(
        id: 'lifetime',
        name: 'اشتراك مدى الحياة',
        description: 'احصل على النسخة المميزة من تطبيق خلفيات إسلامية إلى الأبد بدفعة واحدة',
        price: 35.45,
        currency: 'JOD',
        durationDays: 36500, // ~100 سنة
        productId: 'com.islamiclandporta.islam.allahwallpaper.ahmad.np.lifetime',
        features: [
          'تنزيل جميع الخلفيات بجودة عالية مدى الحياة',
          'إزالة جميع الإعلانات نهائياً',
          'الوصول المبكر للخلفيات الجديدة',
          'دعم تطوير المحتوى الإسلامي',
          'لا توجد مدفوعات متكررة أو تجديد تلقائي',
          'دفعة واحدة تمنحك جميع المزايا المميزة إلى الأبد',
          'ميزات حصرية إضافية للمدفوعين',
          'دعم فريق التطوير لمواصلة العمل',
          'تحديثات مجانية مستمرة',
          'وصول خاص للمحتوى القادم',
        ],
        allowsDownloads: true,
        removesAds: true,
      ),
    ];
  }

  // توليد بصمة الجهاز للأمان
  Future<String> _generateDeviceFingerprint() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final deviceId = 'flutter_${const Uuid().v4()}';
      final data = '${packageInfo.packageName}${packageInfo.version}${Platform.operatingSystem}$deviceId';
      final bytes = utf8.encode(data);
      return sha256.convert(bytes).toString();
    } catch (e) {
      return 'unknown_fingerprint';
    }
  }

  // توليد رمز أمان
  String _generateSecurityToken() {
    final data = '${DateTime.now().millisecondsSinceEpoch}${const Uuid().v4()}';
    final bytes = utf8.encode(data);
    return sha256.convert(bytes).toString();
  }

  // التحقق من صلاحية الاشتراك
  bool _isSubscriptionValid(Subscription subscription) {
    if (!subscription.isActive) return false;

    // التحقق من تاريخ الانتهاء
    if (subscription.endDate != null && DateTime.now().isAfter(subscription.endDate!)) {
      return false;
    }

    // التحقق من التوقيع الأمني
    if (_securityToken == null) return false;

    return true;
  }

  // تحديث بيانات التحقق الأمني
  Future<void> _updateSecurityData() async {
    try {
      // التحقق من وجود بصمة الجهاز
      if (_deviceFingerprint == null) {
        _deviceFingerprint = await _generateDeviceFingerprint();
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_deviceFingerprintKey, _deviceFingerprint!);
      }

      // توليد رمز أمان جديد
      _securityToken = _generateSecurityToken();
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_securityTokenKey, _securityToken!);
      _lastSecurityCheck = DateTime.now();

      debugPrint('✅ تم تحديث بيانات الأمان بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تحديث بيانات الأمان: $e');
    }
  }

  // تهيئة خدمة الاشتراك المحسنة
  Future<void> initialize() async {
    // التحقق السريع لتجنب التهيئة المتكررة
    if (_isInitialized) {
      debugPrint('⚠️ خدمة الاشتراك مهيأة بالفعل');
      return;
    }

    // التحقق من عدد محاولات التهيئة
    if (_initializationAttempts >= _maxInitializationAttempts) {
      debugPrint('⚠️ تم تجاوز الحد الأقصى لمحاولات التهيئة');
      _isInitialized = true; // تعيين كمهيأة لتجنب المزيد من المحاولات
      return;
    }

    _initializationAttempts++;
    debugPrint(
      '🔄 محاولة تهيئة خدمة الاشتراك ($_initializationAttempts/$_maxInitializationAttempts)',
    );

    if (!Platform.isAndroid) {
      // استخدام المحاكاة للمنصات غير المدعومة
      debugPrint('ℹ️ استخدام المحاكاة للمنصات غير المدعومة');

      // تهيئة بيانات الأمان حتى في المحاكاة
      await _updateSecurityData();

      _isInitialized = true;
      return;
    }

    try {
      // تهيئة بيانات الأمان أولاً
      await _updateSecurityData();

      // تهيئة خدمة Google Play Billing في خلفية
      await compute(
        (_) async {
          try {
            await _billingService.initialize();
            debugPrint('✅ تم تهيئة خدمة Google Play Billing بنجاح في الخلفية');
            return true;
          } catch (e) {
            debugPrint('❌ خطأ في تهيئة خدمة Google Play Billing: $e');
            return false;
          }
        },
        null,
      );

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الاشتراك بنجاح');

      // بدء مراقبة الاشتراكات بشكل دوري
      _startSubscriptionMonitoring();

    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الاشتراك: $e');
      // إذا كانت هذه المحاولة الأخيرة، نعتبر الخدمة مهيأة لتجنب المزيد من المحاولات
      if (_initializationAttempts >= _maxInitializationAttempts) {
        _isInitialized = true;
      }
    }
  }

  // بدء مراقبة الاشتراكات بشكل دوري
  void _startSubscriptionMonitoring() {
    // في بيئة الإنتاج، يمكن استخدام Timer.periodic
    // في بيئة التطوير، نستخدم تأخيراً بسيطاً
    if (kDebugMode) {
      Future.delayed(const Duration(hours: 1), () {
        if (!_isInitialized) return;

        try {
          debugPrint('🔍 مراقبة حالة الاشتراك...');
          getCurrentSubscription().then((subscription) {
            if (subscription != null && _isSubscriptionValid(subscription)) {
              debugPrint('✅ الاشتراك صالح: ${subscription.id}');
            } else {
              debugPrint('⚠️ الاشتراك غير صالح أو منتهي الصلاحية');
            }

            // تحديث بيانات الأمان بشكل دوري
            if (_lastSecurityCheck == null || 
                DateTime.now().difference(_lastSecurityCheck!).inMinutes > 30) {
              _updateSecurityData();
            }
          });
        } catch (e) {
          debugPrint('❌ خطأ في مراقبة الاشتراك: $e');
        }

        // استدعاء نفسه بشكل متكرر
        _startSubscriptionMonitoring();
      });
    }
  }

  // الحصول على الاشتراك الحالي المحسنة
  Future<Subscription?> getCurrentSubscription() async {
    try {
      // التحقق من التخزين المؤقت أولاً لتحسين الأداء
      if (_cachedSubscription != null && 
          _lastCacheTime != null && 
          DateTime.now().difference(_lastCacheTime!).inMilliseconds < _cacheTimeout) {
        debugPrint('📦 استخدام الاشتراك المخزن مؤقتاً: ${_cachedSubscription!.id}');
        return _cachedSubscription;
      }

      // تهيئة الخدمة إذا لم تكن مهيأة
      if (!_isInitialized) {
        await initialize();
      }

      // محاولة الحصول على الاشتراك من Google Play Billing
      Subscription? subscription;

      if (Platform.isAndroid) {
        try {
          final billingSubscription = _billingService.getActiveSubscription();
          if (billingSubscription != null) {
            // التحقق من صحة الاشتراك من Google Play
            if (_isSubscriptionValid(billingSubscription)) {
              subscription = billingSubscription;

              // حفظ الاشتراك محلياً
              final prefs = await SharedPreferences.getInstance();
              await prefs.setString(
                _subscriptionKey,
                json.encode(subscription.toJson()),
              );

              // تحديث التخزين المؤقت
              _cachedSubscription = subscription;
              _lastCacheTime = DateTime.now();

              debugPrint('✅ تم العثور على اشتراك نشط من Google Play: ${subscription.id}');
              return subscription;
            } else {
              debugPrint('⚠️ الاشتراك من Google Play غير صالح: ${billingSubscription.id}');
            }
          }
        } catch (e) {
          debugPrint('❌ خطأ في الحصول على الاشتراك من Google Play: $e');
        }
      }

      // إذا لم يتم العثور على اشتراك من Google Play، استخدم الاشتراك المحلي
      try {
        final prefs = await SharedPreferences.getInstance();
        final String? subscriptionJson = prefs.getString(_subscriptionKey);

        if (subscriptionJson == null) {
          debugPrint('ℹ️ لا يوجد اشتراك محفوظ محلياً');
          return null;
        }

        subscription = Subscription.fromJson(json.decode(subscriptionJson));

        // التحقق من صلاحية الاشتراك المحلي
        if (!_isSubscriptionValid(subscription)) {
          // الاشتراك منتهي الصلاحية أو غير صالح
          debugPrint('⚠️ الاشتراك المحلي منتهي الصلاحية: ${subscription.id}');

          // تحديث حالة الاشتراك في التخزين
          subscription = subscription.copyWith(isActive: false);
          await prefs.setString(
            _subscriptionKey,
            json.encode(subscription.toJson()),
          );
        } else {
          debugPrint('✅ تم العثور على اشتراك محلي نشط: ${subscription.id}');
        }

        // تحديث التخزين المؤقت
        _cachedSubscription = subscription;
        _lastCacheTime = DateTime.now();

        return subscription;
      } catch (e) {
        debugPrint('❌ خطأ في الحصول على الاشتراك المحلي: $e');
        return null;
      }
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الاشتراك: $e');
      return null;
    }
  }

  // التحقق مما إذا كان المستخدم مشتركًا
  Future<bool> isSubscribed() async {
    final subscription = await getCurrentSubscription();
    return subscription != null && subscription.isActive;
  }

  // عرض مربع حوار تأكيد الشراء في بيئة التطوير
  Future<bool> _showDebugPurchaseConfirmation(Subscription subscription) async {
    // في بيئة التطوير، نستخدم مربع حوار بسيط
    if (!kDebugMode) return true;

    // في بيئة التطوير، نفترض أن المستخدم وافق على الشراء
    debugPrint('⚠️ محاكاة تأكيد الشراء في بيئة التطوير: ${subscription.name}');
    return true;
  }

  // التحقق مما إذا كان يمكن للمستخدم تنزيل الخلفيات
  // ملاحظة: تنزيل الخلفيات متاح فقط للمشتركين أو بعد مشاهدة إعلان
  Future<bool> canDownloadWallpapers() async {
    // يمكن للمستخدم تنزيل الخلفيات بعد مشاهدة إعلان أو إذا كان مشتركاً
    final subscription = await getCurrentSubscription();
    if (subscription != null &&
        subscription.isActive &&
        subscription.allowsDownloads) {
      return true; // المستخدم مشترك ويمكنه التنزيل
    }

    // يمكن للمستخدم غير المشترك التنزيل بعد مشاهدة إعلان
    return false; // سيتم التعامل مع هذا في واجهة المستخدم
  }

  // التحقق مما إذا كان يمكن للمستخدم تعيين الخلفية
  // ملاحظة: تعيين الخلفية متاح للجميع مجاناً
  Future<bool> canSetWallpaper() async {
    return true; // متاح للجميع
  }

  // التحقق مما إذا كان يجب إخفاء الإعلانات
  Future<bool> shouldHideAds() async {
    final subscription = await getCurrentSubscription();
    return subscription != null &&
        subscription.isActive &&
        subscription.removesAds;
  }

  // اشتراك في خطة معينة
  Future<bool> subscribe(Subscription subscription) async {
    try {
      // إنشاء اشتراك نشط
      final now = DateTime.now();
      final endDate = now.add(Duration(days: subscription.durationDays));

      final activeSubscription = subscription.copyWith(
        isActive: true,
        startDate: now,
        endDate: endDate,
      );

      // حفظ الاشتراك
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _subscriptionKey,
        json.encode(activeSubscription.toJson()),
      );

      // إضافة إلى سجل المشتريات
      await _addToPurchaseHistory(activeSubscription);

      return true;
    } catch (e) {
      return false;
    }
  }

  // شراء اشتراك محسنة
  Future<bool> purchaseSubscription(String subscriptionId) async {
    try {
      // التحقق من صحة الاشتراك المطلوب
      final availableSubscriptions = getAvailableSubscriptions();
      final subscription = availableSubscriptions.firstWhere(
        (sub) => sub.id == subscriptionId,
        orElse: () => throw Exception('الاشتراك غير موجود'),
      );

      // التحقق من وجود معرف المنتج
      if (subscription.productId == null) {
        throw Exception('معرف المنتج غير موجود');
      }

      // تحديث بيانات الأمان قبل عملية الشراء
      await _updateSecurityData();

      // إرسال إشعار ببدء عملية الشراء
      _showSubscriptionStatusNotification(
        'جاري معالجة الاشتراك',
        'يرجى الانتظار حتى اكتمال عملية الشراء...',
      );

      bool success = false;

      // استخدام Google Play Billing إذا كان متاحاً
      if (Platform.isAndroid && _billingService.isAvailable) {
        try {
          // شراء المنتج باستخدام Google Play Billing في خلفية
          success = await compute(
            (_) async {
              try {
                return await _billingService.buyProduct(subscription.productId!);
              } catch (e) {
                debugPrint('❌ خطأ في شراء المنتج: $e');
                return false;
              }
            },
            null,
          );

          if (success) {
            // الانتظار قليلاً للتأكد من معالجة الشراء
            await Future.delayed(const Duration(seconds: 2));

            // الحصول على الاشتراك المحدث في خلفية
            final updatedSubscription = await compute(
              (_) async {
                try {
                  return _billingService.getActiveSubscription();
                } catch (e) {
                  debugPrint('❌ خطأ في الحصول على الاشتراك: $e');
                  return null;
                }
              },
              null,
            );

            if (updatedSubscription != null) {
              // التحقق من صحة الاشتراك
              if (_isSubscriptionValid(updatedSubscription)) {
                // حفظ الاشتراك محلياً
                final prefs = await SharedPreferences.getInstance();
                await prefs.setString(
                  _subscriptionKey,
                  json.encode(updatedSubscription.toJson()),
                );

                // إضافة إلى سجل المشتريات
                await _addToPurchaseHistory(updatedSubscription);

                // تحديث التخزين المؤقت
                _cachedSubscription = updatedSubscription;
                _lastCacheTime = DateTime.now();

                // إرسال إشعار بنجاح الاشتراك
                _showSubscriptionStatusNotification(
                  'تم الاشتراك بنجاح',
                  'أنت الآن مشترك في ${updatedSubscription.name}. استمتع بجميع المميزات!',
                  isSuccess: true,
                );

                return true;
              } else {
                throw Exception('الاشتراك غير صالح بعد الشراء');
              }
            }
          } else {
            // إرسال إشعار بفشل الاشتراك
            _showSubscriptionStatusNotification(
              'فشل الاشتراك',
              'حدث خطأ أثناء معالجة الاشتراك. يرجى المحاولة مرة أخرى.',
              isSuccess: false,
            );
          }
        } catch (e) {
          debugPrint('❌ خطأ في عملية الشراء: $e');
          return false;
        }
      } else {
        // في بيئة الإنتاج، نعرض رسالة للمستخدم
        if (!kDebugMode) {
          throw Exception(
            'خدمة الفوترة غير متاحة. يرجى التأكد من تثبيت Google Play وتحديثه.',
          );
        }

        // في بيئة التطوير، نستخدم المحاكاة
        if (kDebugMode) {
          debugPrint('⚠️ استخدام المحاكاة لشراء الاشتراك: ${subscription.id}');
          return await _simulatePurchase(subscription);
        }

        // في بيئة التطوير فقط، نعرض مربع حوار للتأكيد
        final bool confirmPurchase = await _showDebugPurchaseConfirmation(
          subscription,
        );
        if (!confirmPurchase) {
          return false;
        }

        // محاكاة الشراء للمنصات غير المدعومة أو عندما تكون خدمة الفوترة غير متاحة (في بيئة التطوير فقط)
        final now = DateTime.now();
        final endDate = now.add(Duration(days: subscription.durationDays));

        final activeSubscription = subscription.copyWith(
          isActive: true,
          startDate: now,
          endDate: endDate,
        );

        // حفظ الاشتراك
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(
          _subscriptionKey,
          json.encode(activeSubscription.toJson()),
        );

        // إضافة إلى سجل المشتريات
        await _addToPurchaseHistory(activeSubscription);

        return true;
      }

      return success;
    } catch (e) {
      debugPrint('خطأ في شراء الاشتراك: $e');
      return false;
    }
  }

  // دالة مساعدة لمحاكاة عملية الشراء في بيئة التطوير
  Future<bool> _simulatePurchase(Subscription subscription) async {
    try {
      // في بيئة التطوير، نستخدم تأخيرًا قصيرًا لمحاكاة عملية الشراء
      await Future.delayed(const Duration(seconds: 2));

      // إنشاء اشتراك نشط
      final now = DateTime.now();
      final endDate = now.add(Duration(days: subscription.durationDays));

      final activeSubscription = subscription.copyWith(
        isActive: true,
        startDate: now,
        endDate: endDate,
        purchaseToken: 'simulated_${DateTime.now().millisecondsSinceEpoch}',
      );

      // حفظ الاشتراك
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _subscriptionKey,
        json.encode(activeSubscription.toJson()),
      );

      // إضافة إلى سجل المشتريات
      await _addToPurchaseHistory(activeSubscription);

      // تحديث التخزين المؤقت
      _cachedSubscription = activeSubscription;
      _lastCacheTime = DateTime.now();

      debugPrint('✅ تم محاكاة شراء الاشتراك بنجاح: ${subscription.id}');
      return true;
    } catch (e) {
      debugPrint('❌ خطأ في محاكاة شراء الاشتراك: $e');
      return false;
    }
  }

  // إلغاء الاشتراك المحسنة
  Future<bool> cancelSubscription() async {
    try {
      // تهيئة الخدمة إذا لم تكن مهيأة
      if (!_isInitialized) {
        await initialize();
      }

      // الحصول على الاشتراك الحالي
      final subscription = await getCurrentSubscription();
      if (subscription == null) {
        return false;
      }

      // محاولة إلغاء الاشتراك من Google Play
      if (!kIsWeb &&
          (Platform.isAndroid || Platform.isIOS) &&
          _billingService.isAvailable &&
          subscription.productId != null) {
        // ملاحظة: لا يمكن إلغاء الاشتراكات برمجياً من التطبيق
        // يجب على المستخدم الذهاب إلى Google Play Store لإلغاء الاشتراك

        // فتح صفحة إدارة الاشتراكات في Google Play Store
        try {
          final Uri url = Uri.parse(
            'https://play.google.com/store/account/subscriptions',
          );

          // في بيئة التطوير، نطبع الرابط فقط
          if (kDebugMode) {
            debugPrint('🔗 سيتم فتح صفحة إدارة الاشتراكات: $url');
          } else {
            // في بيئة الإنتاج، سيتم محاولة فتح الرابط
            if (await canLaunchUrl(url)) {
              await launchUrl(url, mode: LaunchMode.externalApplication);
              debugPrint('✅ تم فتح صفحة إدارة الاشتراكات في Google Play Store');
            } else {
              // إذا لم يمكن فتح الرابط، حاول فتح تطبيق Google Play مباشرة
              final Uri marketUrl = Uri.parse('market://account/subscriptions');
              if (await canLaunchUrl(marketUrl)) {
                await launchUrl(marketUrl, mode: LaunchMode.externalApplication);
                debugPrint('✅ تم فتح صفحة إدارة الاشتراكات في تطبيق Google Play');
              } else {
                debugPrint('❌ لا يمكن فتح صفحة إدارة الاشتراكات');
                return false;
              }
            }
          }
        } catch (e) {
          debugPrint('❌ خطأ في فتح صفحة إدارة الاشتراكات: $e');
          return false;
        }

        // لا نقوم بتعديل حالة الاشتراك محلياً لأن المستخدم سيقوم بإلغاء الاشتراك من Google Play
        // وسيتم تحديث حالة الاشتراك تلقائياً عند التحقق من الاشتراك في المرة القادمة
        return true;
      }

      // للمنصات غير المدعومة، نقوم بتعديل حالة الاشتراك محلياً فقط
      final canceledSubscription = subscription.copyWith(isActive: false);

      // حفظ الاشتراك المعدل
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _subscriptionKey,
        json.encode(canceledSubscription.toJson()),
      );

      return true;
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الاشتراك: $e');
      return false;
    }
  }

  // استعادة الاشتراك
  Future<bool> restoreSubscription() async {
    try {
      // تهيئة الخدمة إذا لم تكن مهيأة
      if (!_isInitialized) {
        await initialize();
      }

      // مسح البيانات المخزنة محلياً للتأكد من الحصول على أحدث البيانات من Google Play
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_subscriptionKey);

      debugPrint('🔄 تم مسح بيانات الاشتراك المحلية للتحقق من Google Play');

      // استخدام Google Play Billing إذا كان متاحاً
      if (Platform.isAndroid && _billingService.isAvailable) {
        // إعادة تهيئة خدمة Google Play Billing للتأكد من استخدام الحساب الحالي
        await _billingService.initialize();

        // استعادة المشتريات من Google Play
        final success = await _billingService.restorePurchases();

        if (success) {
          // الانتظار قليلاً للتأكد من معالجة الاستعادة
          await Future.delayed(const Duration(seconds: 2));

          // الحصول على الاشتراك المستعاد
          final restoredSubscription = _billingService.getActiveSubscription();
          if (restoredSubscription != null) {
            // حفظ الاشتراك محلياً
            await prefs.setString(
              _subscriptionKey,
              json.encode(restoredSubscription.toJson()),
            );

            debugPrint(
              '✅ تم استعادة الاشتراك بنجاح: ${restoredSubscription.id}',
            );
            return true;
          } else {
            debugPrint(
              'ℹ️ لم يتم العثور على اشتراكات نشطة في حساب Google Play الحالي',
            );
          }
        } else {
          debugPrint('⚠️ فشل في استعادة المشتريات من Google Play');
        }
      } else {
        // محاكاة استعادة الاشتراك للمنصات غير المدعومة
        final purchaseHistory = await _getPurchaseHistory();
        if (purchaseHistory.isEmpty) {
          debugPrint('ℹ️ لا يوجد سجل مشتريات محلي');
          _showSubscriptionStatusNotification(
            'لم يتم العثور على اشتراكات',
            'لم نتمكن من العثور على أي اشتراكات سابقة.',
            isSuccess: false,
          );
          return false;
        }

        // البحث عن آخر اشتراك تم شراؤه
        final lastPurchase = purchaseHistory.last;

        // التحقق من صلاحية الاشتراك
        if (lastPurchase.endDate != null &&
            DateTime.now().isAfter(lastPurchase.endDate!)) {
          // الاشتراك منتهي الصلاحية
          debugPrint('⚠️ الاشتراك المحلي منتهي الصلاحية: ${lastPurchase.id}');
          _showSubscriptionStatusNotification(
            'الاشتراك منتهي الصلاحية',
            'لقد انتهت صلاحية اشتراكك. يرجى تجديد الاشتراك للاستمتاع بالمميزات.',
            isSuccess: false,
          );
          return false;
        }

        // استعادة الاشتراك
        final restoredSubscription = lastPurchase.copyWith(isActive: true);
        await prefs.setString(
          _subscriptionKey,
          json.encode(restoredSubscription.toJson()),
        );

        _showSubscriptionStatusNotification(
          'تم استعادة الاشتراك',
          'تم استعادة اشتراكك بنجاح: ${restoredSubscription.name}',
          isSuccess: true,
        );

        debugPrint('✅ تم استعادة الاشتراك المحلي: ${restoredSubscription.id}');
        return true;
      }

      return false;
    } catch (e) {
      debugPrint('❌ خطأ في استعادة الاشتراك: $e');
      return false;
    }
  }

  // إضافة اشتراك إلى سجل المشتريات
  Future<void> _addToPurchaseHistory(Subscription subscription) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> purchaseHistory =
          prefs.getStringList(_purchaseHistoryKey) ?? [];

      purchaseHistory.add(json.encode(subscription.toJson()));

      await prefs.setStringList(_purchaseHistoryKey, purchaseHistory);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // الحصول على سجل المشتريات
  Future<List<Subscription>> _getPurchaseHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String>? purchaseHistory = prefs.getStringList(
        _purchaseHistoryKey,
      );

      if (purchaseHistory == null || purchaseHistory.isEmpty) {
        return [];
      }

      return purchaseHistory
          .map((encoded) => Subscription.fromJson(json.decode(encoded)))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // عرض إشعار بحالة الاشتراك
  void _showSubscriptionStatusNotification(
    String title,
    String message, {
    bool isSuccess = true,
  }) {
    // استخدام Fluttertoast لعرض إشعار
    Fluttertoast.showToast(
      msg: '$title: $message',
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      timeInSecForIosWeb: 3,
      backgroundColor: isSuccess ? Colors.green.shade800 : Colors.red.shade800,
      textColor: Colors.white,
      fontSize: 16.0,
    );

    // طباعة الإشعار في وحدة التحكم للتصحيح
    if (isSuccess) {
      debugPrint('✅ $title: $message');
    } else {
      debugPrint('❌ $title: $message');
    }
  }

  // تحرير الموارد
  void dispose() {
    if (!kIsWeb && (Platform.isAndroid || Platform.isIOS) && _isInitialized) {
      _billingService.dispose();
    }
  }
}
