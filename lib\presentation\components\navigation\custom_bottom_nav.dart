import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';

import '../effects/glass_container.dart';

/// شريط تنقل سفلي مخصص
class CustomBottomNav extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<CustomBottomNavItem> items;
  final bool isGlass;
  final Color? backgroundColor;
  final double height;

  const CustomBottomNav({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.isGlass = true,
    this.backgroundColor,
    this.height = AppDimensions.bottomNavHeight,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height + MediaQuery.of(context).padding.bottom,
      decoration: BoxDecoration(boxShadow: AppShadows.elevated),
      child: isGlass ? _buildGlassNav(context) : _buildSolidNav(context),
    );
  }

  Widget _buildGlassNav(BuildContext context) {
    // استخدام MediaQuery للحصول على أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;

    return GlassContainer(
      blur: 20,
      borderRadius: AppDimensions.radiusLarge,
      margin: EdgeInsets.fromLTRB(
        screenSize.width * 0.05,
        0,
        screenSize.width * 0.05,
        screenSize.height * 0.02,
      ),
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
      border: 1.5,
      borderColor: Colors.white.withAlpha(40),
      boxShadow: AppShadows.medium,
      gradient: LinearGradient(
        colors: [Colors.white.withAlpha(30), Colors.white.withAlpha(15)],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      ),
      child: _buildNavItems(),
    );
  }

  Widget _buildSolidNav(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.navBackground,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusLarge),
          topRight: Radius.circular(AppDimensions.radiusLarge),
        ),
      ),
      child: _buildNavItems(),
    );
  }

  Widget _buildNavItems() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: List.generate(items.length, (index) {
        final item = items[index];
        final isSelected = currentIndex == index;

        return _NavItem(
          icon: isSelected ? item.activeIcon : item.icon,
          label: item.label,
          isSelected: isSelected,
          onTap: () => onTap(index),
        );
      }),
    );
  }
}

/// عنصر شريط التنقل
class _NavItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const _NavItem({
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // استخدام MediaQuery للحصول على أبعاد الشاشة
    final screenSize = MediaQuery.of(context).size;
    final iconSize = screenSize.width * 0.05; // تقليل حجم الأيقونة
    final fontSize = screenSize.width * 0.025; // تقليل حجم الخط

    return InkWell(
      onTap: onTap,
      customBorder: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
      ),
      splashColor: AppColors.primary.withAlpha(30),
      highlightColor: AppColors.primary.withAlpha(20),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOutQuint,
        padding: EdgeInsets.symmetric(
          horizontal: screenSize.width * 0.04,
          vertical: screenSize.height * 0.01,
        ),
        decoration: BoxDecoration(
          color:
              isSelected ? AppColors.primary.withAlpha(38) : Colors.transparent,
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          border:
              isSelected
                  ? Border.all(
                    color: AppColors.primary.withAlpha(50),
                    width: 1.5,
                  )
                  : null,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة العنصر مع تأثير حركي
            AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOutBack,
              height: isSelected ? iconSize * 1.2 : iconSize,
              width: isSelected ? iconSize * 1.2 : iconSize,
              decoration:
                  isSelected
                      ? BoxDecoration(
                        color: AppColors.primary.withAlpha(20),
                        shape: BoxShape.circle,
                      )
                      : null,
              child: Center(
                child: Icon(
                  icon,
                  color:
                      isSelected
                          ? AppColors.navActiveIcon
                          : AppColors.navInactiveIcon,
                  size: isSelected ? iconSize * 0.7 : iconSize * 0.6,
                ),
              ),
            ),

            // مسافة ديناميكية بين الأيقونة والنص
            SizedBox(height: 2),

            // نص العنصر
            Text(
              label,
              style: TextStyle(
                color:
                    isSelected
                        ? AppColors.navActiveIcon
                        : AppColors.navInactiveIcon,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: fontSize,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

/// عنصر شريط التنقل
class CustomBottomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  const CustomBottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}
