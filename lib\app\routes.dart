import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../presentation/screens/home_screen.dart';
import '../presentation/screens/home_content.dart';
import '../presentation/screens/wallpaper_details_screen.dart';
import '../presentation/screens/category_screen.dart';
import '../presentation/screens/favorites_screen.dart';
import '../presentation/screens/settings_screen.dart';
import '../presentation/screens/subscription/premium_subscription_screen.dart';
import '../presentation/screens/privacy_policy_screen.dart';
import '../presentation/screens/important_links_screen.dart';
import '../presentation/screens/about_screen.dart';
import '../presentation/screens/all_wallpapers_screen.dart';
import '../data/models/wallpaper.dart';
import 'go_router_observer.dart';

class AppRouter {
  // مفتاح التنقل الرئيسي
  static final _rootNavigatorKey = GlobalKey<NavigatorState>();
  static final _shellNavigatorKey = GlobalKey<NavigatorState>();

  static final GoRouter router = GoRouter(
    initialLocation: '/',
    navigatorKey: _rootNavigatorKey,
    // تعطيل الرسوم المتحركة للانتقال بين الشاشات لتجنب القلتش
    routerNeglect: true,
    // عرض إعلان بيني عند الانتقال بين الشاشات
    observers: [GoRouterObserver()],
    // تكوين الانتقالات بين الشاشات
    routes: [
      // الصفحة الرئيسية مباشرة
      GoRoute(path: '/', redirect: (context, state) => '/home'),

      // الشاشة الرئيسية مع شريط التنقل السفلي
      ShellRoute(
        navigatorKey: _shellNavigatorKey,
        builder: (context, state, child) {
          return HomeScreen(child: child);
        },
        routes: [
          // الصفحة الرئيسية
          GoRoute(
            path: '/home',
            builder: (context, state) => const HomeContent(),
          ),

          // صفحة المفضلة
          GoRoute(
            path: '/favorites',
            builder: (context, state) => const FavoritesScreen(),
          ),

          // صفحة الإعدادات
          GoRoute(
            path: '/settings',
            builder: (context, state) => const SettingsScreen(),
          ),
        ],
      ),

      // شاشة تفاصيل الخلفية - بدون شريط التنقل السفلي
      GoRoute(
        path: '/wallpaper/:id',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) {
          final wallpaper = state.extra as Wallpaper;
          return WallpaperDetailsScreen(wallpaper: wallpaper);
        },
      ),

      // شاشة الفئة - بدون شريط التنقل السفلي
      GoRoute(
        path: '/category/:id',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) {
          final categoryId = state.pathParameters['id']!;
          final categoryName = state.uri.queryParameters['name'] ?? '';
          final categoryColor =
              state.extra is Category ? (state.extra as Category).color : null;
          final categoryImageUrl =
              state.extra is Category
                  ? (state.extra as Category).imageUrl
                  : null;

          return CategoryScreen(
            categoryId: categoryId,
            categoryName: categoryName,
            categoryColor: categoryColor,
            categoryImageUrl: categoryImageUrl,
          );
        },
      ),

      // شاشة الاشتراك - بدون شريط التنقل السفلي
      GoRoute(
        path: '/subscription',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) => const PremiumSubscriptionScreen(),
      ),

      // شاشة عن التطبيق - بدون شريط التنقل السفلي
      GoRoute(
        path: '/about',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) => const AboutScreen(),
      ),

      // صفحة سياسة الخصوصية
      GoRoute(
        path: '/privacy-policy',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) => const PrivacyPolicyScreen(),
      ),

      // صفحة الروابط المهمة
      GoRoute(
        path: '/important-links',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) => const ImportantLinksScreen(),
      ),

      // صفحة جميع الخلفيات
      GoRoute(
        path: '/all-wallpapers',
        parentNavigatorKey: _rootNavigatorKey,
        builder: (context, state) => const AllWallpapersScreen(),
      ),
    ],
  );
}
