import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:ui' as ui;
import '../../../../data/models/subscription.dart';
import '../../../../design_system/colors.dart';
import '../../../../design_system/dimensions.dart';
import '../../../../design_system/typography.dart';
import '../../../components/effects/glass_container.dart';

class SubscriptionPlanCard extends StatefulWidget {
  final Subscription subscription;
  final bool isSelected;
  final bool isLoading;
  final VoidCallback? onTap;

  const SubscriptionPlanCard({
    super.key,
    required this.subscription,
    this.isSelected = false,
    this.isLoading = false,
    this.onTap,
  });

  @override
  State<SubscriptionPlanCard> createState() => _SubscriptionPlanCardState();
}

class _SubscriptionPlanCardState extends State<SubscriptionPlanCard>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _shimmerController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();

    // تهيئة الرسوم المتحركة
    _scaleController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _shimmerController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );

    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.linear),
    );

    // بدء الرسوم المتحركة
    _shimmerController.repeat();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.isLoading ? null : widget.onTap,
      onTapDown: (_) => _scaleController.forward(),
      onTapUp: (_) => _scaleController.reverse(),
      onTapCancel: () => _scaleController.reverse(),
      child: AnimatedBuilder(
        animation: _scaleController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: const EdgeInsets.only(bottom: 20),
              child: Stack(
                children: [
                  // بطاقة الاشتراك المحسنة
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
                      gradient: widget.isSelected
                          ? LinearGradient(
                              colors: AppColors.goldGradient,
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            )
                          : null,
                      border: Border.all(
                        color: widget.isSelected
                            ? AppColors.accent
                            : Colors.white.withOpacity(0.2),
                        width: widget.isSelected ? 2 : 1,
                      ),
                      boxShadow: widget.isSelected
                          ? [
                              BoxShadow(
                                color: AppColors.accent.withOpacity(0.4),
                                blurRadius: 20,
                                spreadRadius: 5,
                                offset: const Offset(0, 5),
                              ),
                            ]
                          : [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 10,
                                spreadRadius: 1,
                                offset: const Offset(0, 3),
                              ),
                            ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(
                          sigmaX: widget.isSelected ? 15 : 10,
                          sigmaY: widget.isSelected ? 15 : 10,
                        ),
                        child: Container(
                          padding: EdgeInsets.zero,
                          decoration: BoxDecoration(
                            color: widget.isSelected
                                ? AppColors.card.withOpacity(0.15)
                                : AppColors.card.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
                          ),
                          child: Column(
                            children: [
                              // رأس البطاقة المحسن
                              _buildCardHeader(),

                              // محتوى البطاقة
                              Padding(
                                padding: const EdgeInsets.all(20),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // وصف الاشتراك
                                    Text(
                                      widget.subscription.description,
                                      style: GoogleFonts.cairo(
                                        fontSize: 14,
                                        color: widget.isSelected
                                            ? Colors.white
                                            : Colors.white70,
                                        height: 1.5,
                                        letterSpacing: 0.3,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),

                                    const SizedBox(height: 16),

                                    // السعر والمدة
                                    _buildPriceSection(),

                                    const SizedBox(height: 16),

                                    // المميزات
                                    _buildFeaturesList(),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  // مؤشر التحميل المحسن
                  if (widget.isLoading)
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
                        ),
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const SizedBox(
                                width: 40,
                                height: 40,
                                child: CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.accent,
                                  ),
                                  strokeWidth: 3,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'جاري معالجة الاشتراك...',
                                style: GoogleFonts.cairo(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                  // شارة الخطة الموصى بها المحسنة
                  if (widget.subscription.isRecommended && !widget.isLoading)
                    Positioned(
                      top: -8,
                      right: 20,
                      child: AnimatedBuilder(
                        animation: _shimmerController,
                        builder: (context, child) {
                          return Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 14,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: AppColors.goldGradient,
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.accent.withOpacity(0.5),
                                  blurRadius: 10,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // تأثير السطوع المتحرك
                                Positioned.fill(
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(16),
                                    child: Align(
                                      alignment: Alignment(_shimmerAnimation.value - 0.5, 0),
                                      child: Container(
                                        width: 50,
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            begin: Alignment.centerLeft,
                                            end: Alignment.centerRight,
                                            colors: [
                                              Colors.transparent,
                                              Colors.white.withOpacity(0.3),
                                              Colors.transparent,
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),

                                // النص
                                Text(
                                  'الأكثر شعبية',
                                  style: GoogleFonts.cairo(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // رأس البطاقة
  Widget _buildCardHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              widget.isSelected
                  ? const [Color(0xFFD4AF37), Color(0xFFFFD700)]
                  : [
                    Colors.white.withOpacity(0.1),
                    Colors.white.withOpacity(0.05),
                  ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Row(
        children: [
          // أيقونة الاشتراك
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors:
                    widget.isSelected
                        ? [
                          Colors.black.withOpacity(0.8),
                          Colors.black.withOpacity(0.6),
                        ]
                        : [
                          AppColors.primary,
                          AppColors.primary.withOpacity(0.7),
                        ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color:
                      widget.isSelected
                          ? Colors.black.withOpacity(0.3)
                          : AppColors.primary.withOpacity(0.3),
                  blurRadius: 8,
                  spreadRadius: 1,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Center(
              child: Icon(
                _getSubscriptionIcon(),
                color: widget.isSelected ? AppColors.accent : Colors.white,
                size: 24,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // اسم الاشتراك
          Expanded(
            child: Text(
              widget.subscription.name,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: widget.isSelected ? Colors.black : Colors.white,
              ),
            ),
          ),

          // علامة الاختيار
          if (widget.isSelected)
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                shape: BoxShape.circle,
              ),
              child: const Icon(Icons.check, color: AppColors.accent, size: 20),
            ),
        ],
      ),
    );
  }

  // قسم السعر والمدة
  Widget _buildPriceSection() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        // السعر
        Text(
          '${widget.subscription.price}',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: widget.isSelected ? AppColors.accent : Colors.white,
          ),
        ),

        const SizedBox(width: 4),

        // العملة
        Text(
          widget.subscription.currency,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: widget.isSelected ? AppColors.accent : Colors.white70,
          ),
        ),

        const Spacer(),

        // المدة
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color:
                widget.isSelected
                    ? AppColors.accent.withOpacity(0.2)
                    : Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  widget.isSelected ? AppColors.accent : Colors.white.withOpacity(0.2),
              width: 1,
            ),
          ),
          child: Text(
            _getDurationText(),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: widget.isSelected ? AppColors.accent : Colors.white70,
            ),
          ),
        ),

        // خصم
        if (_hasDiscount()) ...[
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.green.withOpacity(0.5),
                width: 1,
              ),
            ),
            child: Text(
              _getDiscountText(),
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ),
        ],
      ],
    );
  }

  // قائمة المميزات
  Widget _buildFeaturesList() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان المميزات
        Text(
          'المميزات:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: widget.isSelected ? Colors.white : Colors.white70,
          ),
        ),

        const SizedBox(height: 12),

        // قائمة المميزات
        ...widget.subscription.features.map((feature) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors:
                          widget.isSelected
                              ? const [Color(0xFFD4AF37), Color(0xFFFFD700)]
                              : [
                                AppColors.primary,
                                AppColors.primary.withOpacity(0.7),
                              ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.check, color: Colors.black, size: 14),
                ),

                const SizedBox(width: 12),

                Expanded(
                  child: Text(
                    feature,
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.isSelected ? Colors.white : Colors.white70,
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  IconData _getSubscriptionIcon() {
    if (subscription.durationDays >= 365) {
      return Icons.calendar_today;
    } else if (subscription.durationDays >= 30) {
      return Icons.date_range;
    } else {
      return Icons.access_time;
    }
  }

  bool _hasDiscount() {
    return subscription.id.contains('yearly') || subscription.isRecommended;
  }

  String _getDiscountText() {
    if (subscription.id.contains('yearly')) {
      return 'وفر 33%';
    } else if (subscription.isRecommended) {
      return 'أفضل قيمة';
    }
    return '';
  }

  String _getDurationText() {
    if (subscription.durationDays >= 36500) {
      return 'مدى الحياة';
    } else if (subscription.durationDays >= 365) {
      return 'سنوي';
    } else if (subscription.durationDays >= 30) {
      return 'شهري';
    } else {
      return '${subscription.durationDays} يوم';
    }
  }
}
