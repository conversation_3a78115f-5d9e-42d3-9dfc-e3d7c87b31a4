import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../../design_system/colors.dart';

/// مكون تحميل بتأثير الوميض
class ShimmerLoading extends StatelessWidget {
  final Widget child;
  final Color baseColor;
  final Color highlightColor;

  const ShimmerLoading({
    super.key,
    required this.child,
    this.baseColor = const Color(0xFF1E1E1E),
    this.highlightColor = const Color(0xFF3A3A3A),
  });

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: baseColor,
      highlightColor: highlightColor,
      child: child,
    );
  }
}

/// بطاقة خلفية وهمية للتحميل
class WallpaperCardShimmer extends StatelessWidget {
  final double height;
  final double borderRadius;

  const WallpaperCardShimmer({
    super.key,
    this.height = 200,
    this.borderRadius = 16,
  });

  @override
  Widget build(BuildContext context) {
    return ShimmerLoading(
      child: Container(
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(borderRadius),
        ),
      ),
    );
  }
}

/// بطاقة فئة وهمية للتحميل
class CategoryCardShimmer extends StatelessWidget {
  final double width;
  final double height;
  final bool isCircular;

  const CategoryCardShimmer({
    super.key,
    this.width = 100,
    this.height = 100,
    this.isCircular = false,
  });

  @override
  Widget build(BuildContext context) {
    return ShimmerLoading(
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: isCircular ? BoxShape.circle : BoxShape.rectangle,
          borderRadius: isCircular ? null : BorderRadius.circular(16),
        ),
        child: isCircular
            ? null
            : Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    height: 16,
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
