import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// دالة مساعدة لتحليل اللون من النص
Color? _parseColor(dynamic colorValue) {
  if (colorValue == null) return null;

  try {
    final colorString = colorValue.toString();

    // إذا كان النص يبدأ بـ # أو 0x
    if (colorString.startsWith('#') || colorString.startsWith('0x')) {
      // إزالة # إذا وجدت
      final hexValue =
          colorString.startsWith('#')
              ? '0xFF${colorString.substring(1)}'
              : colorString;

      return Color(int.parse(hexValue));
    }
    // إذا كان النص يحتوي على Color
    else if (colorString.contains('Color(')) {
      // محاولة تحليل اللون من تنسيق القيم المنفصلة (alpha, red, green, blue)
      final rgbaRegex = RegExp(
        r'Color\(alpha: ([\d\.]+), red: ([\d\.]+), green: ([\d\.]+), blue: ([\d\.]+)',
      );
      final rgbaMatch = rgbaRegex.firstMatch(colorString);

      if (rgbaMatch != null && rgbaMatch.groupCount >= 4) {
        final alpha = double.parse(rgbaMatch.group(1)!);
        final red = double.parse(rgbaMatch.group(2)!);
        final green = double.parse(rgbaMatch.group(3)!);
        final blue = double.parse(rgbaMatch.group(4)!);

        return Color.fromARGB(
          (alpha * 255).round(),
          (red * 255).round(),
          (green * 255).round(),
          (blue * 255).round(),
        );
      } else {
        // محاولة تحليل اللون من تنسيق سداسي عشري
        final hexRegex = RegExp(r'Color\(0x([0-9A-Fa-f]{8})\)');
        final hexMatch = hexRegex.firstMatch(colorString);

        if (hexMatch != null && hexMatch.groupCount >= 1) {
          return Color(int.parse(hexMatch.group(1)!, radix: 16));
        }
      }
    } else {
      // محاولة تحليل النص كرقم سداسي عشري
      return Color(int.parse(colorString, radix: 16));
    }
  } catch (e) {
    debugPrint('خطأ في تحليل اللون: $e');
  }

  return null;
}

/// نموذج بيانات الخلفية
class Wallpaper extends Equatable {
  final String id;
  final String title;
  final String imageUrl;
  final String thumbnailUrl;
  final String category;
  final List<String> tags;
  final bool isFeatured;
  final bool isPremium;
  final int downloads;
  final int likes;
  final int views;
  final DateTime createdAt;
  final String resolution;
  final int fileSize; // بالكيلوبايت
  final String? blurHash;
  final Color? dominantColor;

  Wallpaper({
    required this.id,
    this.title = '',
    required this.imageUrl,
    required this.thumbnailUrl,
    this.category = 'إسلامية',
    this.tags = const [],
    this.isFeatured = false,
    this.isPremium = false,
    this.downloads = 0,
    this.likes = 0,
    this.views = 0,
    DateTime? createdAt,
    this.resolution = '',
    this.fileSize = 0,
    this.blurHash,
    this.dominantColor,
  }) : createdAt = createdAt ?? DateTime.now();

  factory Wallpaper.fromJson(Map<String, dynamic> json) {
    // التعامل مع API الجديد
    if (json.containsKey('Image_link') || json.containsKey('Image link')) {
      // تحويل من التنسيق الجديد
      final imageUrl = json['Image_link'] ?? json['Image link'] ?? '';
      final category = json['address'] ?? '';

      // جميع الخلفيات مجانية للتعيين (isPremium = false)
      final isPremium = false; // تم تعديله ليكون دائماً false

      return Wallpaper(
        id: json['id'] ?? '',
        title: category,
        imageUrl: imageUrl,
        thumbnailUrl: imageUrl,
        category: category,
        tags: [category],
        isFeatured: false,
        isPremium: isPremium,
        downloads:
            int.tryParse(
              json['Download_number']?.toString() ??
                  json['Download number']?.toString() ??
                  '0',
            ) ??
            0,
        likes: 0,
        views:
            int.tryParse(
              json['Views_number']?.toString() ??
                  json['Views number']?.toString() ??
                  '0',
            ) ??
            0,
        createdAt: DateTime.now(),
        resolution: '',
        fileSize: 0,
        blurHash: null,
        dominantColor: null,
      );
    } else {
      // التنسيق القديم
      return Wallpaper(
        id: json['id'] ?? '',
        title: json['title'] ?? '',
        imageUrl: json['imageUrl'] ?? '',
        thumbnailUrl: json['thumbnailUrl'] ?? json['imageUrl'] ?? '',
        category: json['category'] ?? 'إسلامية',
        tags: List<String>.from(json['tags'] ?? []),
        isFeatured: json['isFeatured'] ?? false,
        isPremium: false, // تم تعديله ليكون دائماً false
        downloads: json['downloads'] ?? 0,
        likes: json['likes'] ?? 0,
        views: json['views'] ?? 0,
        createdAt:
            json['createdAt'] != null
                ? DateTime.parse(json['createdAt'])
                : DateTime.now(),
        resolution: json['resolution'] ?? '',
        fileSize: json['fileSize'] ?? 0,
        blurHash: json['blurHash'],
        dominantColor: _parseColor(json['dominantColor']),
      );
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'imageUrl': imageUrl,
      'thumbnailUrl': thumbnailUrl,
      'category': category,
      'tags': tags,
      'isFeatured': isFeatured,
      'isPremium': isPremium,
      'downloads': downloads,
      'likes': likes,
      'views': views,
      'createdAt': createdAt.toIso8601String(),
      'resolution': resolution,
      'fileSize': fileSize,
      'blurHash': blurHash,
      'dominantColor': dominantColor?.toString(),
    };
  }

  Wallpaper copyWith({
    String? id,
    String? title,
    String? imageUrl,
    String? thumbnailUrl,
    String? category,
    List<String>? tags,
    bool? isFeatured,
    bool? isPremium,
    int? downloads,
    int? likes,
    int? views,
    DateTime? createdAt,
    String? resolution,
    int? fileSize,
    String? blurHash,
    Color? dominantColor,
  }) {
    return Wallpaper(
      id: id ?? this.id,
      title: title ?? this.title,
      imageUrl: imageUrl ?? this.imageUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      isFeatured: isFeatured ?? this.isFeatured,
      isPremium: isPremium ?? this.isPremium,
      downloads: downloads ?? this.downloads,
      likes: likes ?? this.likes,
      views: views ?? this.views,
      createdAt: createdAt ?? this.createdAt,
      resolution: resolution ?? this.resolution,
      fileSize: fileSize ?? this.fileSize,
      blurHash: blurHash ?? this.blurHash,
      dominantColor: dominantColor ?? this.dominantColor,
    );
  }

  @override
  List<Object?> get props => [
    id,
    title,
    imageUrl,
    thumbnailUrl,
    category,
    tags,
    isFeatured,
    isPremium,
    downloads,
    likes,
    views,
    createdAt,
    resolution,
    fileSize,
    blurHash,
    dominantColor,
  ];
}

/// نموذج بيانات الفئة
class Category extends Equatable {
  final String id;
  final String name;
  final String imageUrl;
  final String description;
  final int wallpaperCount;
  final Color? color;
  final String? blurHash;

  const Category({
    required this.id,
    required this.name,
    required this.imageUrl,
    this.description = '',
    this.wallpaperCount = 0,
    this.color,
    this.blurHash,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      description: json['description'] ?? '',
      wallpaperCount: json['wallpaperCount'] ?? 0,
      color: _parseColor(json['color']),
      blurHash: json['blurHash'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'imageUrl': imageUrl,
      'description': description,
      'wallpaperCount': wallpaperCount,
      'color': color?.toString(),
      'blurHash': blurHash,
    };
  }

  @override
  List<Object?> get props => [
    id,
    name,
    imageUrl,
    description,
    wallpaperCount,
    color,
    blurHash,
  ];
}
