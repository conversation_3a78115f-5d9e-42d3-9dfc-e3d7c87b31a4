import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/wallpaper.dart';

class FavoritesService {
  static const String _favoritesKey = 'user_favorites';

  // الحصول على قائمة المفضلة
  Future<List<Wallpaper>> getFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String>? favoritesJson = prefs.getStringList(_favoritesKey);
      
      if (favoritesJson == null || favoritesJson.isEmpty) {
        return [];
      }
      
      return favoritesJson
          .map((json) => Wallpaper.fromJson(jsonDecode(json)))
          .toList();
    } catch (e) {
      debugPrint('خطأ في جلب المفضلة: $e');
      return [];
    }
  }

  // إضافة خلفية إلى المفضلة
  Future<bool> addToFavorites(Wallpaper wallpaper) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String> favoritesJson = prefs.getStringList(_favoritesKey) ?? [];
      
      // التحقق من وجود الخلفية في المفضلة
      final favorites = favoritesJson
          .map((json) => Wallpaper.fromJson(jsonDecode(json)))
          .toList();
      
      if (favorites.any((fav) => fav.id == wallpaper.id)) {
        return true; // الخلفية موجودة بالفعل
      }
      
      // إضافة الخلفية إلى المفضلة
      favoritesJson.add(jsonEncode(wallpaper.toJson()));
      await prefs.setStringList(_favoritesKey, favoritesJson);
      
      return true;
    } catch (e) {
      debugPrint('خطأ في إضافة الخلفية إلى المفضلة: $e');
      return false;
    }
  }

  // إزالة خلفية من المفضلة
  Future<bool> removeFromFavorites(String wallpaperId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final List<String>? favoritesJson = prefs.getStringList(_favoritesKey);
      
      if (favoritesJson == null || favoritesJson.isEmpty) {
        return true; // لا توجد خلفيات في المفضلة
      }
      
      // تحويل JSON إلى كائنات
      final favorites = favoritesJson
          .map((json) => Wallpaper.fromJson(jsonDecode(json)))
          .toList();
      
      // إزالة الخلفية من القائمة
      favorites.removeWhere((fav) => fav.id == wallpaperId);
      
      // حفظ القائمة الجديدة
      final newFavoritesJson = favorites
          .map((fav) => jsonEncode(fav.toJson()))
          .toList();
      
      await prefs.setStringList(_favoritesKey, newFavoritesJson);
      
      return true;
    } catch (e) {
      debugPrint('خطأ في إزالة الخلفية من المفضلة: $e');
      return false;
    }
  }

  // التحقق من وجود خلفية في المفضلة
  Future<bool> isFavorite(String wallpaperId) async {
    try {
      final favorites = await getFavorites();
      return favorites.any((fav) => fav.id == wallpaperId);
    } catch (e) {
      debugPrint('خطأ في التحقق من وجود الخلفية في المفضلة: $e');
      return false;
    }
  }

  // الحصول على قائمة معرفات المفضلة
  Future<List<String>> getFavoriteIds() async {
    try {
      final favorites = await getFavorites();
      return favorites.map((fav) => fav.id).toList();
    } catch (e) {
      debugPrint('خطأ في جلب معرفات المفضلة: $e');
      return [];
    }
  }
}
