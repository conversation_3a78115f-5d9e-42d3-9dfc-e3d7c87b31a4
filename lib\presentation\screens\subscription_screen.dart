import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:glassmorphism/glassmorphism.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../design_system/colors.dart';
import '../../data/models/subscription.dart';
import '../../data/services/subscription_service.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen>
    with TickerProviderStateMixin {
  final SubscriptionService _subscriptionService = SubscriptionService();

  List<Subscription> _availableSubscriptions = [];
  Subscription? _currentSubscription;
  String? _selectedSubscriptionId;
  bool _isLoading = true;
  bool _isDisposed = false;

  // متحكمات الأنيميشن
  late AnimationController _pulseController;
  late AnimationController _shimmerController;

  @override
  void initState() {
    super.initState();

    // تهيئة متحكمات الأنيميشن
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    )..repeat();

    // تحميل البيانات
    _loadSubscriptions();
  }

  Future<void> _loadSubscriptions() async {
    if (!_isMounted) return;

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      // تهيئة خدمة الاشتراك
      await _subscriptionService.initialize();

      if (!_isMounted) return;

      // الحصول على الاشتراك الحالي
      final currentSubscription =
          await _subscriptionService.getCurrentSubscription();

      if (!_isMounted) return;

      // الحصول على الاشتراكات المتاحة
      final availableSubscriptions =
          SubscriptionService.getAvailableSubscriptions();

      if (!_isMounted) return;

      if (mounted) {
        setState(() {
          _currentSubscription = currentSubscription;
          _availableSubscriptions = availableSubscriptions;
          _selectedSubscriptionId =
              availableSubscriptions
                  .firstWhere(
                    (sub) => sub.isRecommended,
                    orElse: () => availableSubscriptions.first,
                  )
                  .id;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الاشتراكات: $e');
      if (_isMounted && mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _purchaseSubscription() async {
    if (_selectedSubscriptionId == null) return;

    // عرض مربع حوار تأكيد الشراء
    final bool confirmPurchase = await _showPurchaseConfirmationDialog();
    if (!confirmPurchase) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // عرض رسالة معالجة الدفع
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
              SizedBox(width: 12),
              Text('جاري معالجة عملية الدفع...'),
            ],
          ),
          backgroundColor: AppColors.primary,
          duration: Duration(seconds: 3),
        ),
      );

      final success = await _subscriptionService.purchaseSubscription(
        _selectedSubscriptionId!,
      );

      if (!_isMounted) return;

      // إخفاء رسالة المعالجة
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      if (success) {
        // إعادة تحميل البيانات للتأكد من تحديث حالة الاشتراك
        await _loadSubscriptions();

        if (!_isMounted) return;

        // التحقق من أن الاشتراك تم بنجاح فعلاً
        final currentSubscription = await _subscriptionService.getCurrentSubscription();

        if (!_isMounted) return;

        if (currentSubscription != null && currentSubscription.isActive) {
          // عرض شاشة نجاح الشراء فقط بعد تأكيد الاشتراك
          _showPurchaseSuccessDialog();
        } else {
          // إذا لم يتم تفعيل الاشتراك، عرض رسالة خطأ
          if (_isMounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('لم يتم تأكيد الاشتراك. يرجى المحاولة مرة أخرى أو التحقق من حالة الدفع.'),
                backgroundColor: AppColors.error,
                duration: Duration(seconds: 5),
              ),
            );
          }
        }
      } else {
        if (_isMounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إلغاء عملية الدفع أو حدث خطأ. حاول مرة أخرى.'),
              backgroundColor: AppColors.error,
              duration: Duration(seconds: 4),
            ),
          );
        }
      }
    } catch (e) {
      if (!_isMounted) return;

      // إخفاء رسالة المعالجة في حالة الخطأ
      ScaffoldMessenger.of(context).hideCurrentSnackBar();

      if (_isMounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء معالجة الدفع: ${e.toString()}'),
            backgroundColor: AppColors.error,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (_isMounted && mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // عرض مربع حوار تأكيد الشراء المحسن
  Future<bool> _showPurchaseConfirmationDialog() async {
    if (_selectedSubscriptionId == null) return false;

    // البحث عن الاشتراك في القائمة المتاحة
    final subscription = _availableSubscriptions.firstWhere(
      (sub) => sub.id == _selectedSubscriptionId,
      orElse: () => throw Exception('الاشتراك غير موجود'),
    );

    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            backgroundColor: const Color(0xFF1A1A2E),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
              side: BorderSide(
                color: AppColors.accent.withOpacity(0.3),
                width: 1,
              ),
            ),
            title: Column(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: AppColors.goldGradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.accent.withOpacity(0.3),
                        blurRadius: 15,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.workspace_premium,
                    color: Colors.black,
                    size: 30,
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'تأكيد الاشتراك',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 20,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.accent.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: AppColors.accent.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        subscription.name,
                        style: const TextStyle(
                          color: AppColors.accent,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '${subscription.price} ${subscription.currency}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      Text(
                        subscription.id == 'monthly' ? 'شهرياً' :
                        subscription.id == 'yearly' ? 'سنوياً' : 'مرة واحدة',
                        style: const TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 14,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
                const Text(
                  'ستحصل على جميع المميزات المدفوعة فوراً بعد إتمام عملية الدفع.',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: const Text(
                    '⚠️ ملاحظة: سيتم تجديد الاشتراك تلقائياً. يمكنك إلغاؤه في أي وقت من إعدادات Google Play.',
                    style: TextStyle(
                      color: Colors.orange,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            actions: [
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(false),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                          side: BorderSide(
                            color: AppColors.textSecondary.withOpacity(0.3),
                          ),
                        ),
                      ),
                      child: const Text(
                        'إلغاء',
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(true),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.accent,
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 5,
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.payment, size: 20),
                          SizedBox(width: 8),
                          Text(
                            'متابعة الدفع',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ) ??
        false;
  }

  // عرض شاشة نجاح الشراء المحسنة
  void _showPurchaseSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF1A1A2E),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(24),
          side: BorderSide(
            color: AppColors.accent.withOpacity(0.3),
            width: 2,
          ),
        ),
        title: Column(
          children: [
            // أنيميشن نجاح
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.green, Color(0xFF4CAF50)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.green.withOpacity(0.4),
                    blurRadius: 20,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: const Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 50,
              ),
            )
                .animate()
                .scale(
                  begin: const Offset(0, 0),
                  end: const Offset(1, 1),
                  duration: const Duration(milliseconds: 600),
                  curve: Curves.elasticOut,
                )
                .then()
                .shimmer(
                  duration: const Duration(milliseconds: 1000),
                ),
            const SizedBox(height: 20),
            const Text(
              '🎉 تم الاشتراك بنجاح!',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 22,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.accent.withOpacity(0.1),
                    AppColors.accent.withOpacity(0.05),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.accent.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Column(
                children: [
                  Text(
                    'مرحباً بك في النسخة المميزة!',
                    style: TextStyle(
                      color: AppColors.accent,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'يمكنك الآن الاستمتاع بجميع المميزات الحصرية بدون قيود.',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            // قائمة المميزات المفعلة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Column(
                children: [
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 16),
                      SizedBox(width: 8),
                      Text(
                        'تنزيلات غير محدودة',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 16),
                      SizedBox(width: 8),
                      Text(
                        'إزالة جميع الإعلانات',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                  SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(Icons.check_circle, color: Colors.green, size: 16),
                      SizedBox(width: 8),
                      Text(
                        'خلفيات حصرية جديدة',
                        style: TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          Column(
            children: [
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // العودة للصفحة الرئيسية
                  context.pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.accent,
                  foregroundColor: Colors.black,
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 5,
                ),
                child: const Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.star, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'استمتع بالمميزات الآن',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  'البقاء في صفحة الاشتراكات',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _restoreSubscription() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // إعادة تهيئة خدمة الاشتراك
      await _subscriptionService.initialize();

      // مسح البيانات المحلية قبل الاستعادة
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('subscription');

      // استعادة الاشتراك
      final success = await _subscriptionService.restoreSubscription();

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم استعادة الاشتراك بنجاح!'),
            backgroundColor: AppColors.success,
          ),
        );

        // إعادة تحميل البيانات
        await _loadSubscriptions();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لم يتم العثور على اشتراكات سابقة.'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ خطأ في استعادة الاشتراك: $e');

      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('حدث خطأ أثناء استعادة الاشتراك. حاول مرة أخرى.'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _cancelSubscription() async {
    if (_currentSubscription == null) return;

    // عرض مربع حوار للتأكيد
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.surface,
            title: const Text(
              'إلغاء الاشتراك',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            content: const Text(
              'هل أنت متأكد من رغبتك في إلغاء الاشتراك؟ ستفقد جميع المميزات الحصرية.',
              style: TextStyle(color: Colors.white),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context, true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('تأكيد الإلغاء'),
              ),
            ],
          ),
    );

    if (confirmed != true) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _subscriptionService.cancelSubscription();

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إلغاء الاشتراك بنجاح.'),
            backgroundColor: AppColors.success,
          ),
        );

        // إعادة تحميل البيانات
        await _loadSubscriptions();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء إلغاء الاشتراك. حاول مرة أخرى.'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('حدث خطأ أثناء إلغاء الاشتراك. حاول مرة أخرى.'),
          backgroundColor: AppColors.error,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: const Color(0xFF0F172A),
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF1E293B), Color(0xFF0F172A)],
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(77), // 0.3 opacity
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFF3B82F6).withAlpha(77), // 0.3 opacity
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Text(
                '1:08',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
        actions: [
          GlassmorphicContainer(
            width: 40,
            height: 40,
            borderRadius: 40,
            blur: 20,
            alignment: Alignment.center,
            border: 0,
            linearGradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withAlpha(51), // 0.2 opacity
                Colors.white.withAlpha(26), // 0.1 opacity
              ],
            ),
            borderGradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withAlpha(51), // 0.2 opacity
                Colors.white.withAlpha(26), // 0.1 opacity
              ],
            ),
            child: IconButton(
              icon: const Icon(Icons.arrow_forward, color: Colors.white),
              onPressed: () => context.pop(),
            ),
          ),
          const SizedBox(width: 16),
        ],
        automaticallyImplyLeading: false,
      ),
      body: Stack(
        children: [
          // المحتوى الرئيسي
          _isLoading ? _buildLoadingState() : _buildContent(),

          // زر الاشتراك الثابت
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: _buildFixedSubscribeButton(),
          ),
        ],
      ),
    );
  }

  Widget _buildFixedSubscribeButton() {
    // إذا كان المستخدم مشتركًا بالفعل، لا نعرض زر الاشتراك
    if (_currentSubscription != null && _currentSubscription!.isActive) {
      return const SizedBox.shrink();
    }

    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Color(0xFF0F172A), Color(0xFF0F172A)],
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(128), // 0.5 opacity
            blurRadius: 15,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر الاشتراك المحسن
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(25),
              gradient: _isLoading
                  ? LinearGradient(
                      colors: [
                        AppColors.accent.withOpacity(0.5),
                        AppColors.accent.withOpacity(0.3),
                      ],
                    )
                  : const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: AppColors.goldGradient,
                    ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.accent.withOpacity(0.4),
                  blurRadius: 15,
                  spreadRadius: 3,
                  offset: const Offset(0, 6),
                ),
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 8,
                  spreadRadius: 0,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _isLoading ? null : _purchaseSubscription,
                borderRadius: BorderRadius.circular(25),
                splashColor: Colors.white.withOpacity(0.2),
                highlightColor: Colors.white.withOpacity(0.1),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 18),
                  child: _isLoading
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                strokeWidth: 3,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                              ),
                            ),
                            SizedBox(width: 12),
                            Text(
                              'جاري المعالجة...',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                color: Colors.black.withOpacity(0.2),
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.workspace_premium,
                                color: Colors.black,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'اشترك الآن',
                              style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                                letterSpacing: 0.5,
                              ),
                            ),
                            const SizedBox(width: 8),
                            const Icon(
                              Icons.arrow_forward,
                              color: Colors.black,
                              size: 20,
                            ),
                          ],
                        ),
                ),
              ),
            ),
          )
              .animate()
              .fadeIn(duration: const Duration(milliseconds: 600))
              .scale(
                begin: const Offset(0.9, 0.9),
                end: const Offset(1, 1),
                duration: const Duration(milliseconds: 600),
                curve: Curves.elasticOut,
              )
              .then()
              .shimmer(
                duration: const Duration(milliseconds: 2000),
                curve: Curves.easeInOut,
              )
              .then()
              .custom(
                duration: const Duration(milliseconds: 2000),
                curve: Curves.easeInOut,
                builder: (context, value, child) => Transform.scale(
                  scale: 1.0 + (0.02 * value),
                  child: child,
                ),
              ),

          const SizedBox(height: 12),

          // زر استعادة الاشتراك
          TextButton(
            onPressed: _restoreSubscription,
            style: TextButton.styleFrom(foregroundColor: Colors.blue[300]),
            child: const Text(
              'استعادة الاشتراك',
              style: TextStyle(
                decoration: TextDecoration.underline,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // نص توضيحي
          const Text(
            'ملاحظة: هذا اشتراك تجريبي لأغراض العرض فقط.',
            style: TextStyle(
              fontSize: 10,
              color: AppColors.textMuted,
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: AppColors.backgroundGradient,
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
        ),
      ),
    );
  }

  Widget _buildContent() {
    // إذا كان هناك اشتراك نشط
    if (_currentSubscription != null && _currentSubscription!.isActive) {
      return _buildActiveSubscription();
    }

    // إذا لم يكن هناك اشتراك نشط
    return _buildSubscriptionPlans();
  }

  @override
  void dispose() {
    // تعيين علامة التخلص
    _isDisposed = true;

    // تنظيف متحكمات الأنيميشن
    _pulseController.dispose();
    _shimmerController.dispose();

    // تنظيف خدمة الاشتراك
    _subscriptionService.dispose();

    super.dispose();
  }

  // دالة مساعدة للتحقق من حالة الصفحة
  bool get _isMounted => mounted && !_isDisposed;

  Widget _buildActiveSubscription() {
    final subscription = _currentSubscription!;
    final endDate = subscription.endDate;

    String endDateText = 'مدى الحياة';
    if (endDate != null) {
      // تنسيق التاريخ بشكل أفضل
      final now = DateTime.now();
      final difference = endDate.difference(now).inDays;

      if (difference > 365) {
        // أكثر من سنة
        final years = (difference / 365).floor();
        endDateText = 'بعد $years ${years == 1 ? 'سنة' : 'سنوات'}';
      } else if (difference > 30) {
        // أكثر من شهر
        final months = (difference / 30).floor();
        endDateText = 'بعد $months ${months == 1 ? 'شهر' : 'أشهر'}';
      } else if (difference > 0) {
        // أقل من شهر
        endDateText = 'بعد $difference ${difference == 1 ? 'يوم' : 'أيام'}';
      } else {
        // منتهي الصلاحية
        endDateText = 'منتهي الصلاحية';
      }
    }

    // تحديث حالة الاشتراك في الخدمة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _subscriptionService.initialize();
    });

    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: AppColors.backgroundGradient,
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: AnimateList(
              interval: const Duration(milliseconds: 100),
              effects: [
                const FadeEffect(duration: Duration(milliseconds: 500)),
                const SlideEffect(
                  begin: Offset(0, 30),
                  end: Offset.zero,
                  duration: Duration(milliseconds: 500),
                  curve: Curves.easeOutQuad,
                ),
              ],
              children: [
                // أيقونة النجاح
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: AppColors.accent,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.accent.withAlpha(77), // 0.3 opacity
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.workspace_premium,
                    color: Colors.black,
                    size: 40,
                  ),
                ),

                const SizedBox(height: 24),

                // شارة الاشتراك النشط
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: AppColors.goldGradient,
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.accent.withAlpha(100),
                        blurRadius: 10,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                  child: const Text(
                    'اشتراك نشط',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // عنوان الاشتراك
                Text(
                  'أنت مشترك في ${subscription.name}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 8),

                // تاريخ انتهاء الاشتراك
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.access_time,
                      size: 16,
                      color: AppColors.accent,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      subscription.id == 'lifetime'
                          ? 'اشتراك مدى الحياة'
                          : 'ينتهي $endDateText',
                      style: const TextStyle(
                        fontSize: 16,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // قائمة المميزات
                const Text(
                  'المميزات المتاحة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),

                const SizedBox(height: 16),

                ...subscription.features.map((feature) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Row(
                      children: [
                        Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: AppColors.goldGradient,
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.accent.withAlpha(50),
                                blurRadius: 4,
                                spreadRadius: 0,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.check,
                            color: Colors.black,
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            feature,
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),

                const SizedBox(height: 32),

                // زر إدارة الاشتراك
                ElevatedButton.icon(
                  onPressed: () async {
                    // فتح صفحة إدارة الاشتراكات في Google Play
                    final Uri url = Uri.parse(
                      'https://play.google.com/store/account/subscriptions',
                    );
                    try {
                      await launchUrl(
                        url,
                        mode: LaunchMode.externalApplication,
                      );
                    } catch (e) {
                      debugPrint('❌ خطأ في فتح صفحة إدارة الاشتراكات: $e');
                    }
                  },
                  icon: const Icon(Icons.settings),
                  label: const Text('إدارة الاشتراك'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.white.withAlpha(50),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 24,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),

                const Spacer(),

                // زر إلغاء الاشتراك
                OutlinedButton.icon(
                  onPressed: _cancelSubscription,
                  icon: const Icon(Icons.cancel),
                  label: const Text('إلغاء الاشتراك'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.error,
                    side: const BorderSide(color: AppColors.error),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // نص توضيحي
                const Text(
                  'ملاحظة: هذا اشتراك تجريبي لأغراض العرض فقط. في التطبيق الحقيقي، سيتم استخدام Google Play Billing.',
                  style: TextStyle(
                    fontSize: 12,
                    color: AppColors.textMuted,
                    fontStyle: FontStyle.italic,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubscriptionPlans() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: AppColors.backgroundGradient,
        ),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: AnimateList(
                interval: const Duration(milliseconds: 100),
                effects: [
                  const FadeEffect(duration: Duration(milliseconds: 500)),
                  const SlideEffect(
                    begin: Offset(0, 30),
                    end: Offset.zero,
                    duration: Duration(milliseconds: 500),
                    curve: Curves.easeOutQuad,
                  ),
                ],
                children: [
                  // عنوان الصفحة
                  const Text(
                    'النسخة المدفوعة',
                    style: TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // وصف الصفحة
                  const Text(
                    'احصل على مميزات حصرية وخلفيات إضافية',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.textSecondary,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // قائمة المميزات
                  const Text(
                    'مميزات النسخة المدفوعة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),

                  const SizedBox(height: 12),

                  _buildFeatureItem('تنزيلات غير محدودة'),
                  _buildFeatureItem('إزالة الإعلانات'),
                  _buildFeatureItem('خلفيات حصرية'),
                  _buildFeatureItem('تحديثات أسبوعية'),
                  _buildFeatureItem('دعم المطورين'),

                  const SizedBox(height: 24),

                  // قائمة خطط الاشتراك
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      color: Color.fromRGBO(20, 20, 40, 0.7),
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: Color.fromRGBO(255, 215, 0, 0.3),
                        width: 1,
                      ),
                    ),
                    child: const Text(
                      'اختر خطة الاشتراك',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // قائمة الاشتراكات
                  ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _availableSubscriptions.length,
                    itemBuilder: (context, index) {
                      final subscription = _availableSubscriptions[index];
                      final isSelected =
                          _selectedSubscriptionId == subscription.id;

                      return _buildSubscriptionCard(
                        subscription: subscription,
                        isSelected: isSelected,
                        onTap: () {
                          setState(() {
                            _selectedSubscriptionId = subscription.id;
                          });
                        },
                      );
                    },
                  ),

                  const SizedBox(height: 20),

                  // زر استعادة الاشتراك
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    child: TextButton(
                      onPressed: _restoreSubscription,
                      style: TextButton.styleFrom(
                        minimumSize: const Size(double.infinity, 40),
                        foregroundColor: Colors.blue[300],
                      ),
                      child: const Text(
                        'استعادة الاشتراك',
                        style: TextStyle(
                          decoration: TextDecoration.underline,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // نص توضيحي
                  Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    child: const Text(
                      'ملاحظة: هذا اشتراك تجريبي لأغراض العرض فقط. في التطبيق الحقيقي، سيتم استخدام Google Play Billing.',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.textMuted,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: AppColors.accent,
              borderRadius: BorderRadius.circular(14),
              boxShadow: [
                BoxShadow(
                  color: Color.fromRGBO(255, 215, 0, 0.2),
                  blurRadius: 6,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: const Icon(Icons.check, color: Colors.black, size: 18),
          ),
          const SizedBox(width: 14),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionCard({
    required Subscription subscription,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    // تحديد الأيقونة المناسبة لكل نوع اشتراك
    IconData subscriptionIcon;
    Color iconColor;
    if (subscription.id == 'monthly') {
      subscriptionIcon = Icons.calendar_month;
      iconColor = Colors.blue;
    } else if (subscription.id == 'yearly') {
      subscriptionIcon = Icons.calendar_today;
      iconColor = Colors.green;
    } else {
      subscriptionIcon = Icons.all_inclusive;
      iconColor = AppColors.accent;
    }

    // حساب التوفير للاشتراك السنوي
    String? savingsText;
    if (subscription.id == 'yearly') {
      savingsText = 'توفير 33%';
    }

    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            // بطاقة الاشتراك المحسنة
            Container(
              margin: const EdgeInsets.only(bottom: 20, top: 15),
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: isSelected ? AppColors.accent : Colors.white.withOpacity(0.1),
                  width: isSelected ? 3 : 1,
                ),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: isSelected
                      ? [
                          const Color(0xFF2A2A4A),
                          const Color(0xFF1A1A3A),
                        ]
                      : [
                          const Color(0xFF1E1E3E),
                          const Color(0xFF141428),
                        ],
                ),
                boxShadow: [
                  if (isSelected)
                    BoxShadow(
                      color: AppColors.accent.withOpacity(0.4),
                      blurRadius: 20,
                      spreadRadius: 2,
                      offset: const Offset(0, 8),
                    ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 10,
                    spreadRadius: 0,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // الصف العلوي - الأيقونة والاسم والسعر
                  Row(
                    children: [
                      // أيقونة الاشتراك المحسنة
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: isSelected
                                ? AppColors.goldGradient
                                : [iconColor.withOpacity(0.8), iconColor],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: (isSelected ? AppColors.accent : iconColor)
                                  .withOpacity(0.3),
                              blurRadius: 8,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                        child: Icon(
                          subscriptionIcon,
                          color: isSelected ? Colors.black : Colors.white,
                          size: 24,
                        ),
                      ),

                      const SizedBox(width: 16),

                      // اسم الاشتراك والوصف
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              subscription.name,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? AppColors.accent : Colors.white,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              subscription.description,
                              style: const TextStyle(
                                fontSize: 13,
                                color: AppColors.textSecondary,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),

                      // السعر المحسن
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: isSelected
                                ? AppColors.goldGradient
                                : [
                                    Colors.white.withOpacity(0.1),
                                    Colors.white.withOpacity(0.05),
                                  ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected
                                ? AppColors.accent.withOpacity(0.5)
                                : Colors.white.withOpacity(0.2),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              '${subscription.price}',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.black : Colors.white,
                              ),
                            ),
                            Text(
                              subscription.currency,
                              style: TextStyle(
                                fontSize: 12,
                                color: isSelected
                                    ? Colors.black.withOpacity(0.7)
                                    : AppColors.textSecondary,
                              ),
                            ),
                            Text(
                              subscription.id == 'monthly'
                                  ? 'شهرياً'
                                  : subscription.id == 'yearly'
                                      ? 'سنوياً'
                                      : 'مرة واحدة',
                              style: TextStyle(
                                fontSize: 10,
                                color: isSelected
                                    ? Colors.black.withOpacity(0.6)
                                    : AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // المميزات المحسنة
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.accent.withOpacity(0.1)
                          : Colors.white.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isSelected
                            ? AppColors.accent.withOpacity(0.3)
                            : Colors.white.withOpacity(0.1),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        // عرض أول 3 مميزات
                        ...subscription.features.take(3).map((feature) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: Row(
                              children: [
                                Container(
                                  width: 20,
                                  height: 20,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: isSelected
                                          ? AppColors.goldGradient
                                          : [Colors.green, Colors.green.shade700],
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                    ),
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.check,
                                    color: Colors.white,
                                    size: 12,
                                  ),
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Text(
                                    feature,
                                    style: TextStyle(
                                      fontSize: 13,
                                      color: isSelected
                                          ? Colors.white
                                          : AppColors.textSecondary,
                                      fontWeight: isSelected
                                          ? FontWeight.w500
                                          : FontWeight.normal,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),

                        // عرض عدد المميزات المتبقية
                        if (subscription.features.length > 3)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.accent.withOpacity(0.2)
                                  : Colors.blue.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              '+ ${subscription.features.length - 3} ميزات إضافية',
                              style: TextStyle(
                                fontSize: 11,
                                color: isSelected ? AppColors.accent : Colors.blue,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
          ),

            // شارة موصى به المحسنة
            if (subscription.isRecommended)
              Positioned(
                top: -5,
                right: -5,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFFFF6B35), Color(0xFFFF8E53)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(15),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFFFF6B35).withOpacity(0.4),
                        blurRadius: 10,
                        spreadRadius: 2,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: const Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.star, color: Colors.white, size: 16),
                      SizedBox(width: 4),
                      Text(
                        'الأفضل',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              )
                  .animate()
                  .scale(
                    begin: const Offset(0, 0),
                    end: const Offset(1, 1),
                    duration: const Duration(milliseconds: 400),
                    delay: const Duration(milliseconds: 200),
                    curve: Curves.elasticOut,
                  )
                  .then()
                  .shimmer(
                    duration: const Duration(milliseconds: 1500),
                  ),

            // شارة التوفير المحسنة
            if (savingsText != null)
              Positioned(
                bottom: 5,
                left: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Color(0xFF4CAF50), Color(0xFF45A049)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFF4CAF50).withOpacity(0.3),
                        blurRadius: 8,
                        spreadRadius: 1,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.savings,
                        color: Colors.white,
                        size: 14,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        savingsText,
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              )
                  .animate()
                  .slideX(
                    begin: -1,
                    end: 0,
                    duration: const Duration(milliseconds: 500),
                    delay: const Duration(milliseconds: 300),
                    curve: Curves.easeOutBack,
                  ),
        ],
      ),
    );
  }
}
