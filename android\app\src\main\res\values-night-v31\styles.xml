<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is on -->
    <style name="LaunchTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <!-- تعيين مدة ظهور شاشة البداية إلى 0 مللي ثانية -->
        <item name="android:windowSplashScreenAnimationDuration">0</item>
        <!-- تعطيل الرسوم المتحركة للانتقال -->
        <item name="android:windowAnimationStyle">@null</item>
        <!-- تعطيل صورة العلامة التجارية في شاشة البداية -->
        <!-- تعيين سلوك شاشة البداية لإظهار الأيقونة دائماً -->
        <item name="android:windowSplashScreenBehavior">icon_preferred</item>
        <!-- تعيين خلفية شاشة البداية -->
        <!-- تعيين أيقونة شاشة البداية -->
        <!-- تعيين لون خلفية أيقونة شاشة البداية -->
        <!-- إعدادات عامة -->
        <!-- إزالة الأيقونة لجعل الشاشة سوداء فقط -->
        <item name="android:forceDarkAllowed">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <item name="android:windowSplashScreenBackground">#000000</item>
        <item name="android:windowSplashScreenIconBackgroundColor">#000000</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Black.NoTitleBar">
        <item name="android:windowBackground">#000000</item>
        <item name="android:windowSplashScreenBackground">#000000</item>
        <item name="android:windowSplashScreenAnimatedIcon">@drawable/android12splash</item>
        <item name="android:windowSplashScreenIconBackgroundColor">#000000</item>
        <item name="android:windowSplashScreenBrandingImage">@null</item>
        <item name="android:windowSplashScreenAnimationDuration">0</item>
        <item name="android:enforceNavigationBarContrast">false</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowContentTransitions">false</item>
        <item name="android:windowActivityTransitions">false</item>
        <item name="android:windowNoDisplay">false</item>
    </style>
</resources>
