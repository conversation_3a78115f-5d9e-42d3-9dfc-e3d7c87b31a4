import 'package:flutter/material.dart';
import 'colors.dart';

/// نظام الظلال الموحد للتطبيق
class AppShadows {
  // ظل خفيف للعناصر المرتفعة قليلاً
  static List<BoxShadow> get small => [
    BoxShadow(
      color: AppColors.shadowLight,
      blurRadius: 4,
      offset: const Offset(0, 2),
    ),
  ];

  // ظل متوسط للبطاقات والأزرار
  static List<BoxShadow> get medium => [
    BoxShadow(
      color: AppColors.shadowLight,
      blurRadius: 8,
      offset: const Offset(0, 4),
    ),
  ];

  // ظل كبير للعناصر البارزة
  static List<BoxShadow> get large => [
    BoxShadow(
      color: AppColors.shadowMedium,
      blurRadius: 16,
      offset: const Offset(0, 8),
    ),
  ];

  // ظل كبير جدًا للنوافذ المنبثقة
  static List<BoxShadow> get xLarge => [
    BoxShadow(
      color: AppColors.shadowMedium,
      blurRadius: 24,
      offset: const Offset(0, 12),
    ),
  ];

  // ظل للعناصر المميزة مثل الأزرار الذهبية
  static List<BoxShadow> get premium => [
    BoxShadow(
      color: AppColors.premiumColor.withOpacity(0.3),
      blurRadius: 16,
      spreadRadius: 2,
      offset: const Offset(0, 4),
    ),
    BoxShadow(
      color: AppColors.shadowMedium,
      blurRadius: 8,
      offset: const Offset(0, 2),
    ),
  ];

  // ظل للعناصر الأساسية مثل الأزرار الزرقاء
  static List<BoxShadow> get primary => [
    BoxShadow(
      color: AppColors.primary.withOpacity(0.3),
      blurRadius: 16,
      spreadRadius: 2,
      offset: const Offset(0, 4),
    ),
    BoxShadow(
      color: AppColors.shadowMedium,
      blurRadius: 8,
      offset: const Offset(0, 2),
    ),
  ];

  // ظل للعناصر الثانوية مثل الأزرار الخضراء
  static List<BoxShadow> get secondary => [
    BoxShadow(
      color: AppColors.secondary.withOpacity(0.3),
      blurRadius: 16,
      spreadRadius: 2,
      offset: const Offset(0, 4),
    ),
    BoxShadow(
      color: AppColors.shadowMedium,
      blurRadius: 8,
      offset: const Offset(0, 2),
    ),
  ];

  // ظل للبطاقات
  static List<BoxShadow> get card => [
    BoxShadow(
      color: AppColors.shadowLight,
      blurRadius: 12,
      offset: const Offset(0, 6),
    ),
  ];

  // ظل للعناصر المرتفعة مثل شريط التنقل
  static List<BoxShadow> get elevated => [
    BoxShadow(
      color: AppColors.shadowDark,
      blurRadius: 8,
      offset: const Offset(0, -2),
    ),
  ];

  // ظل للنصوص الكبيرة
  static List<Shadow> get textMedium => [
    Shadow(
      color: Colors.black.withAlpha(128),
      blurRadius: 3,
      offset: const Offset(0, 1),
    ),
  ];

  // ظل للنصوص الصغيرة
  static List<Shadow> get textSmall => [
    Shadow(
      color: Colors.black.withAlpha(102),
      blurRadius: 2,
      offset: const Offset(0, 1),
    ),
  ];
}
