import 'package:flutter/material.dart';
import '../../../../design_system/colors.dart';

class PremiumFeatureItem extends StatelessWidget {
  final String title;
  final String? description;
  final bool isActive;
  
  const PremiumFeatureItem({
    Key? key,
    required this.title,
    this.description,
    this.isActive = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          // أيقونة الميزة
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: isActive 
                  ? AppColors.accent.withAlpha(50)
                  : AppColors.primary.withAlpha(30),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                isActive ? Icons.check : Icons.star,
                color: isActive ? AppColors.accent : AppColors.primary,
                size: 20,
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // معلومات الميزة
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                if (description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    description!,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white60,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
