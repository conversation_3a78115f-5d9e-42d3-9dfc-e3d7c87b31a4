package com.islamiclandporta.islam.allahwallpaper.ahmad.np;

import android.app.Activity;
import android.app.WallpaperManager;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Build;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;

import java.io.IOException;

import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

public class WallpaperMethodChannel implements MethodChannel.MethodCallHandler {
    private static final String TAG = "WallpaperMethodChannel";
    private static final String CHANNEL = "com.islamiclandporta.islam.allahwallpaper.ahmad.np/wallpaper";
    private final Activity activity;
    private final Context context;

    public WallpaperMethodChannel(Activity activity, Context context) {
        this.activity = activity;
        this.context = context;
    }

    public static void registerWith(FlutterEngine flutterEngine, Activity activity) {
        MethodChannel channel = new MethodChannel(flutterEngine.getDartExecutor().getBinaryMessenger(), CHANNEL);
        channel.setMethodCallHandler(new WallpaperMethodChannel(activity, activity.getApplicationContext()));
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
        // التحقق من صحة المعاملات لتجنب IllegalArgumentException
        if (call == null || result == null) {
            Log.e(TAG, "MethodCall أو Result null في WallpaperMethodChannel");
            return;
        }

        // تنفيذ العمليات في خيط منفصل لتجنب ANR
        new Thread(() -> {
            android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());

            try {
                String method = call.method;
                if (method == null) {
                    mainHandler.post(() -> {
                        try {
                            result.error("INVALID_METHOD", "اسم الطريقة مفقود", null);
                        } catch (Exception e) {
                            Log.e(TAG, "خطأ في إرجاع خطأ الطريقة المفقودة: " + e.getMessage());
                        }
                    });
                    return;
                }

                switch (method) {
                    case "initialize":
                        // إرجاع النتيجة في الخيط الرئيسي مع معالجة الأخطاء
                        mainHandler.post(() -> {
                            try {
                                result.success(true);
                            } catch (Exception e) {
                                Log.e(TAG, "خطأ في إرجاع نتيجة التهيئة: " + e.getMessage());
                            }
                        });
                        break;

                    case "setHomeScreenWallpaper":
                        try {
                            Object imageBytesObj = call.argument("imageBytes");
                            if (imageBytesObj == null) {
                                mainHandler.post(() -> {
                                    try {
                                        result.error("INVALID_ARGUMENT", "بيانات الصورة مفقودة", null);
                                    } catch (Exception e) {
                                        Log.e(TAG, "خطأ في إرجاع خطأ البيانات المفقودة: " + e.getMessage());
                                    }
                                });
                                return;
                            }

                            byte[] homeImageBytes = (byte[]) imageBytesObj;
                            if (homeImageBytes.length == 0) {
                                mainHandler.post(() -> {
                                    try {
                                        result.error("INVALID_ARGUMENT", "بيانات الصورة فارغة", null);
                                    } catch (Exception e) {
                                        Log.e(TAG, "خطأ في إرجاع خطأ البيانات الفارغة: " + e.getMessage());
                                    }
                                });
                                return;
                            }

                            boolean homeSuccess = setHomeScreenWallpaper(homeImageBytes);
                            mainHandler.post(() -> {
                                try {
                                    result.success(homeSuccess);
                                } catch (Exception e) {
                                    Log.e(TAG, "خطأ في إرجاع نتيجة تعيين الخلفية: " + e.getMessage());
                                }
                            });
                        } catch (ClassCastException e) {
                            Log.e(TAG, "خطأ في تحويل بيانات الصورة: " + e.getMessage());
                            mainHandler.post(() -> {
                                try {
                                    result.error("TYPE_ERROR", "نوع بيانات الصورة غير صحيح", null);
                                } catch (Exception resultError) {
                                    Log.e(TAG, "خطأ في إرجاع خطأ النوع: " + resultError.getMessage());
                                }
                            });
                        }
                        break;
                    case "setLockScreenWallpaper":
                        byte[] lockImageBytes = call.argument("imageBytes");
                        boolean lockSuccess = setLockScreenWallpaper(lockImageBytes);
                        new android.os.Handler(android.os.Looper.getMainLooper()).post(() -> {
                            result.success(lockSuccess);
                        });
                        break;
                    case "setBothScreensWallpaper":
                        byte[] bothImageBytes = call.argument("imageBytes");
                        boolean bothSuccess = setBothScreensWallpaper(bothImageBytes);
                        new android.os.Handler(android.os.Looper.getMainLooper()).post(() -> {
                            result.success(bothSuccess);
                        });
                        break;
                    default:
                        new android.os.Handler(android.os.Looper.getMainLooper()).post(() -> {
                            result.notImplemented();
                        });
                        break;
                }
            } catch (Exception e) {
                android.util.Log.e("WallpaperMethodChannel", "خطأ في معالجة استدعاء الطريقة: " + e.getMessage());
                new android.os.Handler(android.os.Looper.getMainLooper()).post(() -> {
                    result.error("EXECUTION_ERROR", "خطأ في تنفيذ العملية: " + e.getMessage(), null);
                });
            }
        }).start();
    }

    // تعيين خلفية الشاشة الرئيسية
    private boolean setHomeScreenWallpaper(byte[] imageBytes) {
        try {
            if (imageBytes == null) {
                showToast("Error: Image data is null");
                return false;
            }

            Bitmap bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
            WallpaperManager manager = WallpaperManager.getInstance(context);

            if (bitmap != null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                    manager.setBitmap(bitmap, null, true, WallpaperManager.FLAG_SYSTEM);
                } else {
                    manager.setBitmap(bitmap);
                }
                showToast("تم تعيين خلفية الشاشة الرئيسية بنجاح");
                return true;
            } else {
                showToast("Error: Failed to create bitmap");
                return false;
            }
        } catch (IOException e) {
            e.printStackTrace();
            showToast("Error setting home screen wallpaper: " + e.getMessage());
            return false;
        }
    }

    // تعيين خلفية شاشة القفل
    private boolean setLockScreenWallpaper(byte[] imageBytes) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                if (imageBytes == null) {
                    showToast("Error: Image data is null");
                    return false;
                }

                Bitmap bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
                WallpaperManager manager = WallpaperManager.getInstance(context);

                if (bitmap != null) {
                    manager.setBitmap(bitmap, null, true, WallpaperManager.FLAG_LOCK);
                    showToast("تم تعيين خلفية شاشة القفل بنجاح");
                    return true;
                } else {
                    showToast("Error: Failed to create bitmap");
                    return false;
                }
            } else {
                showToast("تعيين خلفية شاشة القفل غير مدعوم في هذا الإصدار من أندرويد");
                return false;
            }
        } catch (IOException e) {
            e.printStackTrace();
            showToast("Error setting lock screen wallpaper: " + e.getMessage());
            return false;
        }
    }

    // تعيين خلفية الشاشة الرئيسية وشاشة القفل معًا
    private boolean setBothScreensWallpaper(byte[] imageBytes) {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                if (imageBytes == null) {
                    showToast("Error: Image data is null");
                    return false;
                }

                Bitmap bitmap = BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
                WallpaperManager manager = WallpaperManager.getInstance(context);

                if (bitmap != null) {
                    manager.setBitmap(bitmap, null, true, WallpaperManager.FLAG_SYSTEM | WallpaperManager.FLAG_LOCK);
                    showToast("تم تعيين الخلفية للشاشتين بنجاح");
                    return true;
                } else {
                    showToast("Error: Failed to create bitmap");
                    return false;
                }
            } else {
                // في الإصدارات القديمة، نقوم بتعيين الخلفية للشاشة الرئيسية فقط
                return setHomeScreenWallpaper(imageBytes);
            }
        } catch (IOException e) {
            e.printStackTrace();
            showToast("Error setting both screens wallpaper: " + e.getMessage());
            return false;
        }
    }

    // عرض رسالة للمستخدم
    private void showToast(String message) {
        activity.runOnUiThread(() -> Toast.makeText(context, message, Toast.LENGTH_SHORT).show());
    }
}
