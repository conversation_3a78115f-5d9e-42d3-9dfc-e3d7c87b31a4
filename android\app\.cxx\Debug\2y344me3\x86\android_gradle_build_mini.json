{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\AndroidStudioProjects\\Slmeh.Pro\\android\\app\\.cxx\\Debug\\2y344me3\\x86", "clean"]], "buildTargetsCommandComponents": ["F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\AndroidStudioProjects\\Slmeh.Pro\\android\\app\\.cxx\\Debug\\2y344me3\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}