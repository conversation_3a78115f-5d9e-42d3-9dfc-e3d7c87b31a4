import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// دالة مساعدة لتحليل اللون من النص
Color _parseColor(String colorString) {
  try {
    // إذا كان النص يبدأ بـ 0x أو 0X أو #
    if (colorString.startsWith('0x') ||
        colorString.startsWith('0X') ||
        colorString.startsWith('#')) {
      // إزالة # إذا وجدت
      if (colorString.startsWith('#')) {
        colorString = '0xFF${colorString.substring(1)}';
      }

      // تحويل النص إلى رقم
      return Color(int.parse(colorString));
    }
    // إذا كان النص يحتوي على Color
    else if (colorString.contains('Color(')) {
      // استخراج قيمة اللون من النص
      final regex = RegExp(
        r'Color\(.*?(\d+\.\d+).*?(\d+\.\d+).*?(\d+\.\d+).*?(\d+\.\d+)',
      );
      final match = regex.firstMatch(colorString);

      if (match != null && match.groupCount >= 4) {
        final alpha = double.parse(match.group(1)!) * 255;
        final red = double.parse(match.group(2)!) * 255;
        final green = double.parse(match.group(3)!) * 255;
        final blue = double.parse(match.group(4)!) * 255;

        return Color.fromARGB(
          alpha.round(),
          red.round(),
          green.round(),
          blue.round(),
        );
      }
    }

    // محاولة تحليل النص كرقم سداسي عشري
    return Color(int.parse(colorString, radix: 16));
  } catch (e) {
    debugPrint('خطأ في تحليل اللون: $e');
    return Colors.blue; // لون افتراضي في حالة الخطأ
  }
}

/// امتداد لتحويل اللون إلى تنسيق سداسي عشري
extension ColorExtension on Color {
  String toHex() {
    // إضافة 0xFF في البداية لتحديد قيمة ألفا كاملة
    return '0xFF${r.round().toRadixString(16).padLeft(2, '0')}'
        '${g.round().toRadixString(16).padLeft(2, '0')}'
        '${b.round().toRadixString(16).padLeft(2, '0')}';
  }
}

/// نموذج بيانات الخلفية
class Wallpaper extends Equatable {
  final String id;
  final String title;
  final String imageUrl;
  final String thumbnailUrl;
  final String category;
  final List<String> tags;
  final bool isFeatured;
  final bool isPremium;
  final int downloads;
  final int likes;
  final int views;
  final DateTime createdAt;
  final String resolution;
  final int fileSize; // بالكيلوبايت
  final String? blurHash;
  final Color? dominantColor;

  Wallpaper({
    required this.id,
    this.title = '',
    required this.imageUrl,
    required this.thumbnailUrl,
    this.category = 'إسلامية',
    this.tags = const [],
    this.isFeatured = false,
    this.isPremium = false,
    this.downloads = 0,
    this.likes = 0,
    this.views = 0,
    DateTime? createdAt,
    this.resolution = '',
    this.fileSize = 0,
    this.blurHash,
    this.dominantColor,
  }) : createdAt = createdAt ?? DateTime.now();

  factory Wallpaper.fromJson(Map<String, dynamic> json) {
    return Wallpaper(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      thumbnailUrl: json['thumbnailUrl'] ?? json['imageUrl'] ?? '',
      category: json['category'] ?? 'إسلامية',
      tags: List<String>.from(json['tags'] ?? []),
      isFeatured: json['isFeatured'] ?? false,
      isPremium: json['isPremium'] ?? false,
      downloads: json['downloads'] ?? 0,
      likes: json['likes'] ?? 0,
      views: json['views'] ?? 0,
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'])
              : DateTime.now(),
      resolution: json['resolution'] ?? '',
      fileSize: json['fileSize'] ?? 0,
      blurHash: json['blurHash'],
      dominantColor:
          json['dominantColor'] != null
              ? _parseColor(json['dominantColor'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'imageUrl': imageUrl,
      'thumbnailUrl': thumbnailUrl,
      'category': category,
      'tags': tags,
      'isFeatured': isFeatured,
      'isPremium': isPremium,
      'downloads': downloads,
      'likes': likes,
      'views': views,
      'createdAt': createdAt.toIso8601String(),
      'resolution': resolution,
      'fileSize': fileSize,
      'blurHash': blurHash,
      'dominantColor': dominantColor?.toHex(),
    };
  }

  Wallpaper copyWith({
    String? id,
    String? title,
    String? imageUrl,
    String? thumbnailUrl,
    String? category,
    List<String>? tags,
    bool? isFeatured,
    bool? isPremium,
    int? downloads,
    int? likes,
    int? views,
    DateTime? createdAt,
    String? resolution,
    int? fileSize,
    String? blurHash,
    Color? dominantColor,
  }) {
    return Wallpaper(
      id: id ?? this.id,
      title: title ?? this.title,
      imageUrl: imageUrl ?? this.imageUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      category: category ?? this.category,
      tags: tags ?? this.tags,
      isFeatured: isFeatured ?? this.isFeatured,
      isPremium: isPremium ?? this.isPremium,
      downloads: downloads ?? this.downloads,
      likes: likes ?? this.likes,
      views: views ?? this.views,
      createdAt: createdAt ?? this.createdAt,
      resolution: resolution ?? this.resolution,
      fileSize: fileSize ?? this.fileSize,
      blurHash: blurHash ?? this.blurHash,
      dominantColor: dominantColor ?? this.dominantColor,
    );
  }

  @override
  List<Object?> get props => [
    id,
    title,
    imageUrl,
    thumbnailUrl,
    category,
    tags,
    isFeatured,
    isPremium,
    downloads,
    likes,
    views,
    createdAt,
    resolution,
    fileSize,
    blurHash,
    dominantColor,
  ];
}

/// نموذج بيانات الفئة
class Category extends Equatable {
  final String id;
  final String name;
  final String imageUrl;
  final String description;
  final int wallpaperCount;
  final Color color;

  const Category({
    required this.id,
    required this.name,
    required this.imageUrl,
    this.description = '',
    this.wallpaperCount = 0,
    this.color = Colors.blue,
  });

  factory Category.fromJson(Map<String, dynamic> json) {
    return Category(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      imageUrl: json['imageUrl'] ?? '',
      description: json['description'] ?? '',
      wallpaperCount: json['wallpaperCount'] ?? 0,
      color: json['color'] != null ? _parseColor(json['color']) : Colors.blue,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'imageUrl': imageUrl,
      'description': description,
      'wallpaperCount': wallpaperCount,
      'color': color.toHex(),
    };
  }

  @override
  List<Object> get props => [
    id,
    name,
    imageUrl,
    description,
    wallpaperCount,
    color,
  ];
}
