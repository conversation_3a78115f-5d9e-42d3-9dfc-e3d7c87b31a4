import 'dart:convert';

/// نموذج بيانات للتحكم عن بعد في التطبيق
/// يحتوي فقط على إعدادات التشغيل/الإيقاف للإعلانات
class RemoteConfig {
  final String appVersion;
  final bool showBannerAds;
  final bool showInterstitialAds;
  final bool showRewardedAds;
  final String message;
  final bool forceUpdate;
  final String updateUrl;
  final Map<String, dynamic> additionalSettings;

  // حالة تقييد الإعلانات
  final bool adRestriction;
  final DateTime? adRestrictionEndDate;
  final String adRestrictionReason;
  final String alternativeAdNetwork;

  RemoteConfig({
    required this.appVersion,
    required this.showBannerAds,
    required this.showInterstitialAds,
    required this.showRewardedAds,
    required this.message,
    required this.forceUpdate,
    required this.updateUrl,
    required this.additionalSettings,
    required this.adRestriction,
    this.adRestrictionEndDate,
    required this.adRestrictionReason,
    required this.alternativeAdNetwork,
  });

  factory RemoteConfig.fromJson(Map<String, dynamic> json) {
    final additionalSettings = json['additional_settings'] ?? {};

    // لا نجلب معرفات الإعلانات من الخادم - نستخدم المعرفات الثابتة في الكود

    // استخراج معلومات تقييد الإعلانات
    final bool adRestriction = additionalSettings['ad_restriction'] == 'ON';

    DateTime? adRestrictionEndDate;
    if (additionalSettings.containsKey('ad_restriction_end_date')) {
      final endDateString = additionalSettings['ad_restriction_end_date'];
      if (endDateString != null && endDateString.isNotEmpty) {
        try {
          adRestrictionEndDate = DateTime.parse(endDateString);
        } catch (e) {
          // استخدام طريقة آمنة للتسجيل بدلاً من print
        }
      }
    }

    final String adRestrictionReason =
        additionalSettings['ad_restriction_reason'] ?? '';
    final String alternativeAdNetwork =
        additionalSettings['alternative_ad_network'] ?? 'facebook';

    return RemoteConfig(
      appVersion: json['app_version'] ?? '1.0.0',
      showBannerAds: json['show_banner_ads'] == 'ON',
      showInterstitialAds: json['show_interstitial_ads'] == 'ON',
      showRewardedAds: json['show_rewarded_ads'] == 'ON',
      message: json['message'] ?? '',
      forceUpdate: json['force_update'] == 'ON',
      updateUrl:
          json['update_url'] ??
          'https://play.google.com/store/apps/details?id=com.islamiclandporta.islam.allahwallpaper.ahmad.np',
      additionalSettings: additionalSettings,
      // معلومات تقييد الإعلانات
      adRestriction: adRestriction,
      adRestrictionEndDate: adRestrictionEndDate,
      adRestrictionReason: adRestrictionReason,
      alternativeAdNetwork: alternativeAdNetwork,
    );
  }

  factory RemoteConfig.defaultConfig() {
    return RemoteConfig(
      appVersion: '1.0.0',
      showBannerAds: true,
      showInterstitialAds: true,
      showRewardedAds: true,
      message: '',
      forceUpdate: false,
      updateUrl:
          'https://play.google.com/store/apps/details?id=com.islamiclandporta.islam.allahwallpaper.ahmad.np',
      additionalSettings: {},
      // معلومات تقييد الإعلانات الافتراضية
      adRestriction: false,
      adRestrictionEndDate: null,
      adRestrictionReason: '',
      alternativeAdNetwork: 'facebook',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'app_version': appVersion,
      'show_banner_ads': showBannerAds ? 'ON' : 'OFF',
      'show_interstitial_ads': showInterstitialAds ? 'ON' : 'OFF',
      'show_rewarded_ads': showRewardedAds ? 'ON' : 'OFF',
      'message': message,
      'force_update': forceUpdate ? 'ON' : 'OFF',
      'update_url': updateUrl,
      // نحتفظ فقط بالإعدادات الإضافية (بدون معرفات الإعلانات)
      'additional_settings': {
        // نحفظ معلومات تقييد الإعلانات
        'ad_restriction': adRestriction ? 'ON' : 'OFF',
        'ad_restriction_reason': adRestrictionReason,
        'alternative_ad_network': alternativeAdNetwork,
        // نحفظ تاريخ انتهاء التقييد إذا كان موجودًا
        if (adRestrictionEndDate != null)
          'ad_restriction_end_date': adRestrictionEndDate!.toIso8601String(),
      },
    };
  }

  @override
  String toString() {
    return jsonEncode(toJson());
  }
}
