import 'package:flutter/material.dart';
import '../../../data/services/ad_service.dart';

/// مدير إعلانات فتح التطبيق
/// يستخدم لعرض إعلانات عند فتح التطبيق
class AppOpenAdManager {
  static final AppOpenAdManager _instance = AppOpenAdManager._internal();
  final AdService _adService = AdService();
  
  // متغيرات للتحكم في عرض الإعلانات
  bool _isInitialized = false;
  DateTime? _lastAdShownTime;
  
  // الحصول على نسخة واحدة من المدير (Singleton)
  factory AppOpenAdManager() {
    return _instance;
  }
  
  AppOpenAdManager._internal();
  
  /// تهيئة مدير إعلانات فتح التطبيق
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // تهيئة خدمة الإعلانات
      await _adService.initialize();
      
      _isInitialized = true;
      debugPrint('✅ تم تهيئة مدير إعلانات فتح التطبيق بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مدير إعلانات فتح التطبيق: $e');
    }
  }
  
  /// عرض إعلان فتح التطبيق
  /// يتم استدعاء هذه الدالة عند فتح التطبيق
  Future<bool> showAdOnAppOpen() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    // التحقق من الوقت المنقضي منذ آخر إعلان
    final now = DateTime.now();
    if (_lastAdShownTime != null) {
      final difference = now.difference(_lastAdShownTime!);
      // عدم عرض الإعلان إذا لم يمر 4 ساعات على الأقل
      if (difference.inHours < 4) {
        return false;
      }
    }
    
    final result = await _adService.showAppOpenAd();
    if (result) {
      _lastAdShownTime = now;
    }
    
    return result;
  }
  
  /// عرض إعلان فتح التطبيق بشكل إجباري
  /// يتم استدعاء هذه الدالة عند الحاجة لعرض إعلان بشكل إجباري
  Future<bool> showAdForced() async {
    if (!_isInitialized) {
      await initialize();
    }
    
    final result = await _adService.showAppOpenAd();
    if (result) {
      _lastAdShownTime = DateTime.now();
    }
    
    return result;
  }
}
