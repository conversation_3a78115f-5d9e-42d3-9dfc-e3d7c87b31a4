import 'dart:isolate';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'app/app.dart';
import 'services/app_initialization_service.dart';
import 'utils/performance_optimizer.dart';

/// تهيئة إعدادات واجهة المستخدم الأساسية
void _initUISettings() {
  // تعيين اتجاه التطبيق للوضع العمودي فقط (بدون انتظار)
  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // تعيين لون شريط الحالة والتنقل بشكل ثابت
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent, // شفاف لشريط الحالة
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.transparent, // شفاف لشريط التنقل
      systemNavigationBarIconBrightness: Brightness.light,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarContrastEnforced:
          false, // تعطيل تباين الألوان الإجباري
    ),
  );

  // تعيين وضع واجهة المستخدم النظامية للسماح بالسحب لإظهار شريط التنقل
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
    overlays: [SystemUiOverlay.top],
  );
}

// تهيئة التطبيق بأقصى سرعة ممكنة - محسن لتجنب ANR
void main() async {
  // تهيئة Flutter - ضروري لتشغيل التطبيق
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة الإعدادات الأساسية للواجهة فوراً (بدون انتظار)
  _initUISettings();

  // تحسين أداء التطبيق لتجنب ANR
  PerformanceOptimizer.optimizeAppStartup();

  // تشغيل التطبيق الرئيسي فوراً بدون تأخير
  runApp(const WallpaperApp());

  // تهيئة الخدمات في الخلفية بعد ظهور واجهة المستخدم
  WidgetsBinding.instance.addPostFrameCallback((_) async {
    try {
      // تأخير قصير للسماح للواجهة بالظهور أولاً
      await Future.delayed(const Duration(milliseconds: 100));

      // تهيئة الخدمات الأساسية في خيط منفصل
      final initService = AppInitializationService();

      // تنفيذ التهيئة بدون انتظار لتجنب ANR
      compute((_) async {
        try {
          await initService.initialize();
        } catch (e) {
          debugPrint('❌ خطأ في تهيئة الخدمات الأساسية: $e');
        }
      }, null);

      // تأخير إضافي قبل تهيئة الخدمات الثانوية
      await Future.delayed(const Duration(milliseconds: 500));

      // تهيئة الخدمات الثانوية
      compute((_) {
        try {
          initService.initializeBackgroundServices();
        } catch (e) {
          debugPrint('❌ خطأ في تهيئة الخدمات الثانوية: $e');
        }
      }, null);
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة التطبيق: $e');
    }
  });
}
