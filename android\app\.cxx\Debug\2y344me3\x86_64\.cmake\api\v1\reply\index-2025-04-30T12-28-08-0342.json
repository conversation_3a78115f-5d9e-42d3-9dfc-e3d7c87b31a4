{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-000d0253a477b0481cf5.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-cba3286a69cc029385af.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-f552a72e74ea4e94d630.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-cba3286a69cc029385af.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-f552a72e74ea4e94d630.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-000d0253a477b0481cf5.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}