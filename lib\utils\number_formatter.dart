/// أداة مساعدة لتنسيق الأرقام بشكل مختصر
class NumberFormatter {
  /// تنسيق الرقم بشكل مختصر (مثل 1ك، 1م)
  static String formatCompact(int number) {
    if (number < 1000) {
      return number.toString();
    } else if (number < 1000000) {
      return '${(number / 1000).toStringAsFixed(number % 1000 == 0 ? 0 : 1)}ك';
    } else {
      return '${(number / 1000000).toStringAsFixed(number % 1000000 == 0 ? 0 : 1)}م';
    }
  }
}
