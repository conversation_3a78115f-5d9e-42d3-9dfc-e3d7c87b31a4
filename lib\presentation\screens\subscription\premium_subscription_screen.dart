import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../data/models/subscription.dart';
import '../../../data/services/subscription_service.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../components/effects/glass_container.dart';
import '../../components/navigation/modern_app_header.dart';
import 'widgets/premium_feature_item.dart';
import 'widgets/subscription_plan_card.dart';

class PremiumSubscriptionScreen extends StatefulWidget {
  const PremiumSubscriptionScreen({Key? key}) : super(key: key);

  @override
  State<PremiumSubscriptionScreen> createState() =>
      _PremiumSubscriptionScreenState();
}

class _PremiumSubscriptionScreenState extends State<PremiumSubscriptionScreen>
    with SingleTickerProviderStateMixin, TickerProviderStateMixin {
  final SubscriptionService _subscriptionService = SubscriptionService();
  bool _isLoading = true;
  bool _isProcessing = false;
  Subscription? _currentSubscription;
  List<Subscription> _availableSubscriptions = [];
  String _selectedSubscriptionId = '';

  // متحكمات الرسوم المتحركة المتعددة
  late AnimationController _headerAnimationController;
  late AnimationController _contentAnimationController;
  late AnimationController _buttonAnimationController;

  // تأثيرات متحركة متقدمة
  late Animation<double> _headerScaleAnimation;
  late Animation<Offset> _contentSlideAnimation;
  late Animation<double> _buttonScaleAnimation;
  late CurvedAnimation _contentCurvedAnimation;

  @override
  void initState() {
    super.initState();

    // تهيئة متحكمات الرسوم المتحركة المتعددة
    _headerAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    _contentAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _buttonAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    // إعداد الرسوم المتحركة
    _headerScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _headerAnimationController, curve: Curves.elasticOut),
    );

    _contentCurvedAnimation = CurvedAnimation(
      parent: _contentAnimationController,
      curve: Curves.easeOutCubic,
    );

    _contentSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(_contentCurvedAnimation);

    _buttonScaleAnimation = Tween<double>(begin: 0.9, end: 1.0).animate(
      CurvedAnimation(parent: _buttonAnimationController, curve: Curves.easeOutBack),
    );

    // تحميل البيانات
    _loadSubscriptions();

    // إخفاء شريط التنقل النظامي وتعيين نمط شريط الحالة
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: [SystemUiOverlay.top],
    );

    // تعيين نمط شريط الحالة
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    _contentAnimationController.dispose();
    _buttonAnimationController.dispose();
    super.dispose();
  }

  // تحميل البيانات بشكل أسرع وأكثر كفاءة
  Future<void> _loadSubscriptions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل البيانات بشكل متزامن
      final currentSubscription = await _subscriptionService.getCurrentSubscription();

      // الحصول على الاشتراكات المتاحة
      final availableSubscriptions = SubscriptionService.getAvailableSubscriptions();

      if (!mounted) return;

      setState(() {
        _currentSubscription = currentSubscription;
        _availableSubscriptions = availableSubscriptions;
        _selectedSubscriptionId =
            availableSubscriptions
                .firstWhere(
                  (sub) => sub.isRecommended,
                  orElse: () => availableSubscriptions.first,
                )
                .id;
        _isLoading = false;
      });

      // بدء الرسوم المتحركة بشكل متسلسل لتحسين الأداء
      _headerAnimationController.forward();
      await Future.delayed(const Duration(milliseconds: 200));
      _contentAnimationController.forward();
      await Future.delayed(const Duration(milliseconds: 300));
      _buttonAnimationController.forward();

    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isLoading = false;
      });

      // إظهار إشعار خطأ احترافي
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في التحميل: ${e.toString()}'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  // دالة الاشتراك المحسنة مع رسوم متحركة وتجربة مستخدم أفضل
  Future<void> _subscribe() async {
    if (_isProcessing) return;

    // التحقق من اختيار خطة
    if (_selectedSubscriptionId.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار خطة الاشتراك أولاً'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final selectedSubscription = _availableSubscriptions.firstWhere(
        (sub) => sub.id == _selectedSubscriptionId,
      );

      // إظهار شريط تقدم احترافي
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري معالجة الاشتراك...'),
          backgroundColor: Colors.blue,
        ),
      );

      // استخدام خدمة الاشتراك لشراء الاشتراك
      final success = await _subscriptionService.purchaseSubscription(
        selectedSubscription.id,
      );

      if (!mounted) return;

      if (success) {
        // إعادة تحميل البيانات بشكل غير متزامن
        _loadSubscriptions();

        // عرض رسالة نجاح احترافية
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم الاشتراك بنجاح! مرحباً بك في النسخة المميزة من التطبيق'),
            backgroundColor: Colors.green,
          ),
        );

        // الانتقال إلى شاشة الاشتراك النشط بعد تأخير قصير
        await Future.delayed(const Duration(milliseconds: 1500));
        if (!mounted) return;

        // إعادة بناء الواجهة لعرض الاشتراك النشط
        setState(() {});
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل الاشتراك. يرجى المحاولة مرة أخرى.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الاشتراك: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  // دالة استعادة المشتريات المحسنة
  Future<void> _restoreSubscription() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري الاستعادة...'),
          backgroundColor: Colors.blue,
        ),
      );

      final success = await _subscriptionService.restoreSubscription();

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم الاستعادة بنجاح! تم استعادة اشتراكك بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // إعادة تحميل البيانات
        await _loadSubscriptions();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد مشتريات سابقة لهذا الحساب'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الاستعادة: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  // دالة إلغاء الاشتراك المحسنة
  Future<void> _cancelSubscription() async {
    if (_isProcessing) return;

    // عرض مربع حوار تأكيد احترافي
    final bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surface,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
        ),
        title: Text(
          'إلغاء الاشتراك',
          style: GoogleFonts.cairo(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'هل أنت متأكد من رغبتك في إلغاء الاشتراك؟',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: Colors.white70,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'ستفقد جميع المميزات الحصرية المتاحة للمشتركين.',
              style: GoogleFonts.cairo(
                fontSize: 14,
                color: Colors.white60,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'إلغاء',
              style: GoogleFonts.cairo(
                fontSize: 16,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.error,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              ),
            ),
            child: Text(
              'تأكيد الإلغاء',
              style: GoogleFonts.cairo(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirm != true || !mounted) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري الإلغاء...'),
          backgroundColor: Colors.blue,
        ),
      );

      final success = await _subscriptionService.cancelSubscription();

      if (!mounted) return;

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم الإلغاء بنجاح. تم إلغاء اشتراكك بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // إعادة تحميل البيانات
        await _loadSubscriptions();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('فشل الإلغاء. يرجى المحاولة مرة أخرى.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الإلغاء: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child:
            _isLoading
                ? _buildLoadingState()
                : _currentSubscription != null && _currentSubscription!.isActive
                ? _buildActiveSubscription()
                : _buildSubscriptionPlans(),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(AppColors.accent),
      ),
    );
  }

  // واجهة الاشتراك النشط المحسنة
  Widget _buildActiveSubscription() {
    final subscription = _currentSubscription!;
    final endDate = subscription.endDate;

    String endDateText = 'مدى الحياة';
    if (endDate != null) {
      endDateText = '${endDate.year}/${endDate.month}/${endDate.day}';
    }

    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        // رأس الصفحة الثابت مع رسوم متحركة
        SliverPersistentHeader(
          delegate: ModernAppHeader(
            title: 'الاشتراك المميز',
            subtitle: 'أنت مشترك في النسخة المدفوعة',
            accentColor: AppColors.accent,
            showBackButton: true,
          ),
          pinned: true,
        ),

        // محتوى الاشتراك النشط
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const SizedBox(height: 24),

                // أيقونة النجاح المتحركة مع تأثيرات متقدمة
                AnimatedBuilder(
                  animation: _headerAnimationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _headerScaleAnimation.value,
                      child: Container(
                        width: 160,
                        height: 160,
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            colors: [
                              AppColors.accent.withOpacity(0.3),
                              AppColors.accent.withOpacity(0.1),
                            ],
                            radius: 0.8,
                          ),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.accent.withOpacity(0.4),
                              blurRadius: 20,
                              spreadRadius: 5,
                              offset: const Offset(0, 0),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.workspace_premium,
                          color: AppColors.accent,
                          size: 90,
                        ),
                      ),
                    );
                  },
                ).animate().fadeIn(delay: 300.ms, duration: 800.ms),

                const SizedBox(height: 24),

                // بطاقة معلومات الاشتراك المحسنة
                AnimatedBuilder(
                  animation: _contentAnimationController,
                  builder: (context, child) {
                    return SlideTransition(
                      position: _contentSlideAnimation,
                      child: GlassContainer(
                        padding: const EdgeInsets.all(28),
                        opacity: 0.2,
                        animate: true,
                        borderRadius: AppDimensions.radiusLarge,
                        border: 2,
                        borderColor: AppColors.accent.withOpacity(0.3),
                        child: Column(
                          children: [
                            // عنوان الاشتراك
                            Text(
                              'أنت مشترك في ${subscription.name}',
                              style: GoogleFonts.cairo(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                letterSpacing: 0.5,
                              ),
                              textAlign: TextAlign.center,
                            ),

                            const SizedBox(height: 20),

                            // شارة الاشتراك النشط
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: AppColors.goldGradient,
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                'نشط',
                                style: GoogleFonts.cairo(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                              ),
                            ),

                            const SizedBox(height: 20),

                            // تاريخ انتهاء الاشتراك مع أيقونة
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.card.withOpacity(0.3),
                                borderRadius: BorderRadius.circular(16),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(6),
                                    decoration: BoxDecoration(
                                      color: AppColors.accent.withOpacity(0.2),
                                      shape: BoxShape.circle,
                                    ),
                                    child: Icon(
                                      Icons.calendar_today,
                                      color: AppColors.accent,
                                      size: 20,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Text(
                                    'ينتهي في: $endDateText',
                                    style: GoogleFonts.cairo(
                                      fontSize: 16,
                                      color: Colors.white70,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 28),

                            // عنوان المميزات
                            Text(
                              'المميزات المتاحة لك',
                              style: GoogleFonts.cairo(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            ),

                            const SizedBox(height: 20),

                            // قائمة المميزات مع رسوم متحركة
                            ...subscription.features.map((feature) {
                              final index = subscription.features.indexOf(feature);
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 16),
                                child: PremiumFeatureItem(
                                  title: feature,
                                  isActive: true,
                                ).animate()
                                  .fadeIn(
                                    delay: (300 + (index * 100)).ms,
                                    duration: 600.ms,
                                  )
                                  .slideX(
                                    begin: 0.2,
                                    end: 0,
                                    delay: (300 + (index * 100)).ms,
                                    duration: 600.ms,
                                    curve: Curves.easeOutQuad,
                                  ),
                              );
                            }),
                          ],
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 32),

                // زر إلغاء الاشتراك المحسن
                AnimatedBuilder(
                  animation: _buttonAnimationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _buttonScaleAnimation.value,
                      child: SizedBox(
                        width: double.infinity,
                        height: 56,
                        child: OutlinedButton.icon(
                          onPressed: _isProcessing ? null : _cancelSubscription,
                          icon: _isProcessing
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    AppColors.error,
                                  ),
                                ),
                              )
                              : const Icon(Icons.cancel),
                          label: Text(
                            'إلغاء الاشتراك',
                            style: GoogleFonts.cairo(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: AppColors.error,
                            side: const BorderSide(color: AppColors.error),
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ).animate().fadeIn(delay: 800.ms, duration: 600.ms),

                const SizedBox(height: 16),

                // نص توضيحي محسن
                Text(
                  'استمتع بجميع المميزات الحصرية والمحتوى المميز',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white70,
                    fontWeight: FontWeight.w500,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // واجهة خطط الاشتراك المحسنة
  Widget _buildSubscriptionPlans() {
    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        // رأس الصفحة الثابت مع رسوم متحركة
        SliverPersistentHeader(
          delegate: ModernAppHeader(
            title: 'الاشتراك المميز',
            subtitle: 'احصل على مميزات حصرية',
            accentColor: AppColors.accent,
            showBackButton: true,
          ),
          pinned: true,
        ),

        // محتوى خطط الاشتراك
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // صورة توضيحية مع رسوم متحركة
                AnimatedBuilder(
                  animation: _headerAnimationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _headerScaleAnimation.value,
                      child: Container(
                        width: 160,
                        height: 160,
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            colors: [
                              AppColors.accent.withOpacity(0.2),
                              AppColors.accent.withOpacity(0.05),
                            ],
                            radius: 0.8,
                          ),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.accent.withOpacity(0.3),
                              blurRadius: 25,
                              spreadRadius: 8,
                              offset: const Offset(0, 0),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Icon(
                            Icons.workspace_premium,
                            color: AppColors.accent,
                            size: 90,
                          ),
                        ),
                      ),
                    );
                  },
                ).animate().fadeIn(delay: 300.ms, duration: 800.ms),

                const SizedBox(height: 32),

                // عنوان الصفحة
                Text(
                  'اشترك في النسخة المميزة',
                  style: GoogleFonts.cairo(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                  textAlign: TextAlign.center,
                ).animate().fadeIn(delay: 400.ms, duration: 600.ms),

                const SizedBox(height: 16),

                // وصف الصفحة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Text(
                    'احصل على مزايا حصرية وخلفيات مميزة بجودة عالية وبدون إعلانات',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      color: Colors.white70,
                      height: 1.6,
                      letterSpacing: 0.3,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ).animate().fadeIn(delay: 500.ms, duration: 600.ms),

                const SizedBox(height: 40),

                // المميزات الرئيسية
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildSimpleFeatureItem(
                      icon: Icons.download_rounded,
                      title: 'تنزيل الخلفيات',
                    ),
                    _buildSimpleFeatureItem(
                      icon: Icons.block,
                      title: 'بدون إعلانات',
                    ),
                    _buildSimpleFeatureItem(
                      icon: Icons.auto_awesome,
                      title: 'محتوى حصري',
                    ),
                  ],
                ).animate().fadeIn(delay: 600.ms, duration: 600.ms),

                const SizedBox(height: 40),

                // عنوان خطط الاشتراك
                Text(
                  'اختر خطة الاشتراك',
                  style: GoogleFonts.cairo(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: 0.5,
                  ),
                  textAlign: TextAlign.center,
                ).animate()
                  .fadeIn(delay: 700.ms, duration: 600.ms)
                  .slideX(
                    begin: -0.2,
                    end: 0,
                    delay: 700.ms,
                    duration: 600.ms,
                    curve: Curves.easeOutQuad,
                  ),

                const SizedBox(height: 24),

                // خطط الاشتراك مع رسوم متحركة
                ..._availableSubscriptions.map((subscription) {
                  final index = _availableSubscriptions.indexOf(subscription);
                  return AnimatedBuilder(
                    animation: _contentAnimationController,
                    builder: (context, child) {
                      return SlideTransition(
                        position: _contentSlideAnimation,
                        child: SubscriptionPlanCard(
                          subscription: subscription,
                          isSelected: _selectedSubscriptionId == subscription.id,
                          isLoading: _isProcessing && _selectedSubscriptionId == subscription.id,
                          onTap: _isProcessing
                              ? null
                              : () {
                                setState(() {
                                  _selectedSubscriptionId = subscription.id;
                                });
                              },
                        ),
                      );
                    },
                  ).animate()
                    .fadeIn(delay: (800 + (index * 150)).ms, duration: 600.ms)
                    .slideY(
                      begin: 0.3,
                      end: 0,
                      delay: (800 + (index * 150)).ms,
                      duration: 600.ms,
                      curve: Curves.easeOutQuad,
                    );
                }),

                const SizedBox(height: 40),

                // زر الاشتراك
                AnimatedBuilder(
                  animation: _buttonAnimationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _buttonScaleAnimation.value,
                      child: Container(
                        width: double.infinity,
                        height: 64,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: AppColors.goldGradient,
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.accent.withOpacity(0.4),
                              blurRadius: 15,
                              spreadRadius: 5,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: ElevatedButton(
                          onPressed: _isProcessing ? null : _subscribe,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.transparent,
                            foregroundColor: Colors.black,
                            shadowColor: Colors.transparent,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            elevation: 0,
                          ),
                          child: _isProcessing
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 3,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.black,
                                    ),
                                  ),
                                )
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.workspace_premium,
                                      size: 28,
                                    ),
                                    const SizedBox(width: 16),
                                    Text(
                                      'اشترك الآن',
                                      style: GoogleFonts.cairo(
                                        fontSize: 22,
                                        fontWeight: FontWeight.bold,
                                        letterSpacing: 0.5,
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    );
                  },
                ).animate()
                  .fadeIn(delay: 1200.ms, duration: 600.ms)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    delay: 1200.ms,
                    duration: 600.ms,
                    curve: Curves.easeOutQuad,
                  ),

                const SizedBox(height: 20),

                // زر استعادة المشتريات
                TextButton.icon(
                  onPressed: _isProcessing ? null : _restoreSubscription,
                  icon: _isProcessing
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              AppColors.primary,
                            ),
                          ),
                        )
                      : const Icon(Icons.restore),
                  label: Text(
                    'استعادة المشتريات',
                    style: GoogleFonts.cairo(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white70,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white70,
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                      side: BorderSide(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                  ),
                ).animate()
                  .fadeIn(delay: 1300.ms, duration: 600.ms)
                  .slideY(
                    begin: 0.3,
                    end: 0,
                    delay: 1300.ms,
                    duration: 600.ms,
                    curve: Curves.easeOutQuad,
                  ),

                const SizedBox(height: 24),

                // نص توضيحي
                Text(
                  'اشترك الآن للوصول إلى جميع الخلفيات المميزة والمحتوى الحصري',
                  style: GoogleFonts.cairo(
                    fontSize: 14,
                    color: Colors.white38,
                    fontStyle: FontStyle.italic,
                    letterSpacing: 0.3,
                  ),
                  textAlign: TextAlign.center,
                ).animate()
                  .fadeIn(delay: 1400.ms, duration: 600.ms),

                const SizedBox(height: 40),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // عنصر ميزة بسيط
  Widget _buildSimpleFeatureItem({
    required IconData icon,
    required String title,
  }) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppColors.accent.withOpacity(0.2),
                AppColors.accent.withOpacity(0.1),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: AppColors.accent, size: 30),
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }
}
