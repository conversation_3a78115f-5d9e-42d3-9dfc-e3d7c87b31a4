import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';

/// أداة مساعدة للتحقق من منصة التشغيل وإصدار النظام
class PlatformUtils {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  static AndroidDeviceInfo? _androidInfo;
  static bool _initialized = false;

  /// تهيئة معلومات الجهاز
  static Future<void> init() async {
    if (_initialized) return;
    
    if (Platform.isAndroid) {
      _androidInfo = await _deviceInfo.androidInfo;
    }
    
    _initialized = true;
  }

  /// التحقق مما إذا كان الجهاز يعمل بنظام أندرويد 12 أو أعلى
  static Future<bool> isAndroid12OrHigher() async {
    await init();
    
    if (Platform.isAndroid && _androidInfo != null) {
      // أندرويد 12 هو SDK 31
      return _androidInfo!.version.sdkInt >= 31;
    }
    
    return false;
  }

  /// الحصول على إصدار أندرويد كنص
  static Future<String> getAndroidVersionString() async {
    await init();
    
    if (Platform.isAndroid && _androidInfo != null) {
      return _androidInfo!.version.release;
    }
    
    return 'غير معروف';
  }

  /// الحصول على رقم SDK لنظام أندرويد
  static Future<int> getAndroidSdkInt() async {
    await init();
    
    if (Platform.isAndroid && _androidInfo != null) {
      return _androidInfo!.version.sdkInt;
    }
    
    return 0;
  }
}
