import 'dart:ui';
import 'package:flutter/material.dart';

/// حاوية زجاجية بتأثير الضبابية
class GlassContainer extends StatelessWidget {
  final Widget child;
  final double borderRadius;
  final double blur;
  final double opacity;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? width;
  final double? height;
  final double border;
  final Color borderColor;
  final List<BoxShadow>? boxShadow;
  final Alignment? alignment;
  final bool animate;
  final BorderRadius? customBorderRadius;
  final Gradient? gradient;

  const GlassContainer({
    super.key,
    required this.child,
    this.borderRadius = 0,
    this.blur = 10,
    this.opacity = 0.2,
    this.backgroundColor,
    this.padding,
    this.margin,
    this.width,
    this.height,
    this.border = 0,
    this.borderColor = Colors.white,
    this.boxShadow,
    this.alignment,
    this.animate = false,
    this.customBorderRadius,
    this.gradient,
  });

  @override
  Widget build(BuildContext context) {
    final borderRadiusValue =
        customBorderRadius ?? BorderRadius.circular(borderRadius);

    final glassContainer = Container(
      margin: margin,
      width: width,
      height: height,
      alignment: alignment,
      decoration: BoxDecoration(
        borderRadius: borderRadiusValue,
        boxShadow:
            boxShadow ??
            [
              BoxShadow(
                color: Colors.black.withAlpha(38), // 0.15 * 255 = 38
                blurRadius: 8,
                spreadRadius: 0,
                offset: const Offset(0, 2),
              ),
            ],
      ),
      child: ClipRRect(
        borderRadius: borderRadiusValue,
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
          child: Container(
            padding: padding,
            decoration: BoxDecoration(
              color: (backgroundColor ?? Colors.white).withAlpha(
                (opacity * 255).toInt(),
              ),
              borderRadius: borderRadiusValue,
              border:
                  border > 0
                      ? Border.all(
                        color: borderColor.withAlpha(64), // 0.25 * 255 = 64
                        width: border,
                      )
                      : Border.all(
                        color: Colors.white.withAlpha(38), // 0.15 * 255 = 38
                        width: 0.5,
                      ),
              gradient:
                  gradient ??
                  LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withAlpha(38), // 0.15 * 255 = 38
                      Colors.white.withAlpha(13), // 0.05 * 255 = 13
                    ],
                  ),
            ),
            child: child,
          ),
        ),
      ),
    );

    if (animate) {
      return TweenAnimationBuilder<double>(
        tween: Tween<double>(begin: 0.0, end: 1.0),
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOutCubic,
        builder: (context, value, child) {
          return Opacity(opacity: value, child: child);
        },
        child: glassContainer,
      );
    }

    return glassContainer;
  }
}
