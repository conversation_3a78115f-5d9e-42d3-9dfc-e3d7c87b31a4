import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';

/// مكون تحميل بتأثير الوميض
class ShimmerLoader extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;
  final BoxShape shape;
  
  const ShimmerLoader({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius = AppDimensions.radiusMedium,
    this.shape = BoxShape.rectangle,
  });
  
  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: AppColors.card,
      highlightColor: AppColors.surfaceLight,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: Colors.white,
          shape: shape,
          borderRadius: shape == BoxShape.rectangle
              ? BorderRadius.circular(borderRadius)
              : null,
        ),
      ),
    );
  }
}

/// مكون تحميل لبطاقة الخلفية
class WallpaperCardShimmer extends StatelessWidget {
  final double? height;
  final double borderRadius;
  
  const WallpaperCardShimmer({
    super.key,
    this.height = 200,
    this.borderRadius = AppDimensions.radiusMedium,
  });
  
  @override
  Widget build(BuildContext context) {
    return ShimmerLoader(
      width: double.infinity,
      height: height!,
      borderRadius: borderRadius,
    );
  }
}

/// مكون تحميل لبطاقة الفئة
class CategoryCardShimmer extends StatelessWidget {
  final double? width;
  final double? height;
  final bool isCircular;
  
  const CategoryCardShimmer({
    super.key,
    this.width,
    this.height = 100,
    this.isCircular = false,
  });
  
  @override
  Widget build(BuildContext context) {
    if (isCircular) {
      return Column(
        children: [
          ShimmerLoader(
            width: width ?? 80,
            height: width ?? 80,
            shape: BoxShape.circle,
          ),
          const SizedBox(height: AppDimensions.marginSmall),
          ShimmerLoader(
            width: (width ?? 80) * 0.8,
            height: 16,
            borderRadius: AppDimensions.radiusSmall,
          ),
        ],
      );
    }
    
    return ShimmerLoader(
      width: width ?? double.infinity,
      height: height!,
      borderRadius: AppDimensions.radiusMedium,
    );
  }
}

/// مكون تحميل لشبكة الخلفيات
class WallpaperGridShimmer extends StatelessWidget {
  final int itemCount;
  final int crossAxisCount;
  final double spacing;
  
  const WallpaperGridShimmer({
    super.key,
    this.itemCount = 10,
    this.crossAxisCount = 2,
    this.spacing = AppDimensions.marginMedium,
  });
  
  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: spacing,
        mainAxisSpacing: spacing,
        childAspectRatio: 0.7,
      ),
      itemCount: itemCount,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.all(spacing),
      itemBuilder: (context, index) {
        return const WallpaperCardShimmer();
      },
    );
  }
}

/// مكون تحميل لقائمة الفئات
class CategoryListShimmer extends StatelessWidget {
  final int itemCount;
  final bool isCircular;
  final double spacing;
  
  const CategoryListShimmer({
    super.key,
    this.itemCount = 5,
    this.isCircular = false,
    this.spacing = AppDimensions.marginMedium,
  });
  
  @override
  Widget build(BuildContext context) {
    if (isCircular) {
      return SizedBox(
        height: 120,
        child: ListView.separated(
          scrollDirection: Axis.horizontal,
          itemCount: itemCount,
          padding: EdgeInsets.all(spacing),
          separatorBuilder: (context, index) => SizedBox(width: spacing),
          itemBuilder: (context, index) {
            return const CategoryCardShimmer(isCircular: true);
          },
        ),
      );
    }
    
    return SizedBox(
      height: 120,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: itemCount,
        padding: EdgeInsets.all(spacing),
        separatorBuilder: (context, index) => SizedBox(width: spacing),
        itemBuilder: (context, index) {
          return CategoryCardShimmer(
            width: 150,
            height: 120,
          );
        },
      ),
    );
  }
}
