import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:http/http.dart' as http;
import 'package:permission_handler/permission_handler.dart';

class ImageUtils {
  // تحميل صورة من الإنترنت وحفظها محليًا
  static Future<String?> downloadImage(String imageUrl, String fileName) async {
    try {
      // طلب الإذن
      final status = await Permission.storage.request();
      if (!status.isGranted) {
        return null;
      }

      // تحميل الصورة
      final http.Response response = await http.get(Uri.parse(imageUrl));

      // الحصول على مسار التخزين
      final Directory? directory = await getExternalStorageDirectory();
      if (directory == null) {
        return null;
      }

      // إنشاء مجلد للتطبيق إذا لم يكن موجودًا
      final String appDirPath = '${directory.path}/IslamicWallpapers';
      final Directory appDir = Directory(appDirPath);
      if (!await appDir.exists()) {
        await appDir.create(recursive: true);
      }

      // حفظ الصورة
      final String filePath = '$appDirPath/$fileName';
      final File file = File(filePath);
      await file.writeAsBytes(response.bodyBytes);

      return filePath;
    } catch (e) {
      return null;
    }
  }

  // التحقق من وجود الإذن
  static Future<bool> checkStoragePermission() async {
    final status = await Permission.storage.status;
    if (status.isGranted) {
      return true;
    }

    // طلب الإذن إذا لم يكن ممنوحًا
    final result = await Permission.storage.request();
    return result.isGranted;
  }
}
