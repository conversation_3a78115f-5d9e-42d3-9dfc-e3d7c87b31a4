import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:shared_preferences/shared_preferences.dart';

class WallpaperCacheManager {
  static const String _cacheKeyPrefix = 'wallpaper_cache_';
  static const String _lastUpdatedKey = 'wallpapers_last_updated';

  // الحصول على مسار الملف المحلي للخلفية
  static Future<String?> getLocalPath(String imageUrl) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('$_cacheKeyPrefix${_getUrlKey(imageUrl)}');
    } catch (e) {
      debugPrint('خطأ في الحصول على مسار الملف المحلي: $e');
      return null;
    }
  }

  // حفظ الخلفية محليًا
  static Future<String?> cacheImage(String imageUrl) async {
    try {
      // التحقق مما إذا كانت الصورة محفوظة بالفعل
      final existingPath = await getLocalPath(imageUrl);
      if (existingPath != null) {
        final file = File(existingPath);
        if (await file.exists()) {
          return existingPath;
        }
      }

      // تنزيل الصورة وحفظها محليًا
      final file = await DefaultCacheManager().getSingleFile(imageUrl);

      // نسخ الملف إلى مجلد التطبيق الدائم
      final appDir = await getApplicationDocumentsDirectory();
      final fileName = '${_getUrlKey(imageUrl)}.jpg';
      final localFile = File('${appDir.path}/wallpapers/$fileName');

      // إنشاء المجلد إذا لم يكن موجودًا
      await localFile.parent.create(recursive: true);

      // نسخ الملف
      await file.copy(localFile.path);

      // حفظ المسار في التفضيلات المشتركة
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        '$_cacheKeyPrefix${_getUrlKey(imageUrl)}',
        localFile.path,
      );

      return localFile.path;
    } catch (e) {
      debugPrint('خطأ في حفظ الصورة محليًا: $e');
      return null;
    }
  }

  // تنزيل جميع الخلفيات وحفظها محليًا
  static Future<void> cacheAllImages(List<String> imageUrls) async {
    try {
      for (final url in imageUrls) {
        await cacheImage(url);
      }

      // تحديث وقت آخر تحديث
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastUpdatedKey, DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('خطأ في حفظ جميع الصور محليًا: $e');
    }
  }

  // التحقق من وقت آخر تحديث للخلفيات
  static Future<DateTime?> getLastUpdated() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdated = prefs.getString(_lastUpdatedKey);

      if (lastUpdated != null) {
        return DateTime.parse(lastUpdated);
      }

      return null;
    } catch (e) {
      debugPrint('خطأ في الحصول على وقت آخر تحديث: $e');
      return null;
    }
  }

  // مسح ذاكرة التخزين المؤقت
  static Future<void> clearCache() async {
    try {
      // مسح ملفات الصور
      final appDir = await getApplicationDocumentsDirectory();
      final cacheDir = Directory('${appDir.path}/wallpapers');

      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }

      // مسح المسارات المحفوظة
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith(_cacheKeyPrefix)) {
          await prefs.remove(key);
        }
      }

      // مسح وقت آخر تحديث
      await prefs.remove(_lastUpdatedKey);
    } catch (e) {
      debugPrint('خطأ في مسح ذاكرة التخزين المؤقت: $e');
    }
  }

  // تحويل عنوان URL إلى مفتاح فريد
  static String _getUrlKey(String url) {
    return url.hashCode.toString();
  }
}
