import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';
import '../../../design_system/typography.dart';
import '../effects/glass_container.dart';

/// عنصر شريط التنقل السفلي
class NavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final Color? activeColor;
  final Color? activeBgColor;

  NavItem({
    required this.icon,
    required this.label,
    IconData? activeIcon,
    this.activeColor,
    this.activeBgColor,
  }) : activeIcon = activeIcon ?? icon;
}

/// شريط تنقل سفلي احترافي
class PremiumBottomNav extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<NavItem> items;
  final bool useGlass;
  final Color? backgroundColor;
  final Color? activeColor;
  final Color? inactiveColor;
  final double height;
  final double elevation;
  final double borderRadius;
  final EdgeInsets margin;

  const PremiumBottomNav({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
    this.useGlass = true,
    this.backgroundColor,
    this.activeColor,
    this.inactiveColor,
    this.height = 65,
    this.elevation = 15,
    this.borderRadius = 24,
    this.margin = const EdgeInsets.fromLTRB(12, 0, 12, 12),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // حساب ارتفاع الشريط مع مراعاة الهامش السفلي للجهاز
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final totalHeight = height + bottomPadding;

    return Container(
      height: totalHeight,
      margin: margin,
      child:
          useGlass
              ? _buildGlassNav(context, bottomPadding)
              : _buildSolidNav(context, bottomPadding),
    );
  }

  /// بناء شريط تنقل زجاجي
  Widget _buildGlassNav(BuildContext context, double bottomPadding) {
    return GlassContainer(
      borderRadius: borderRadius,
      blur: 10,
      opacity: 0.7,
      border: 1.5,
      borderColor: Colors.white.withOpacity(0.2),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.2),
          blurRadius: elevation,
          offset: const Offset(0, 5),
        ),
      ],
      child: _buildNavContent(bottomPadding),
    );
  }

  /// بناء شريط تنقل صلب
  Widget _buildSolidNav(BuildContext context, double bottomPadding) {
    return Container(
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.surface,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: elevation,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: _buildNavContent(bottomPadding),
    );
  }

  /// بناء محتوى شريط التنقل
  Widget _buildNavContent(double bottomPadding) {
    return Padding(
      padding: EdgeInsets.only(bottom: bottomPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(items.length, (index) {
          final item = items[index];
          final isActive = index == currentIndex;

          return _NavButton(
            icon: isActive ? item.activeIcon : item.icon,
            label: item.label,
            isActive: isActive,
            activeColor: item.activeColor ?? activeColor ?? AppColors.accent,
            inactiveColor: inactiveColor ?? AppColors.textMuted,
            activeBgColor: item.activeBgColor,
            onTap: () => onTap(index),
          );
        }),
      ),
    );
  }
}

/// زر التنقل
class _NavButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isActive;
  final Color activeColor;
  final Color inactiveColor;
  final Color? activeBgColor;
  final VoidCallback onTap;

  const _NavButton({
    Key? key,
    required this.icon,
    required this.label,
    required this.isActive,
    required this.activeColor,
    required this.inactiveColor,
    this.activeBgColor,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        splashColor: Colors.transparent,
        highlightColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // أيقونة العنصر
              Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color:
                          isActive
                              ? (activeBgColor ?? activeColor.withOpacity(0.2))
                              : Colors.transparent,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      icon,
                      color: isActive ? activeColor : inactiveColor,
                      size: isActive ? 24 : 22,
                    ),
                  )
                  .animate(target: isActive ? 1 : 0)
                  .scale(
                    begin: const Offset(0.8, 0.8),
                    end: const Offset(1.0, 1.0),
                    curve: Curves.easeOutBack,
                    duration: const Duration(milliseconds: 300),
                  ),

              const SizedBox(height: 4),

              // نص العنصر
              Text(
                    label,
                    style: TextStyle(
                      color: isActive ? activeColor : inactiveColor,
                      fontSize: 12,
                      fontWeight:
                          isActive ? FontWeight.bold : FontWeight.normal,
                    ),
                  )
                  .animate(target: isActive ? 1 : 0)
                  .fadeIn(duration: const Duration(milliseconds: 200)),
            ],
          ),
        ),
      ),
    );
  }
}
