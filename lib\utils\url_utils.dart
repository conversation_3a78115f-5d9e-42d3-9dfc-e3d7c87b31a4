import 'package:url_launcher/url_launcher.dart';

class UrlUtils {
  // فتح رابط في المتصفح
  static Future<bool> launchURL(String urlString) async {
    final Uri url = Uri.parse(urlString);
    
    if (await canLaunchUrl(url)) {
      return await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      return false;
    }
  }
  
  // فتح رابط بريد إلكتروني
  static Future<bool> launchEmail(String email, {String subject = '', String body = ''}) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      queryParameters: {
        'subject': subject,
        'body': body,
      },
    );
    
    if (await canLaunchUrl(emailUri)) {
      return await launchUrl(emailUri);
    } else {
      return false;
    }
  }
  
  // فتح رابط متجر التطبيقات
  static Future<bool> openAppStore(String appId) async {
    final Uri url = Uri.parse('https://play.google.com/store/apps/details?id=$appId');
    
    if (await canLaunchUrl(url)) {
      return await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      return false;
    }
  }
}
