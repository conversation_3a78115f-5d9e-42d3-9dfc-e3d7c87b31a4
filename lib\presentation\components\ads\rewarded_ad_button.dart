import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../data/services/ad_service.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/typography.dart';
import '../../../design_system/gradients.dart';

/// زر احترافي لعرض إعلان المكافأة
class RewardedAdButton extends StatefulWidget {
  final String text;
  final IconData icon;
  final VoidCallback onRewarded;
  final VoidCallback? onFailed;
  final bool isLoading;
  final bool isPremium;

  const RewardedAdButton({
    super.key,
    required this.text,
    required this.icon,
    required this.onRewarded,
    this.onFailed,
    this.isLoading = false,
    this.isPremium = false,
  });

  @override
  State<RewardedAdButton> createState() => _RewardedAdButtonState();
}

class _RewardedAdButtonState extends State<RewardedAdButton>
    with SingleTickerProviderStateMixin {
  final AdService _adService = AdService();
  bool _isLoading = false;
  bool _isAdReady = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    _checkAdStatus();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _checkAdStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الإعلان مسبقًا
      await _adService.loadRewardedAd();

      // التحقق من جاهزية الإعلان
      final bool isReady = await _adService.isRewardedAdReady();

      if (mounted) {
        setState(() {
          _isAdReady = isReady;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isAdReady = false;
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _showRewardedAd() async {
    if (_isLoading || widget.isLoading) return;

    // إذا لم يكن الإعلان جاهزًا، نحاول تحميله مرة أخرى
    if (!_isAdReady) {
      setState(() {
        _isLoading = true;
      });

      // عرض رسالة للمستخدم
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('جاري تحميل الإعلان، يرجى الانتظار...'),
          backgroundColor: AppColors.secondary,
          behavior: SnackBarBehavior.floating,
          duration: Duration(seconds: 2),
        ),
      );

      await _checkAdStatus();

      if (!_isAdReady) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('فشل تحميل الإعلان، يرجى المحاولة مرة أخرى لاحقًا'),
              backgroundColor: AppColors.error,
              behavior: SnackBarBehavior.floating,
            ),
          );

          // استدعاء دالة الفشل
          if (widget.onFailed != null) {
            widget.onFailed!();
          }
        }
        return;
      }
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // عرض رسالة للمستخدم قبل عرض الإعلان
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'جاري تحضير الإعلان، يرجى مشاهدته كاملاً للحصول على المكافأة',
            ),
            backgroundColor: AppColors.primary,
            behavior: SnackBarBehavior.floating,
            duration: Duration(seconds: 2),
          ),
        );
      }

      await _adService.showRewardedAd(
        onRewarded: () {
          // استدعاء الدالة عند نجاح المكافأة
          widget.onRewarded();

          if (mounted) {
            setState(() {
              _isLoading = false;
              _isAdReady = false; // إعادة تعيين حالة الإعلان
            });

            // تحميل إعلان جديد للمرة القادمة
            _checkAdStatus();
          }
        },
        onFailed: () {
          // استدعاء الدالة عند فشل المكافأة
          if (widget.onFailed != null) {
            widget.onFailed!();
          }

          if (mounted) {
            setState(() {
              _isLoading = false;
              _isAdReady = false;
            });

            // عرض رسالة خطأ
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('لم تكتمل مشاهدة الإعلان، لن تحصل على المكافأة'),
                backgroundColor: AppColors.error,
                behavior: SnackBarBehavior.floating,
              ),
            );

            // تحميل إعلان جديد للمرة القادمة
            _checkAdStatus();
          }
        },
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isAdReady = false;
        });

        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ أثناء عرض الإعلان، يرجى المحاولة مرة أخرى'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );

        // تحميل إعلان جديد للمرة القادمة
        _checkAdStatus();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDisabled = _isLoading || widget.isLoading;

    return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
            gradient:
                widget.isPremium ? AppGradients.gold : AppGradients.primary,
            boxShadow: const [
              BoxShadow(
                color: Color(
                  0x4D1E88E5,
                ), // AppColors.primary with 30% opacity (0x4D)
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: isDisabled ? null : _showRewardedAd,
              borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
              splashColor: Colors.white24,
              highlightColor: Colors.white10,
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingLarge,
                  vertical: AppDimensions.paddingMedium,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (isDisabled)
                      const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    else
                      AnimatedBuilder(
                        animation: _animationController,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: 1.0 + (_animationController.value * 0.1),
                            child: Icon(
                              widget.icon,
                              color: Colors.white,
                              size: 24,
                            ),
                          );
                        },
                      ),
                    const SizedBox(width: 12),
                    Text(
                      widget.text,
                      style: AppTypography.labelLarge.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        )
        .animate()
        .fadeIn(duration: const Duration(milliseconds: 300))
        .slideY(
          begin: 0.2,
          end: 0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOutCubic,
        );
  }
}
