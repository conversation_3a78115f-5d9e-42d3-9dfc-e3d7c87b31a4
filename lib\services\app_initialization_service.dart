import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

import 'advanced_initialization_service.dart';
import 'background_initialization_service.dart';
import 'wallpaper_platform_service.dart';

/// خدمة مركزية لتهيئة جميع خدمات التطبيق
class AppInitializationService {
  static final AppInitializationService _instance = AppInitializationService._internal();
  
  factory AppInitializationService() => _instance;
  
  AppInitializationService._internal();
  
  final AdvancedInitializationService _advancedService = AdvancedInitializationService();
  final BackgroundInitializationService _backgroundService = BackgroundInitializationService();
  
  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;
  
  /// تهيئة الخدمات الأساسية للتطبيق
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // تهيئة خدمة MobileAds في خيط منفصل - بدون انتظار
      _advancedService.initializeHeavyService(
        serviceName: 'MobileAds',
        initFunction: () => MobileAds.instance.initialize(),
        waitForResult: false,
      );
      
      // تهيئة خدمة التهيئة في الخلفية - بدون انتظار
      _backgroundService.initialize();
      
      // تعيين حالة التهيئة
      _isInitialized = true;
      _advancedService.isInitialized = true;
      
      debugPrint('✅ تم تهيئة الخدمات الأساسية بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمات الأساسية: $e');
    }
  }
  
  /// تهيئة الخدمات الثانوية في الخلفية
  void initializeBackgroundServices() {
    try {
      // تنفيذ العمليات الثقيلة في خيط منفصل
      Isolate.run(() {
        WallpaperPlatformService.init();
        debugPrint('✅ تم تهيئة خدمة منصة الخلفيات بنجاح');
      });
      
      debugPrint('✅ تم بدء تهيئة الخدمات الثانوية في الخلفية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الخدمات الثانوية: $e');
    }
  }
  
  /// إيقاف وتنظيف الموارد
  void dispose() {
    _advancedService.dispose();
  }
}