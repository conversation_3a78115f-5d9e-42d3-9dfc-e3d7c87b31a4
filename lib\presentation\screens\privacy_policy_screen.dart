import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../design_system/colors.dart';
import '../components/effects/glass_container.dart';
import '../components/navigation/modern_app_header.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            // رأس الصفحة الثابت
            SliverPersistentHeader(
              delegate: ModernAppHeader(
                title: 'سياسة الخصوصية',
                subtitle: 'كيف نتعامل مع بياناتك',
                showBackButton: true,
              ),
              pinned: true,
            ),

            // محتوى سياسة الخصوصية
            SliverPadding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 120),
              sliver: SliverList(
                delegate: SliverChildListDelegate([
                  _buildSection(
                    title: 'مقدمة',
                    content: 'تنطبق سياسة الخصوصية هذه على تطبيق الأجهزة المحمولة (المشار إليه فيما يلي باسم "التطبيق") الذي أنشأه المطور (المشار إليه فيما يلي باسم "مقدم الخدمة") كخدمة مجانية. هذه الخدمة مخصصة للاستخدام "كما هي".',
                    icon: Icons.info_outline,
                  ),
                  
                  _buildSection(
                    title: 'جمع المعلومات واستخدامها',
                    content: 'يجمع التطبيق معلومات عند تنزيله واستخدامه. قد تتضمن هذه المعلومات معلومات مثل:\n\n'
                        '• عنوان بروتوكول الإنترنت الخاص بجهازك (على سبيل المثال عنوان IP)\n'
                        '• صفحات التطبيق التي تزورها، ووقت وتاريخ زيارتك، والوقت الذي تقضيه في تلك الصفحات\n'
                        '• الوقت المستغرق في التطبيق\n'
                        '• نظام التشغيل الذي تستخدمه على جهازك المحمول\n\n'
                        'لا يقوم التطبيق بجمع معلومات دقيقة حول موقع جهازك المحمول.\n\n'
                        'يجوز لمزود الخدمة استخدام المعلومات التي قدمتها للاتصال بك من وقت لآخر لتزويدك بمعلومات مهمة وإشعارات مطلوبة وعروض ترويجية تسويقية.',
                    icon: Icons.data_usage,
                  ),
                  
                  _buildSection(
                    title: 'وصول الطرف الثالث',
                    content: 'يتم إرسال البيانات المجمعة والمجهولة المصدر فقط دوريًا إلى خدمات خارجية لمساعدة مزود الخدمة على تحسين التطبيق وخدماته. ويجوز لمزود الخدمة مشاركة معلوماتك مع جهات خارجية بالطرق الموضحة في بيان الخصوصية هذا.\n\n'
                        'يرجى العلم أن التطبيق يستخدم خدمات خارجية لديها سياسة خصوصية خاصة بها بشأن معالجة البيانات. تجدون أدناه روابط لسياسة الخصوصية الخاصة بمقدمي الخدمات الخارجية الذين يستخدمهم التطبيق:\n\n'
                        '• خدمات جوجل بلاي\n'
                        '• أد موب\n'
                        '• Google Analytics لـ Firebase\n'
                        '• Firebase Crashlytics\n'
                        '• فيسبوك',
                    icon: Icons.security,
                  ),
                  
                  _buildSection(
                    title: 'الإفصاح عن المعلومات',
                    content: 'يجوز لمزود الخدمة الكشف عن المعلومات المقدمة من المستخدم والمجمعة تلقائيًا:\n\n'
                        '• حسبما يقتضيه القانون، مثل الامتثال لاستدعاء قضائي، أو عملية قانونية مماثلة؛\n'
                        '• عندما يعتقدون بحسن نية أن الإفصاح ضروري لحماية حقوقهم، أو حماية سلامتك أو سلامة الآخرين، أو التحقيق في الاحتيال، أو الاستجابة لطلب حكومي؛\n'
                        '• مع مقدمي الخدمات الموثوق بهم الذين يعملون نيابة عنهم، ليس لديهم استخدام مستقل للمعلومات التي نكشفها لهم، وقد وافقوا على الالتزام بالقواعد المنصوص عليها في بيان الخصوصية هذا.',
                    icon: Icons.privacy_tip,
                  ),
                  
                  _buildSection(
                    title: 'حقوق الانسحاب',
                    content: 'يمكنك إيقاف جمع المعلومات من خلال التطبيق بسهولة عن طريق إلغاء تثبيته. يمكنك استخدام إجراءات إلغاء التثبيت القياسية المتوفرة على جهازك المحمول أو عبر سوق تطبيقات الهاتف المحمول أو الشبكة.',
                    icon: Icons.exit_to_app,
                  ),
                  
                  _buildSection(
                    title: 'سياسة الاحتفاظ بالبيانات',
                    content: 'سيحتفظ مزوّد الخدمة ببيانات المستخدم طوال فترة استخدامك للتطبيق ولمدة معقولة بعد ذلك. إذا كنت ترغب في حذف بيانات المستخدم التي قدمتها عبر التطبيق، يُرجى التواصل معه عبر البريد الإلكتروني <EMAIL>، وسيرد عليك خلال فترة معقولة.',
                    icon: Icons.storage,
                  ),
                  
                  _buildSection(
                    title: 'أطفال',
                    content: 'لا يستخدم مزود الخدمة التطبيق لطلب البيانات عن علم من الأطفال الذين تقل أعمارهم عن 13 عامًا أو التسويق لهم.\n\n'
                        'لا يستهدف التطبيق أي شخص دون سن الثالثة عشرة. ولا يجمع مزود الخدمة معلومات شخصية عن الأطفال دون سن الثالثة عشرة عمدًا. في حال اكتشف مزود الخدمة أن طفلًا دون سن الثالثة عشرة قد قدّم معلومات شخصية، فسيحذفها فورًا من خوادمه. إذا كنتَ ولي أمر أو وصيًا وعلمتَ أن طفلك قد قدّم لنا معلومات شخصية، يُرجى التواصل مع مزود الخدمة (<EMAIL>) ليتمكن من اتخاذ الإجراءات اللازمة.',
                    icon: Icons.child_care,
                  ),
                  
                  _buildSection(
                    title: 'حماية',
                    content: 'يولي مزود الخدمة اهتمامًا بالغًا بحماية سرية معلوماتك. ويوفر ضمانات مادية وإلكترونية وإجرائية لحماية المعلومات التي يعالجها ويحتفظ بها.',
                    icon: Icons.shield,
                  ),
                  
                  _buildSection(
                    title: 'التغييرات',
                    content: 'قد تُحدَّث سياسة الخصوصية هذه من وقت لآخر لأي سبب. سيُخطرك مُقدِّم الخدمة بأي تغييرات تطرأ على سياسة الخصوصية من خلال تحديث هذه الصفحة بسياسة الخصوصية الجديدة. ننصحك بالاطلاع على سياسة الخصوصية هذه بانتظام للاطلاع على أي تغييرات، حيث يُعدّ الاستمرار في استخدامها موافقةً على جميع التغييرات.\n\n'
                        'سياسة الخصوصية هذه سارية اعتبارًا من 2025-05-01',
                    icon: Icons.update,
                  ),
                  
                  _buildSection(
                    title: 'موافقتك',
                    content: 'من خلال استخدام التطبيق، فإنك توافق على معالجة معلوماتك على النحو المنصوص عليه في سياسة الخصوصية هذه الآن وكما تم تعديلها من قبلنا.',
                    icon: Icons.check_circle,
                  ),
                  
                  _buildSection(
                    title: 'اتصل بنا',
                    content: 'إذا كانت لديك أي أسئلة بخصوص الخصوصية أثناء استخدام التطبيق، أو لديك أسئلة حول الممارسات، يرجى الاتصال بمقدم الخدمة عبر البريد الإلكتروني على <EMAIL>.',
                    icon: Icons.email,
                    hasContactButton: true,
                  ),
                  
                  const SizedBox(height: 40),
                ]),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    required IconData icon,
    bool hasContactButton = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: GlassContainer(
        padding: const EdgeInsets.all(20),
        opacity: 0.15,
        animate: true,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withAlpha(40),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ).animate().fadeIn(duration: 400.ms).slideX(
              begin: -0.1,
              end: 0,
              duration: 400.ms,
              curve: Curves.easeOutQuad,
            ),
            
            const SizedBox(height: 16),
            
            Text(
              content,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white70,
                height: 1.5,
              ),
              textAlign: TextAlign.justify,
              textDirection: TextDirection.rtl,
            ).animate().fadeIn(
              delay: 200.ms,
              duration: 400.ms,
            ),
            
            if (hasContactButton) ...[
              const SizedBox(height: 16),
              Center(
                child: ElevatedButton.icon(
                  onPressed: () {
                    _launchEmail('<EMAIL>');
                  },
                  icon: const Icon(Icons.email),
                  label: const Text('تواصل معنا'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ).animate().fadeIn(
                delay: 400.ms,
                duration: 400.ms,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _launchEmail(String email) async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=استفسار حول سياسة الخصوصية',
    );
    
    try {
      await launchUrl(emailUri);
    } catch (e) {
      debugPrint('لا يمكن فتح تطبيق البريد الإلكتروني: $e');
    }
  }
}
