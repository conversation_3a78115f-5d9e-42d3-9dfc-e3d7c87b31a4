{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\AndroidStudioProjects\\Slmeh.Pro\\android\\app\\.cxx\\RelWithDebInfo\\l424v4w2\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["F:\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\AndroidStudioProjects\\Slmeh.Pro\\android\\app\\.cxx\\RelWithDebInfo\\l424v4w2\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}