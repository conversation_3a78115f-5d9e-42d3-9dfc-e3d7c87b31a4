import 'package:equatable/equatable.dart';

/// نموذج بيانات الاشتراك
class Subscription extends Equatable {
  final String id;
  final String name;
  final String description;
  final double price;
  final String currency;
  final int durationDays;
  final bool isActive;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? productId;
  final bool isRecommended;
  final List<String> features;
  final String? purchaseToken; // رمز الشراء من Google Play
  final bool allowsDownloads; // السماح بتنزيل الخلفيات
  final bool removesAds; // إزالة الإعلانات

  const Subscription({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    this.currency = 'USD',
    required this.durationDays,
    this.isActive = false,
    this.startDate,
    this.endDate,
    this.productId,
    this.isRecommended = false,
    this.features = const [],
    this.purchaseToken,
    this.allowsDownloads = true, // افتراضيًا، يسمح بالتنزيل
    this.removesAds = true, // افتراضيًا، يزيل الإعلانات
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'USD',
      durationDays: json['durationDays'] as int,
      isActive: json['isActive'] as bool? ?? false,
      startDate:
          json['startDate'] != null
              ? DateTime.parse(json['startDate'] as String)
              : null,
      endDate:
          json['endDate'] != null
              ? DateTime.parse(json['endDate'] as String)
              : null,
      productId: json['productId'] as String?,
      isRecommended: json['isRecommended'] as bool? ?? false,
      features:
          (json['features'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      purchaseToken: json['purchaseToken'] as String?,
      allowsDownloads: json['allowsDownloads'] as bool? ?? true,
      removesAds: json['removesAds'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'currency': currency,
      'durationDays': durationDays,
      'isActive': isActive,
      'startDate': startDate?.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'productId': productId,
      'isRecommended': isRecommended,
      'features': features,
      'purchaseToken': purchaseToken,
      'allowsDownloads': allowsDownloads,
      'removesAds': removesAds,
    };
  }

  Subscription copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    String? currency,
    int? durationDays,
    bool? isActive,
    DateTime? startDate,
    DateTime? endDate,
    String? productId,
    bool? isRecommended,
    List<String>? features,
    String? purchaseToken,
    bool? allowsDownloads,
    bool? removesAds,
  }) {
    return Subscription(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      durationDays: durationDays ?? this.durationDays,
      isActive: isActive ?? this.isActive,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      productId: productId ?? this.productId,
      isRecommended: isRecommended ?? this.isRecommended,
      features: features ?? this.features,
      purchaseToken: purchaseToken ?? this.purchaseToken,
      allowsDownloads: allowsDownloads ?? this.allowsDownloads,
      removesAds: removesAds ?? this.removesAds,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    price,
    currency,
    durationDays,
    isActive,
    startDate,
    endDate,
    productId,
    isRecommended,
    features,
    purchaseToken,
    allowsDownloads,
    removesAds,
  ];
}
