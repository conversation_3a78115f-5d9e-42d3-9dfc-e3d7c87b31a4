import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class CustomLoadingIndicator extends StatelessWidget {
  final double size;
  final Color color;
  final double strokeWidth;
  final String? message;

  const CustomLoadingIndicator({
    Key? key,
    this.size = 40.0,
    this.color = Colors.white,
    this.strokeWidth = 3.0,
    this.message,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(color),
            strokeWidth: strokeWidth,
          ),
        ).animate().shimmer(delay: 200.ms, duration: 1500.ms),
        if (message != null) ...[
          const SizedBox(height: 16),
          Text(
            message!,
            style: TextStyle(
              color: color,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

class CircularLoadingIndicator extends StatelessWidget {
  final double size;
  final Color color;
  final double strokeWidth;

  const CircularLoadingIndicator({
    Key? key,
    this.size = 24.0,
    this.color = Colors.white,
    this.strokeWidth = 2.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(color),
        strokeWidth: strokeWidth,
      ),
    );
  }
}

class GradientLoadingIndicator extends StatelessWidget {
  final double size;
  final List<Color> colors;
  final double strokeWidth;

  const GradientLoadingIndicator({
    Key? key,
    this.size = 40.0,
    this.colors = const [
      Color(0xFF4F46E5),
      Color(0xFF7C3AED),
      Color(0xEC4899),
    ],
    this.strokeWidth = 3.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(colors.first),
        strokeWidth: strokeWidth,
        backgroundColor: colors.last.withOpacity(0.2),
      ),
    );
  }
}
