import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:go_router/go_router.dart';
import 'package:glassmorphism/glassmorphism.dart';

import '../../data/models/wallpaper.dart';
import '../../data/services/wallpaper_service.dart';
import '../../design_system/colors.dart';
import '../../utils/number_formatter.dart';
import '../components/cards/wallpaper_card.dart';
import '../components/loaders/shimmer_loading.dart';

class CategoryScreen extends StatefulWidget {
  final String categoryId;
  final String categoryName;
  final Color? categoryColor;
  final String? categoryImageUrl;

  const CategoryScreen({
    super.key,
    required this.categoryId,
    required this.categoryName,
    this.categoryColor,
    this.categoryImageUrl,
  });

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen>
    with SingleTickerProviderStateMixin {
  final WallpaperService _wallpaperService = WallpaperService();
  List<Wallpaper> _wallpapers = [];
  bool _isLoading = true;
  String _error = '';
  late AnimationController _animationController;
  late Animation<double> _headerAnimation;
  final ScrollController _scrollController = ScrollController();
  bool _isHeaderCollapsed = false;

  @override
  void initState() {
    super.initState();

    // إعداد التحريك للرأس
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );

    _headerAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    // إضافة مستمع للتمرير
    _scrollController.addListener(_onScroll);

    // تأخير تحميل الخلفيات لتجنب مشكلة setState أثناء البناء
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadWallpapers();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final bool shouldCollapseHeader = _scrollController.offset > 180;
    if (shouldCollapseHeader != _isHeaderCollapsed) {
      setState(() {
        _isHeaderCollapsed = shouldCollapseHeader;
      });
      if (_isHeaderCollapsed) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    }
  }

  // بناء صورة الفئة (محلية أو من الإنترنت)
  Widget _buildCategoryImage(String imageUrl, Color fallbackColor) {
    // التحقق مما إذا كانت الصورة محلية
    if (imageUrl.startsWith('assets/')) {
      return Image.asset(imageUrl, fit: BoxFit.cover);
    } else {
      // صورة من الإنترنت
      return Image.network(
        imageUrl,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            color: fallbackColor.withAlpha(76), // 0.3 opacity
          );
        },
      );
    }
  }

  Future<void> _loadWallpapers() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
      _error = '';
    });

    try {
      final wallpapers = await _wallpaperService.fetchWallpapersByCategory(
        widget.categoryId,
      );

      if (!mounted) return;

      setState(() {
        _wallpapers = wallpapers;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _error = 'حدث خطأ أثناء تحميل الخلفيات';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final Color categoryColor = widget.categoryColor ?? AppColors.primary;
    final String categoryImageUrl = widget.categoryImageUrl ?? '';

    return Scaffold(
      body: Stack(
        children: [
          // خلفية الصفحة
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: AppColors.backgroundGradient,
              ),
            ),
          ),

          // محتوى الصفحة
          CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              // رأس الصفحة
              SliverAppBar(
                expandedHeight: 200,
                floating: false,
                pinned: true,
                backgroundColor: Colors.transparent,
                elevation: 0,
                flexibleSpace: LayoutBuilder(
                  builder: (context, constraints) {
                    final bool isCollapsed =
                        constraints.maxHeight <= kToolbarHeight + 30;
                    return FlexibleSpaceBar(
                      centerTitle: true,
                      title: AnimatedOpacity(
                        opacity: isCollapsed ? 1.0 : 0.0,
                        duration: const Duration(milliseconds: 300),
                        child: Text(
                          widget.categoryName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 18,
                            shadows: [
                              Shadow(
                                color: Colors.black54,
                                blurRadius: 3,
                                offset: Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                      background: Stack(
                        fit: StackFit.expand,
                        children: [
                          // صورة الفئة
                          if (categoryImageUrl.isNotEmpty)
                            ShaderMask(
                              shaderCallback: (rect) {
                                return LinearGradient(
                                  begin: Alignment.topCenter,
                                  end: Alignment.bottomCenter,
                                  colors: [
                                    categoryColor.withOpacity(0.8),
                                    categoryColor.withOpacity(0.5),
                                  ],
                                ).createShader(rect);
                              },
                              blendMode: BlendMode.srcATop,
                              child: _buildCategoryImage(
                                categoryImageUrl,
                                categoryColor,
                              ),
                            ),

                          // تدرج لتحسين قراءة النص
                          Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: [
                                  Colors.black.withAlpha(26), // 0.1 opacity
                                  Colors.black.withAlpha(128), // 0.5 opacity
                                ],
                              ),
                            ),
                          ),

                          // عنوان الفئة
                          Positioned(
                            bottom: 60,
                            left: 0,
                            right: 0,
                            child: Center(
                              child: FadeTransition(
                                opacity: _headerAnimation,
                                child: Column(
                                  children: [
                                    Text(
                                      widget.categoryName,
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 28,
                                        shadows: [
                                          Shadow(
                                            color: Colors.black54,
                                            blurRadius: 5,
                                          ),
                                        ],
                                      ),
                                    ).animate().fadeIn(
                                      duration: const Duration(
                                        milliseconds: 500,
                                      ),
                                    ),
                                    const SizedBox(height: 8),
                                    Container(
                                      height: 4,
                                      width: 40,
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(2),
                                      ),
                                    ).animate().fadeIn(
                                      delay: const Duration(milliseconds: 300),
                                      duration: const Duration(
                                        milliseconds: 500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
                leading: GlassmorphicContainer(
                  width: 40,
                  height: 40,
                  borderRadius: 40,
                  blur: 20,
                  alignment: Alignment.center,
                  border: 0,
                  linearGradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withAlpha(51), // 0.2 opacity
                      Colors.white.withAlpha(26), // 0.1 opacity
                    ],
                  ),
                  borderGradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white.withAlpha(51), // 0.2 opacity
                      Colors.white.withAlpha(26), // 0.1 opacity
                    ],
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.arrow_back, color: Colors.white),
                    onPressed: () => context.pop(),
                  ),
                ),
                actions: [
                  GlassmorphicContainer(
                    width: 40,
                    height: 40,
                    borderRadius: 40,
                    blur: 20,
                    alignment: Alignment.center,
                    border: 0,
                    margin: const EdgeInsets.only(right: 16),
                    linearGradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withAlpha(51), // 0.2 opacity
                        Colors.white.withAlpha(26), // 0.1 opacity
                      ],
                    ),
                    borderGradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withAlpha(51), // 0.2 opacity
                        Colors.white.withAlpha(26), // 0.1 opacity
                      ],
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.refresh, color: Colors.white),
                      onPressed: _loadWallpapers,
                    ),
                  ),
                ],
              ),

              // عدد الخلفيات
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: categoryColor.withAlpha(51), // 0.2 opacity
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: categoryColor.withAlpha(76), // 0.3 opacity
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.wallpaper_outlined,
                              size: 16,
                              color: categoryColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${_isLoading ? '...' : NumberFormatter.formatCompact(_wallpapers.length)} خلفية',
                              style: TextStyle(
                                color: categoryColor,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      if (!_isLoading && _wallpapers.isNotEmpty)
                        Text(
                          'اسحب للأسفل للتحديث',
                          style: TextStyle(
                            color: Colors.white.withAlpha(179), // 0.7 opacity
                            fontSize: 12,
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // محتوى الخلفيات
              _isLoading
                  ? SliverToBoxAdapter(child: _buildLoadingState())
                  : _error.isNotEmpty
                  ? SliverToBoxAdapter(child: _buildErrorState())
                  : _wallpapers.isEmpty
                  ? SliverToBoxAdapter(child: _buildEmptyState())
                  : _buildWallpapersGrid(),

              // مساحة إضافية في الأسفل
              const SliverToBoxAdapter(child: SizedBox(height: 100)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: MasonryGridView.count(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: 6,
          itemBuilder: (context, index) {
            return AnimationConfiguration.staggeredGrid(
              position: index,
              duration: const Duration(milliseconds: 500),
              columnCount: 2,
              child: SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: WallpaperCardShimmer(
                    height: index.isEven ? 240 : 200,
                    borderRadius: 16,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: AppColors.error, size: 64),
            const SizedBox(height: 16),
            Text(
              _error,
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadWallpapers,
              icon: const Icon(Icons.refresh),
              label: const Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.wallpaper,
              color: AppColors.textSecondary,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد خلفيات في فئة ${widget.categoryName}',
              style: const TextStyle(
                color: AppColors.textSecondary,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'سيتم إضافة خلفيات جديدة قريبًا',
              style: TextStyle(
                color: Colors.white.withAlpha(179), // 0.7 opacity
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => context.go('/home'),
              icon: const Icon(Icons.home),
              label: const Text('العودة للرئيسية'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  SliverPadding _buildWallpapersGrid() {
    return SliverPadding(
      padding: const EdgeInsets.all(16),
      sliver: SliverAnimatedGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 16,
          crossAxisSpacing: 16,
        ),
        initialItemCount: _wallpapers.length,
        itemBuilder: (context, index, animation) {
          final wallpaper = _wallpapers[index];
          return FadeTransition(
            opacity: animation,
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0, 0.5),
                end: Offset.zero,
              ).animate(animation),
              child: WallpaperCard(
                wallpaper: wallpaper,
                showInfo: false, // إزالة العناوين وإظهار الإحصائيات فقط
                borderRadius: 16,
              ),
            ),
          );
        },
      ),
    );
  }
}
