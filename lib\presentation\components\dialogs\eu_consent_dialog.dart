import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';
import '../../../services/geo_service.dart';

/// مربع حوار موافقة الاتحاد الأوروبي
class EuConsentDialog extends StatelessWidget {
  final VoidCallback? onAccept;
  final VoidCallback? onLearnMore;

  const EuConsentDialog({super.key, this.onAccept, this.onLearnMore});

  /// التحقق مما إذا كان يجب عرض مربع الحوار
  static Future<bool> shouldShow() async {
    final prefs = await SharedPreferences.getInstance();

    // التحقق مما إذا كان المستخدم قد وافق بالفعل
    final hasAccepted = prefs.getBool('eu_consent_accepted') ?? false;
    if (hasAccepted) {
      return false;
    }

    // التحقق مما إذا كان المستخدم في الاتحاد الأوروبي
    final isInEU = await GeoService.isInEuropeanUnion();
    return isInEU;
  }

  /// حفظ حالة الموافقة
  static Future<void> saveConsent() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('eu_consent_accepted', true);
  }

  /// عرض مربع حوار موافقة الاتحاد الأوروبي
  static Future<void> show(BuildContext context) async {
    if (await shouldShow()) {
      if (context.mounted) {
        showGeneralDialog(
          context: context,
          barrierDismissible: false,
          barrierColor: Colors.black.withOpacity(0.6),
          transitionDuration: const Duration(milliseconds: 300),
          pageBuilder: (context, animation1, animation2) {
            return EuConsentDialog(
              onAccept: () {
                saveConsent();
                Navigator.of(context).pop();
              },
              onLearnMore: () {
                Navigator.of(context).pushNamed('/privacy-policy');
              },
            );
          },
          transitionBuilder: (context, animation, secondaryAnimation, child) {
            return ScaleTransition(
              scale: Tween<double>(begin: 0.8, end: 1.0).animate(
                CurvedAnimation(parent: animation, curve: Curves.easeOutBack),
              ),
              child: FadeTransition(opacity: animation, child: child),
            );
          },
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final dialogWidth = screenSize.width * 0.9;

    return Center(
          child: Material(
            color: Colors.transparent,
            child: Container(
              width: dialogWidth,
              margin: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(
                      0xFF0A3170,
                    ), // لون أزرق داكن يشبه علم الاتحاد الأوروبي
                    Color(0xFF051C45), // لون أغمق للتدرج
                  ],
                ),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.4),
                    blurRadius: 20,
                    spreadRadius: 2,
                    offset: const Offset(0, 10),
                  ),
                ],
                border: Border.all(
                  color: Colors.white.withOpacity(0.15),
                  width: 1.5,
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // رأس مربع الحوار مع شعار الاتحاد الأوروبي
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.fromLTRB(20, 24, 20, 0),
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        // شعار الاتحاد الأوروبي المبسط (دائرة زرقاء مع نجوم)
                        Positioned(
                          right: 0,
                          child: Opacity(
                            opacity: 0.1,
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                color: Colors.blue.shade700,
                                shape: BoxShape.circle,
                              ),
                              child: Center(
                                child: Icon(
                                  Icons.star,
                                  color: Colors.yellow.shade600,
                                  size: 40,
                                ),
                              ),
                            ),
                          ),
                        ),

                        // عنوان مربع الحوار
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: const Icon(
                                Icons.privacy_tip_rounded,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 14),
                            Text(
                              'Privacy Notice',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 20,
                                letterSpacing: 0.5,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withOpacity(0.5),
                                    blurRadius: 3,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // خط فاصل مضيء
                  Container(
                    margin: const EdgeInsets.only(top: 20),
                    height: 1,
                    width: dialogWidth * 0.8,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withOpacity(0.01),
                          Colors.white.withOpacity(0.2),
                          Colors.white.withOpacity(0.01),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                  ),

                  // محتوى الرسالة
                  Padding(
                    padding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
                    child: Column(
                      children: [
                        // نص الرسالة
                        Container(
                          padding: const EdgeInsets.symmetric(
                            vertical: 18,
                            horizontal: 18,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.08),
                            borderRadius: BorderRadius.circular(18),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.15),
                              width: 1,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.2),
                                blurRadius: 10,
                                offset: const Offset(0, 5),
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: Text(
                            'In accordance with the EU General Data Protection Regulation (GDPR), we request your consent to use cookies and collect limited usage data to improve your experience and provide personalized content. You can learn more about how we use your data in our Privacy Policy.',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white.withOpacity(0.95),
                              fontSize: 14.5,
                              height: 1.6,
                              letterSpacing: 0.3,
                            ),
                          ),
                        ),
                        const SizedBox(height: 28),

                        // أزرار الإجراءات
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // زر معرفة المزيد
                            Expanded(
                              child: Container(
                                height: 48,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(14),
                                  border: Border.all(
                                    color: Colors.white.withOpacity(0.3),
                                    width: 1.5,
                                  ),
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.white.withOpacity(0.15),
                                      Colors.white.withOpacity(0.05),
                                    ],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.2),
                                      blurRadius: 8,
                                      offset: const Offset(0, 4),
                                    ),
                                  ],
                                ),
                                child: TextButton.icon(
                                  onPressed: onLearnMore,
                                  icon: Icon(
                                    Icons.info_outline,
                                    color: Colors.white,
                                    size: 18,
                                  ),
                                  label: const Text('Learn More'),
                                  style: TextButton.styleFrom(
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                    ),
                                    textStyle: const TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.w500,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),

                            // زر الموافقة
                            Expanded(
                              child: Container(
                                height: 48,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(14),
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.white,
                                      Colors.white.withOpacity(0.85),
                                    ],
                                    begin: Alignment.topCenter,
                                    end: Alignment.bottomCenter,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withOpacity(0.3),
                                      blurRadius: 10,
                                      offset: const Offset(0, 4),
                                      spreadRadius: 1,
                                    ),
                                  ],
                                ),
                                child: TextButton.icon(
                                  onPressed: onAccept,
                                  icon: Icon(
                                    Icons.check_circle_outline,
                                    color: const Color(0xFF0A3170),
                                    size: 20,
                                  ),
                                  label: const Text('Accept'),
                                  style: TextButton.styleFrom(
                                    foregroundColor: const Color(0xFF0A3170),
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                    ),
                                    textStyle: const TextStyle(
                                      fontSize: 15,
                                      fontWeight: FontWeight.bold,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
        .animate()
        .fadeIn(duration: const Duration(milliseconds: 400))
        .scale(
          begin: const Offset(0.9, 0.9),
          end: const Offset(1.0, 1.0),
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeOutBack,
        );
  }
}
