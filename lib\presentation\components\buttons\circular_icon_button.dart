import 'package:flutter/material.dart';
import '../../../design_system/colors.dart';
import '../../../design_system/dimensions.dart';
import '../../../design_system/shadows.dart';

/// زر أيقونة دائري
class CircularIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final double size;
  final Color? backgroundColor;
  final Color? iconColor;
  final List<BoxShadow>? boxShadow;
  final bool isLoading;

  const CircularIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.size = 48.0,
    this.backgroundColor,
    this.iconColor,
    this.boxShadow,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDisabled = onPressed == null || isLoading;
    final bgColor = backgroundColor ?? AppColors.surface;
    final icoColor = iconColor ?? AppColors.textPrimary;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: isDisabled ? bgColor.withOpacity(0.6) : bgColor,
        shape: BoxShape.circle,
        boxShadow: isDisabled ? null : (boxShadow ?? AppShadows.small),
      ),
      child: Material(
        color: Colors.transparent,
        shape: const CircleBorder(),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: isDisabled ? null : onPressed,
          splashColor: Colors.white24,
          highlightColor: Colors.white10,
          child: Center(
            child:
                isLoading
                    ? SizedBox(
                      width: size / 2,
                      height: size / 2,
                      child: CircularProgressIndicator(
                        strokeWidth: 2.0,
                        valueColor: AlwaysStoppedAnimation<Color>(icoColor),
                      ),
                    )
                    : Icon(
                      icon,
                      color: icoColor,
                      size:
                          size * 0.6, // Aumentamos el tamaño relativo del icono
                    ),
          ),
        ),
      ),
    );
  }
}
