import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import '../models/wallpaper.dart';
import '../../services/wallpaper_service.dart';

/// خدمة تنزيل وتطبيق الخلفيات
class DownloadService {
  /// تنزيل الخلفية وحفظها في الاستديو
  Future<String?> downloadWallpaper(
    Wallpaper wallpaper, {
    Function(double)? onProgress,
  }) async {
    try {
      // التحقق من الأذونات
      if (!await _checkPermissions()) {
        debugPrint('فشل تنزيل الخلفية: لم يتم منح الأذونات المطلوبة');
        return null;
      }

      // الحصول على مسار التخزين
      final directory = await _getStorageDirectory();
      if (directory == null) {
        debugPrint('فشل تنزيل الخلفية: لم يتم العثور على مسار التخزين');
        return null;
      }

      // إنشاء مجلد للخلفيات إذا لم يكن موجودًا
      final wallpaperDir = Directory('${directory.path}/Islamic_Wallpapers');
      if (!await wallpaperDir.exists()) {
        await wallpaperDir.create(recursive: true);
      }

      // اسم الملف مع استخدام عنوان الخلفية لتسهيل التعرف عليها
      final fileName =
          'islamic_wallpaper_${wallpaper.id}_${DateTime.now().millisecondsSinceEpoch}.jpg';
      final filePath = '${wallpaperDir.path}/$fileName';

      // إنشاء ملف جديد
      final file = File(filePath);

      // تنزيل الصورة
      final client = http.Client();
      final request = http.Request('GET', Uri.parse(wallpaper.imageUrl));
      final response = await client.send(request);

      if (response.statusCode != 200) {
        debugPrint('فشل تنزيل الخلفية: رمز الاستجابة ${response.statusCode}');
        return null;
      }

      // إجمالي حجم الملف
      final totalBytes = response.contentLength ?? 0;
      int receivedBytes = 0;

      // إنشاء ملف للكتابة
      final fileStream = file.openWrite();

      // تنزيل الملف مع تتبع التقدم
      await response.stream
          .listen(
            (chunk) {
              receivedBytes += chunk.length;
              fileStream.add(chunk);
              if (totalBytes > 0 && onProgress != null) {
                final progress = receivedBytes / totalBytes;
                onProgress(progress);
              }
            },
            onDone: () async {
              await fileStream.flush();
              await fileStream.close();
              if (onProgress != null) onProgress(1.0);
            },
            onError: (error) {
              debugPrint('خطأ في تنزيل الخلفية: $error');
              fileStream.close();
            },
            cancelOnError: true,
          )
          .asFuture();

      // التأكد من أن الملف تم إنشاؤه بنجاح
      if (await file.exists()) {
        debugPrint('تم تنزيل الخلفية بنجاح: $filePath');

        // استخدام طريقة أكثر موثوقية لحفظ الصورة في الاستديو
        try {
          // استخدام MediaStore API عبر قناة منصة مخصصة
          final result = await _saveImageToGallery(file.path, wallpaper.title);

          if (result) {
            debugPrint('تم حفظ الخلفية في الاستديو بنجاح');
          } else {
            // محاولة بديلة باستخدام النسخ المباشر
            try {
              // الحصول على مسار مجلد الصور العام (DCIM)
              final dcimDir = Directory(
                '/storage/emulated/0/DCIM/Islamic_Wallpapers',
              );
              if (!await dcimDir.exists()) {
                await dcimDir.create(recursive: true);
              }

              // نسخ الملف إلى مجلد الصور
              final dcimFilePath = '${dcimDir.path}/${path.basename(filePath)}';
              await file.copy(dcimFilePath);

              // إعلام معرض الصور بالملف الجديد
              await _scanFile(dcimFilePath);

              debugPrint(
                'تم حفظ الخلفية في الاستديو بنجاح (الطريقة البديلة): $dcimFilePath',
              );
            } catch (e2) {
              debugPrint('فشل حفظ الخلفية في الاستديو بالطريقة البديلة: $e2');
            }
          }
        } catch (e) {
          debugPrint('فشل حفظ الخلفية في الاستديو، ولكن تم تنزيلها بنجاح: $e');
        }

        return filePath;
      } else {
        debugPrint('فشل تنزيل الخلفية: لم يتم إنشاء الملف');
        return null;
      }
    } catch (e) {
      debugPrint('خطأ في تنزيل الخلفية: $e');
      return null;
    }
  }

  /// تطبيق الخلفية
  Future<bool> applyWallpaper(
    Wallpaper wallpaper, {
    required String type,
  }) async {
    try {
      // تحويل النوع إلى WallpaperType
      WallpaperType wallpaperType;
      switch (type) {
        case 'home':
          wallpaperType = WallpaperType.home;
          break;
        case 'lock':
          wallpaperType = WallpaperType.lock;
          break;
        case 'both':
          wallpaperType = WallpaperType.both;
          break;
        default:
          wallpaperType = WallpaperType.both;
      }

      // استخدام WallpaperService لتطبيق الخلفية
      final wallpaperService = WallpaperService();
      final success = await wallpaperService.setWallpaper(
        wallpaper.imageUrl,
        wallpaperType,
      );

      return success;
    } catch (e) {
      debugPrint('خطأ في تطبيق الخلفية: $e');
      return false;
    }
  }

  /// التحقق من الأذونات المطلوبة
  Future<bool> _checkPermissions() async {
    if (Platform.isAndroid) {
      try {
        // التحقق من أذونات الوصول للتخزين
        final storageStatus = await Permission.storage.status;
        final photosStatus = await Permission.photos.status;
        final mediaStatus = await Permission.mediaLibrary.status;

        // إذا كانت أي من الأذونات مرفوضة، نطلب جميع الأذونات المطلوبة
        if (storageStatus.isDenied ||
            photosStatus.isDenied ||
            mediaStatus.isDenied) {
          // طلب الأذونات
          await Permission.storage.request();
          await Permission.photos.request();
          await Permission.mediaLibrary.request();

          // التحقق مرة أخرى بعد طلب الأذونات
          final newStorageStatus = await Permission.storage.status;
          final newPhotosStatus = await Permission.photos.status;
          final newMediaStatus = await Permission.mediaLibrary.status;

          // يجب أن تكون جميع الأذونات ممنوحة
          return newStorageStatus.isGranted ||
              newPhotosStatus.isGranted ||
              newMediaStatus.isGranted;
        }

        // إذا كانت جميع الأذونات ممنوحة بالفعل
        return storageStatus.isGranted ||
            photosStatus.isGranted ||
            mediaStatus.isGranted;
      } catch (e) {
        debugPrint('خطأ في التحقق من الأذونات: $e');
        // في حالة حدوث خطأ، نحاول طلب إذن التخزين فقط
        final result = await Permission.storage.request();
        return result.isGranted;
      }
    } else {
      // لأنظمة التشغيل الأخرى
      return true;
    }
  }

  /// الحصول على مسار التخزين
  Future<Directory?> _getStorageDirectory() async {
    try {
      // أولاً نحاول الحصول على مسار التخزين الخارجي
      return await getExternalStorageDirectory();
    } catch (e) {
      debugPrint('فشل الحصول على مسار التخزين الخارجي: $e');
      try {
        // إذا فشلت المحاولة الأولى، نحاول الحصول على مسار التخزين المؤقت
        return await getTemporaryDirectory();
      } catch (e) {
        debugPrint('فشل الحصول على مسار التخزين المؤقت: $e');
        try {
          // إذا فشلت جميع المحاولات، نحاول الحصول على مسار التخزين الداخلي
          return await getApplicationDocumentsDirectory();
        } catch (e) {
          debugPrint('فشل الحصول على مسار التخزين الداخلي: $e');
          return null;
        }
      }
    }
  }

  /// حفظ الصورة في معرض الصور باستخدام قناة منصة مخصصة
  Future<bool> _saveImageToGallery(String imagePath, String title) async {
    try {
      // إنشاء قناة منصة مخصصة
      const platform = MethodChannel(
        'com.islamiclandporta.islam.allahwallpaper.ahmad.np/gallery',
      );

      // قراءة الملف كبايتات
      final File file = File(imagePath);
      final Uint8List bytes = await file.readAsBytes();

      // استدعاء الدالة الأصلية
      final result = await platform.invokeMethod<bool>('saveImageToGallery', {
        'imageBytes': bytes,
        'title': title,
        'albumName': 'Islamic Wallpapers',
      });

      return result ?? false;
    } catch (e) {
      debugPrint('خطأ في حفظ الصورة في المعرض: $e');
      return false;
    }
  }

  /// مسح الملف لإضافته إلى معرض الصور
  Future<void> _scanFile(String filePath) async {
    try {
      const platform = MethodChannel(
        'com.islamiclandporta.islam.allahwallpaper.ahmad.np/media_scanner',
      );
      await platform.invokeMethod<void>('scanFile', {'filePath': filePath});
    } catch (e) {
      debugPrint('خطأ في مسح الملف: $e');
    }
  }
}
