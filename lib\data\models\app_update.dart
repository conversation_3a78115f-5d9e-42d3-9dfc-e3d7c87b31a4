import 'package:equatable/equatable.dart';

/// نموذج معلومات تحديث التطبيق
class AppUpdate extends Equatable {
  /// رقم الإصدار (مثل 6.0.0)
  final String version;

  /// رقم البناء (مثل 44)
  final int buildNumber;

  /// ما إذا كان التحديث إجبارياً
  final bool isForced;

  /// رابط التحديث على متجر Google Play
  final String playStoreUrl;

  /// عنوان التحديث
  final String title;

  /// وصف التحديث
  final String description;

  /// قائمة التغييرات في التحديث
  final List<String> changes;

  const AppUpdate({
    required this.version,
    required this.buildNumber,
    required this.isForced,
    required this.playStoreUrl,
    required this.title,
    required this.description,
    required this.changes,
  });

  /// إنشاء نموذج من بيانات JSON
  factory AppUpdate.fromJson(Map<String, dynamic> json) {
    return AppUpdate(
      version: json['version'] ?? '',
      buildNumber: json['buildNumber'] ?? 0,
      isForced: json['isForced'] ?? false,
      playStoreUrl:
          json['playStoreUrl'] ??
          'https://play.google.com/store/apps/details?id=com.islamiclandporta.islam.allahwallpaper.ahmad.np',
      title: json['title'] ?? 'تحديث جديد',
      description: json['description'] ?? 'يتوفر تحديث جديد للتطبيق',
      changes: List<String>.from(json['changes'] ?? []),
    );
  }

  /// تحويل النموذج إلى بيانات JSON
  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'buildNumber': buildNumber,
      'isForced': isForced,
      'playStoreUrl': playStoreUrl,
      'title': title,
      'description': description,
      'changes': changes,
    };
  }

  @override
  List<Object?> get props => [
    version,
    buildNumber,
    isForced,
    playStoreUrl,
    title,
    description,
    changes,
  ];
}
