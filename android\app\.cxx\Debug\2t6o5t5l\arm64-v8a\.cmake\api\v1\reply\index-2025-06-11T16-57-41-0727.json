{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/Users/<USER>/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/Users/<USER>/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/Users/<USER>/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/Users/<USER>/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-3ba9674779e173d860c1.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-9379c2db8a23c172bbf7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-b5b2852291f3adeb072e.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-9379c2db8a23c172bbf7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-b5b2852291f3adeb072e.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-3ba9674779e173d860c1.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}