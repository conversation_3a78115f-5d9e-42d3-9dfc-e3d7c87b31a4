import '../../design_system/colors.dart';
import '../models/wallpaper.dart';

class CategoryService {
  // جلب جميع الفئات
  Future<List<Category>> fetchCategories() async {
    try {
      // استخدام بيانات وهمية
      return _getMockCategories();
    } catch (e) {
      // في حالة حدوث خطأ، نعيد قائمة فارغة
      return [];
    }
  }

  // الحصول على فئات متطابقة مع العناوين المطلوبة
  List<Category> _getMockCategories() {
    return [
      const Category(
        id: 'مسجد',
        name: 'مسجد',
        imageUrl: 'assets/images/categories/mosque.jpg',
        description: 'خلفيات للمساجد الإسلامية الجميلة',
        wallpaperCount: 24,
        color: AppColors.categoryMosque,
        blurHash: 'LRF5oZxu4nof~qj[M{ay-;ayWBay',
      ),
      const Category(
        id: 'دعاء',
        name: 'دعاء',
        imageUrl: 'assets/images/categories/dua.jpg',
        description: 'خلفيات أدعية إسلامية',
        wallpaperCount: 20,
        color: AppColors.categoryCalligraphy,
        blurHash: 'L9B:?j00~qxu4nM{IUt7-;t7M{ay',
      ),
      const Category(
        id: 'القران',
        name: 'القران',
        imageUrl: 'assets/images/categories/quran.jpg',
        description: 'خلفيات آيات قرآنية',
        wallpaperCount: 18,
        color: AppColors.categoryQuran,
        blurHash: 'L6By+j00~qxu4nM{IUt7-;t7M{ay',
      ),
      const Category(
        id: 'ذكر الله',
        name: 'ذكر الله',
        imageUrl: 'assets/images/categories/dhikr.jpg',
        description: 'خلفيات أذكار إسلامية',
        wallpaperCount: 16,
        color: AppColors.categoryPattern,
        blurHash: 'L9Bx?j00~qxu4nM{IUt7-;t7M{ay',
      ),
      const Category(
        id: 'اسم الله الحسنى',
        name: 'اسم الله الحسنى',
        imageUrl: 'assets/images/categories/names.webp',
        description: 'خلفيات أسماء الله الحسنى',
        wallpaperCount: 15,
        color: AppColors.categoryAllah,
        blurHash: 'L9Bx?j00~qxu4nM{IUt7-;t7M{ay',
      ),
      const Category(
        id: 'متنوعه',
        name: 'متنوعه',
        imageUrl: 'assets/images/categories/misc.jpg',
        description: 'خلفيات إسلامية متنوعة',
        wallpaperCount: 12,
        color: AppColors.categoryKaaba,
        blurHash: 'L9Bx?j00~qxu4nM{IUt7-;t7M{ay',
      ),
    ];
  }
}
